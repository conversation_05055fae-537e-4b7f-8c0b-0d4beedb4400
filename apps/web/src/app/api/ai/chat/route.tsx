import assert from 'assert';
import type { Message as AISDKMessage } from '@ai-sdk/ui-utils';
import { errors, ServerError } from '@bika/contents/config/server/error';
import type { Locale } from '@bika/contents/i18n';
import { AIChatSO } from '@bika/domains/ai/server';
import {
  createResumableDataStream,
  getResumableStream,
  getResumableStreamId,
  streamResponse,
} from '@bika/domains/ai/server/stream-context';
import { AuthController } from '@bika/domains/auth/apis';
import { createFetchRequestContext } from '@bika/server-orm/trpc';
import { AIResolveUIVOSchema, type AIResolveVO } from '@bika/types/ai/vo';
import { createDataStream, ToolExecutionError } from 'ai';
import { headers } from 'next/headers';

// Allow streaming responses up to 300 seconds
export const maxDuration = 300;

export async function POST(req: Request) {
  const { id: chatId, lastMessage, forceLocale, option: userOption, contexts = [] } = await req.json();

  assert(chatId, 'chatId is required');

  console.log('/api/ai/chat chat contexts', contexts);

  const auth = await AuthController.getAuthByNextRequest(await headers());
  assert(auth);

  const wizardSO = await AIChatSO.get(chatId);

  // 获取请求的用户
  const locale: Locale = forceLocale || auth.user.settings?.locale || 'en';

  const ctx = await createFetchRequestContext({ req, resHeaders: new Headers() });

  // const chatMessages = messages as ChatMessages;
  // const lastMessage = chatMessages[chatMessages.length - 1];
  const lastUIMessage = lastMessage as AISDKMessage;

  // if (lastMessage.role !== 'user') {
  //   // 存在可能，add ToolResult，last Message 不是 user message 所以
  //   console.error(chatMessages);
  //   console.error(`Last message should be user message, but now messages count: ${chatMessages.length}`);
  //   return createDataStreamResponse({
  //     headers: {
  //       'Content-Type': 'text/event-stream',
  //     },
  //     execute: async (_dataStream) => {},
  //   });
  // }

  // 构造 AI Resolve DTO，为了兼容旧的 wizard 模式 + AI SDK
  let resolve: AIResolveVO;
  // 解析 messageContent resolve （ui或 message），判断开头 为"/resolve:"，即理解 为调用 intent-ui tools
  const messageContent = lastMessage.content as string;
  if (messageContent.startsWith('/resolve:')) {
    // UI Resolve
    // 移除 /resolve: 开头，剩下的部分 json 进行反解释
    const jsonStr = messageContent.slice(9);
    const resolveObj = AIResolveUIVOSchema.parse(JSON.parse(jsonStr) as AIResolveVO);
    resolveObj.message = messageContent; // 重新塞回去
    resolve = resolveObj;
  } else if (lastMessage.role === 'assistant') {
    // 理解为 tool result,这个是客户端执行的tool, 需要先放到消息历史中
    const lastMessageToolsInvokes = lastUIMessage.toolInvocations;
    const lastToolInvoke = lastMessageToolsInvokes?.[lastMessageToolsInvokes!.length - 1];
    assert(lastToolInvoke!.state === 'result', 'Tool result should be in result state');

    resolve = {
      type: 'TOOL',
      toolInvocation: lastToolInvoke!,
      // message: lastToolInvoke!.result as string,
      // message: lastMessage, // ui message pass
      option: userOption,
    };
  } else {
    resolve = {
      type: 'MESSAGE',
      message: lastMessage.content as string,
      option: userOption,
      contexts,
    };
  }

  const stream = await createResumableDataStream({
    chatId,
    userId: auth.user.id,
    execute: async (dataStream) => {
      const _resolveGenerator = await wizardSO.resolve(ctx, resolve, locale, dataStream);
    },
    onError: (error) => {
      console.error('createDataStreamResponseError', error);
      // console.trace();
      if (error instanceof ServerError && error.code === errors.billing.ai_credit_not_enough.code) {
        return JSON.stringify(error);
      }

      if (ToolExecutionError.isInstance(error)) {
        // tool execution error
        return JSON.stringify({
          name: 'AI_ToolExecutionError',
          message: error.message,
          toolCallId: error.toolCallId,
          errors: {
            [error.toolCallId]: error.message,
          },
        });
      }
      return error instanceof Error ? error.message : String(error);
    },
    // Error messages are masked by default for security reasons.
    // If you want to expose the error message to the client, you can do so here:
  });
  // return stream, bind streamId to current chat stream
  return streamResponse(stream);
}

export async function GET(request: Request) {
  const resumeRequestedAt = new Date();

  const { searchParams } = new URL(request.url);
  const chatId = searchParams.get('chatId');

  if (!chatId) {
    return new ServerError(errors.ai.chat_not_found);
  }

  const auth = await AuthController.getAuthByNextRequest(await headers());
  assert(auth);

  const chat = await AIChatSO.get(chatId);
  const recentStreamId = await getResumableStreamId(chatId, auth.user.id);

  const emptyDataStream = createDataStream({
    execute: () => {},
  });

  // 缓存已经过期了
  if (!recentStreamId) {
    return streamResponse(emptyDataStream);
  }

  const stream = await getResumableStream(recentStreamId);

  /*
   * For when the generation is streaming during SSR
   * but the resumable stream has concluded at this point.
   */
  if (!stream) {
    const messages = await chat.getMessagesPOs();
    const mostRecentMessage = messages.at(-1);

    if (!mostRecentMessage) {
      return streamResponse(emptyDataStream);
    }
    if (mostRecentMessage.message.role !== 'assistant') {
      return streamResponse(emptyDataStream);
    }

    const messageCreatedAt = new Date(mostRecentMessage.createdAt);

    if (messageCreatedAt.getTime() - resumeRequestedAt.getTime() > 15 * 1000) {
      return streamResponse(emptyDataStream);
    }

    const restoredStream = createDataStream({
      execute: (buffer) => {
        buffer.writeData({
          type: 'append-message',
          message: JSON.stringify(mostRecentMessage),
        });
      },
    });
    return streamResponse(restoredStream);
  }

  return streamResponse(stream);
}

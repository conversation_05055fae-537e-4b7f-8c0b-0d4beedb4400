'use client';

import { useTR<PERSON>Query } from '@bika/api-caller/context';
import { errors } from '@bika/contents/config/server/error/errors';
import { useLocale } from '@bika/contents/i18n/context';
import { AuthUsernameTabView, AuthView } from '@bika/domains/auth/client';
import { useGlobalContext } from '@bika/types/website/context';
import { Button, TextButton } from '@bika/ui/button';
import { AvatarImg, AvatarSize } from '@bika/ui/components/avatar/index';
import { StandalonePage } from '@bika/ui/components/standalone/index';
import { Checkbox } from '@bika/ui/forms';
import { useUIFrameworkContext } from '@bika/ui/framework/context';
import { Box, Stack } from '@bika/ui/layouts';
import { snackbarShow } from '@bika/ui/snackbar';
import { Typography } from '@bika/ui/texts';
import { List, ListItem } from '@mui/joy';
import Link from '@mui/joy/Link';
import Image from 'next/image';
import { useParams, useSearchParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

export default function SpaceJoinPage() {
  const { t } = useLocale();
  const params = useParams<{ inviteId: string }>();
  const searchParams = useSearchParams();
  const router = useRouter();
  const referralCode = searchParams.get('referralCode');
  const { Image: UIFrameworkImage } = useUIFrameworkContext();

  const { lang } = useLocale();
  const inviteToken = params.inviteId;
  const ctx = useGlobalContext();
  const trpcQuery = useTRPCQuery();
  const [inviteEmailVerify, setInviteEmailVerify] = useState(true);
  // const [reachLimit, setReachLimit] = useState<UsageExceedLimitErrorData | null>(null);
  const [limitError, setLimitError] = useState<string | null>(null);
  const [enterAuth, setEnterAuth] = useState(false);
  // 是否同意注册协议
  const isShowAgree = ctx.isFromCNHost && !ctx.authContext.me;
  const [agree, setAgree] = useState(!isShowAgree);
  const [allowEmailDomains, setAllowEmailDomains] = useState<string[]>();
  const { data: spaceInfo } = trpcQuery.linkInvitation.detail.useQuery({ inviteToken });
  const { data: spaceList } = trpcQuery.space.list.useQuery({});
  const joinSpace = trpcQuery.linkInvitation.accept.useMutation();

  useEffect(() => {
    if (spaceList?.length && spaceInfo) {
      const redirectSpace = spaceList.find((space) => space.id === spaceInfo.spaceId);
      if (redirectSpace) {
        router.push(`/space/${redirectSpace?.id}`);
      }
    }
  }, [spaceInfo, router, spaceList]);

  const joinSpaceHandler = (spaceId: string) => {
    joinSpace.mutate(
      { inviteToken },
      {
        onSuccess: () => {
          // 不要改push 有些问题
          window.location.href = `/space/${spaceId}`;
        },
        onError: (err) => {
          if (err?.data?.code === errors.billing.usage_exceed_limit.code) {
            // 超限
            // const errorData = transformUsageLimitErrorData(err?.data?.data);
            // setReachLimit(errorData);
            setLimitError(err.data.message);
          }

          if (err?.data?.data && 'allowEmailDomains' in err.data.data) {
            // 邮件授权域不符合
            setInviteEmailVerify(false);
            setAllowEmailDomains(err?.data?.data?.allowEmailDomains as string[]);
          }

          snackbarShow({
            content: err?.data?.message || 'error',
            color: 'danger',
          });

          // 已在空间中, 跳转到空间
          if (err?.data?.code === errors.user.has_exist_in_space.code) {
            window.location.href = `/space/${spaceId}`;
          } else {
            setEnterAuth(false);
          }
        },
      },
    );
  };

  const renderAgree = () => {
    const text = (
      <Typography level="b4">
        {t('auth.register_agreement', {
          team: (
            <Link href={`/${lang}/terms-of-service`} target="_blank">
              {t.auth.terms_of_service}
            </Link>
          ),
          privacy: (
            <Link href={`/${lang}/privacy`} target="_blank">
              {t.auth.privacy_policy}
            </Link>
          ),
        })}
      </Typography>
    );
    if (isShowAgree) {
      return (
        <Stack direction="row" alignItems="center">
          <Checkbox checked={agree} onChange={(e) => setAgree(e.target.checked)} />
          <Stack px={2} ml={1}>
            {text}
          </Stack>
        </Stack>
      );
    }
    return text;
  };

  if (!spaceInfo) {
    return null;
  }

  const renderView = () => {
    if (limitError !== null) {
      return (
        <>
          <div className=" w-[180px] h-[180px] overflow-hidden border rounded-[10px] !mt-[54px]">
            <UIFrameworkImage
              src="/assets/placeholders/join-space.png"
              width={188}
              height={188}
              alt="join-space placeholder"
            />
          </div>

          <div className="text-h6 text-[--text-primary]">{limitError}</div>

          <div className="w-full flex justify-center">
            <Button
              onClick={() => {
                router.push('/space');
              }}
              sx={{
                height: '42px',
                width: '100%',
              }}
            >
              {t.buttons.back_to_space}
            </Button>
          </div>
        </>
      );
    }
    if (enterAuth) {
      if (ctx.isFromCNHost) {
        console.log(`ctx.isFromCNHost: ${ctx.isFromCNHost}`);
        return (
          <>
            <Box sx={{ marginTop: 4, marginBottom: 6 }}>
              <Typography level="h1">{t.auth.login_and_register}</Typography>
            </Box>
            <AuthUsernameTabView
              ignoreAutoCreateSpace
              onLogin={async () => {
                joinSpaceHandler(spaceInfo.spaceId);
                ctx.showUIModal(null);
              }}
              onCancel={() => {
                setEnterAuth(false);
              }}
              setQuickLoginCode={() => {}}
              tabType="USERNAME_EMAIL"
              referralCode={referralCode || undefined}
              agree={{
                checked: agree,
                setAgree,
                ui: renderAgree(),
              }}
            />
          </>
        );
      }
      return (
        <>
          <Box sx={{ marginTop: 4, marginBottom: 6 }}>
            <Typography level="h1">{t.auth.login_and_register}</Typography>
          </Box>
          <AuthView
            ignoreQuickLogin
            ignoreAutoCreateSpace
            ignoreWeixinLogin
            openWindow
            onLogin={async () => {
              joinSpaceHandler(spaceInfo.spaceId);
              ctx.showUIModal(null);
            }}
            referralCode={referralCode || undefined}
          />
        </>
      );
    }
    // 验证用户的邮箱是否符合预设的规则
    if (!inviteEmailVerify) {
      return (
        <>
          <Image src="/assets/placeholders/join-space.png" width={160} height={160} alt="join-space placeholder" />
          <Typography level="h7" mt={2} sx={{ marginBottom: '20px ', color: 'var(--text-primary)' }}>
            {t.auth.no_permission}
          </Typography>
          <List
            marker="disc"
            sx={{
              width: '100%',
              overflow: 'auto',
              borderRadius: '8px',
              flex: 1,
              bgcolor: 'var(--bg-surface)',
              mb: '24px',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
            }}
          >
            <div>
              {allowEmailDomains?.map((item, index) => (
                <ListItem key={index}>
                  <Typography level="b2" textColor={'var(--text-secondary)'}>
                    {item}
                  </Typography>
                </ListItem>
              ))}
            </div>
          </List>
          <Button
            onClick={async () => {
              await ctx.authContext.logout(window.location.href);
            }}
            sx={{
              borderRadius: '4px',
              height: '42px',
              width: '100%',
              bgcolor: 'var(--brand)',
            }}
          >
            <Typography level="b2" textColor={'var(--static)'}>
              {t.auth.switch_account}
            </Typography>
          </Button>

          <TextButton
            variant="plain"
            onClick={() => {
              ctx.showUIModal({ name: 'USER_EMAIL_SETTING' });
            }}
            sx={{
              borderRadius: '4px',
              height: '42px',
              width: '100%',
              color: 'var(--brand)',
            }}
          >
            <Typography level="b2">{t.buttons.change_bound_email}</Typography>
          </TextButton>

          <TextButton
            variant="plain"
            onClick={() => {
              router.push('/space');
            }}
            sx={{
              borderRadius: '4px',
              height: '42px',
              width: '100%',
              color: 'var(--brand)',
            }}
          >
            <Typography level="b2">{spaceList?.length ? t.buttons.back_to_space : t.buttons.back_to_space}</Typography>
          </TextButton>
        </>
      );
    }
    return (
      <>
        <div className=" w-[80px] h-[80px] overflow-hidden border rounded-[10px] !mt-[54px]">
          <AvatarImg
            alt="space icon"
            avatar={spaceInfo.spaceLogo}
            name={spaceInfo?.spaceName || ''}
            shape="SQUARE"
            customSize={AvatarSize.Size80}
          />
        </div>
        <Typography level="h6" sx={{ marginBottom: '84px', textAlign: 'center' }}>
          {spaceInfo?.inviterName} {t.auth.invite_you_to_join}「{spaceInfo?.spaceName}」
        </Typography>
        <Button
          loading={joinSpace.isLoading}
          onClick={() => {
            if (!ctx.authContext.me) {
              setEnterAuth(true);
            } else {
              joinSpaceHandler(spaceInfo.spaceId);
            }
          }}
          sx={{
            borderRadius: '30px',
            height: '48px',
            width: '100%',
          }}
        >
          <Typography level="b2">{t.settings.space.join}</Typography>
        </Button>
      </>
    );
  };

  return (
    <StandalonePage>
      <Stack
        direction="column"
        p="32px 60px"
        alignItems="center"
        bgcolor={'var(--bg-popup)'}
        spacing={inviteEmailVerify && !enterAuth ? 6 : 3}
      >
        {renderView()}
      </Stack>
    </StandalonePage>
  );
}

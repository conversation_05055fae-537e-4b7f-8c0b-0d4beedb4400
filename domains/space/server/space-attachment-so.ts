import path from 'path';
import _ from 'lodash';
import { errors } from '@bika/contents/config/server/error/errors';
import { ServerError } from '@bika/contents/config/server/error/server_error';
import { AttachmentSO } from '@bika/domains/attachment/server/attachment-so';
import { AttachmentModel } from '@bika/domains/attachment/server/types';
import { DatabaseSO } from '@bika/domains/database/server/database-so';
import { DatabaseFieldModel } from '@bika/domains/database/server/fields/types';
import { RecordSO } from '@bika/domains/database/server/record-so';
import { RecordCellModelMap } from '@bika/domains/database/server/types';
import { isArrayOfType } from '@bika/domains/shared/shared';
import { UserSO } from '@bika/domains/user/server/user-so';
import {
  DatabaseRecordModel,
  db,
  mongoose,
  MongoTransactionCB,
  PrismaPromise,
  SpaceAttachmentModel,
} from '@bika/server-orm';
import { AIGenerateImageProps, AIImageBO } from '@bika/types/ai/bo';
import { AttachmentReference } from '@bika/types/attachment/bo';
import { AttachmentVO } from '@bika/types/attachment/vo';
import { AttachmentCellData, AttachmentCellDataSchema, CellValue } from '@bika/types/database/bo';
import { AvatarLogo } from '@bika/types/system';
import { SpaceSO } from './space-so';

const { uniq } = _;

type QueryCondition = {
  spaceId: string;
  attachmentId?: string;
  attachmentRef?: AttachmentReference;
};

export class SpaceAttachmentSO {
  private readonly _model: SpaceAttachmentModel;

  constructor(model: SpaceAttachmentModel) {
    this._model = model;
  }

  get model() {
    return this._model;
  }

  private static buildQuery(condition: QueryCondition) {
    const { spaceId, attachmentId, attachmentRef } = condition;
    let filter: mongoose.FilterQuery<SpaceAttachmentModel> = { spaceId };
    if (attachmentId) {
      filter = { ...filter, attachmentId };
    }
    if (attachmentRef) {
      filter = { ...filter, 'attachmentRef.type': attachmentRef.type };
      if ('id' in attachmentRef) {
        filter = { ...filter, 'attachmentRef.id': attachmentRef.id };
      }
      if ('fieldId' in attachmentRef) {
        filter = { ...filter, 'attachmentRef.fieldId': attachmentRef.fieldId };
      }
      if ('recordId' in attachmentRef) {
        filter = { ...filter, 'attachmentRef.recordId': attachmentRef.recordId };
      }
    }
    return filter;
  }

  static async find(condition: QueryCondition): Promise<{ list: SpaceAttachmentSO[] }> {
    const filter = this.buildQuery(condition);
    const list = await db.mongo.spaceAttachment.find(filter);
    return { list: list.map((model) => new SpaceAttachmentSO(model)) };
  }

  static async findOne(condition: QueryCondition): Promise<SpaceAttachmentSO | null> {
    const filter = this.buildQuery(condition);
    const model = await db.mongo.spaceAttachment.findOne(filter);
    return model ? new SpaceAttachmentSO(model) : null;
  }

  static async createSession(
    userId: string,
    space: SpaceSO,
    attachment: {
      id: string;
      size: number;
    },
    reference: AttachmentReference,
    checkStorageUsage: boolean = true,
  ): Promise<MongoTransactionCB> {
    if (checkStorageUsage) {
      // 校验容量
      await space.checkStorageUsage(attachment.size);
    }
    return async (session) => {
      await db.mongo.spaceAttachment.create(
        [
          {
            spaceId: space.id,
            attachmentId: attachment.id,
            size: attachment.size,
            attachmentRef: reference,
            createdBy: userId,
            updatedBy: userId,
          },
        ],
        { session },
      );
    };
  }

  static async deleteSession(userId: string, condition: QueryCondition): Promise<MongoTransactionCB> {
    const filter = this.buildQuery(condition);
    return async (session) => {
      await db.mongo.spaceAttachment.updateMany(
        filter,
        {
          $set: {
            status: 'DELETED',
            updatedBy: userId,
          },
        },
        { session },
      );
    };
  }

  /**
   * 只删除单条记录
   * 对于文档节点有多个重复附件引用时候有效果
   */
  static async deleteOneSession(condition: QueryCondition): Promise<MongoTransactionCB> {
    const filter = this.buildQuery(condition);
    return async (session) => {
      await db.mongo.spaceAttachment.deleteOne(filter, { session });
    };
  }

  /**
   * 构建带附件的创建记录会话
   * @returns mongo session
   */
  static async buildCreateRecordAttachmentSession(
    user: UserSO | null,
    database: DatabaseSO,
    records: DatabaseRecordModel[],
  ): Promise<{
    operations: PrismaPromise<AttachmentModel>[];
    mongoSession: MongoTransactionCB;
  }> {
    // 这里使用database.fieldModels去获取字段数据而不是字段对象, 因为外面传入的对象可能已经被改了
    const fields = database.fieldModels.filter((field) => field.type === 'ATTACHMENT');
    const attachmentRefCountMap: Record<string, number> = {};
    const references: SpaceAttachmentModel[] = [];
    // 附件字段追加的附件总容量
    let incrementalRefSizes = 0;
    // 遍历附件字段
    for (const field of fields) {
      const cellEntries: [string, AttachmentCellData[]][] = records.map((record) => {
        if (record.data[field.id]) {
          return [record.id, record.data[field.id] as AttachmentCellData[]];
        }
        return [record.id, []];
      });
      for (const [recordId, cellData] of cellEntries) {
        if (cellData) {
          for (const cell of cellData) {
            // 叠加附件引用计数记录
            attachmentRefCountMap[cell.id] = cell.id in attachmentRefCountMap ? attachmentRefCountMap[cell.id] + 1 : 1;
            // 附件引用记录
            references.push({
              spaceId: database.spaceId,
              attachmentId: cell.id,
              size: cell.size,
              attachmentRef: {
                type: 'RESOURCE',
                id: database.id,
                recordId,
                fieldId: field.id,
              },
              createdBy: user?.id,
              createdAt: new Date(),
            } as SpaceAttachmentModel);
            incrementalRefSizes += cell.size;
          }
        }
      }
    }
    if (incrementalRefSizes !== 0) {
      // 先校验容量
      const space = await database.toNodeSO().getSpace();
      await space.checkStorageUsage(incrementalRefSizes);
    }
    // 根据条件来构建IO操作
    const operations: PrismaPromise<AttachmentModel>[] = [];
    let mongoSession: MongoTransactionCB = async () => {};
    if (references.length > 0) {
      // 创建空间站附件引用记录会话
      mongoSession = async (session) => {
        await db.mongo.spaceAttachment.insertMany(references, { session });
      };
    }
    if (Object.keys(attachmentRefCountMap).length > 0) {
      // 调整附件引用计数操作
      const adjustRefCountOperations = Object.keys(attachmentRefCountMap).map((attachmentId) =>
        AttachmentSO.adjustRefCountOperation(attachmentId, attachmentRefCountMap[attachmentId]),
      );
      operations.push(...adjustRefCountOperations);
    }

    return {
      operations,
      mongoSession,
    };
  }

  /**
   * 构建批量创建附件单元格值的操作
   * @returns mongo session
   */
  static async buildCreateRecordAttachmentOperation(
    user: UserSO | null,
    database: DatabaseSO,
    recordCellModelMap: RecordCellModelMap,
  ): Promise<{
    operations: PrismaPromise<AttachmentModel>[];
    mongoSession: MongoTransactionCB;
  }> {
    // 这里使用database.fieldModels去获取字段数据而不是字段对象, 因为外面传入的对象可能已经被改了
    const fields = database.fieldModels.filter((field) => field.type === 'ATTACHMENT');
    const attachmentRefCountMap: Record<string, number> = {};
    const references: SpaceAttachmentModel[] = [];
    // 附件字段追加的附件总容量
    let incrementalRefSizes = 0;
    // 遍历附件字段
    for (const field of fields) {
      const cellEntries: [string, AttachmentCellData[]][] = Object.entries(recordCellModelMap).map(
        ([recordId, fieldCellModelMap]) => {
          if (fieldCellModelMap[field.id]) {
            return [recordId, fieldCellModelMap[field.id].cell.data as AttachmentCellData[]];
          }
          return [recordId, []];
        },
      );
      for (const [recordId, cellData] of cellEntries) {
        if (cellData) {
          for (const cell of cellData) {
            // 叠加附件引用计数记录
            attachmentRefCountMap[cell.id] = cell.id in attachmentRefCountMap ? attachmentRefCountMap[cell.id] + 1 : 1;
            // 附件引用记录
            references.push({
              spaceId: database.spaceId,
              attachmentId: cell.id,
              size: cell.size,
              attachmentRef: {
                type: 'RESOURCE',
                id: database.id,
                recordId,
                fieldId: field.id,
              },
              createdBy: user?.id,
              createdAt: new Date(),
            } as SpaceAttachmentModel);
            incrementalRefSizes += cell.size;
          }
        }
      }
    }
    if (incrementalRefSizes !== 0) {
      // 先校验容量
      const space = await database.toNodeSO().getSpace();
      await space.checkStorageUsage(incrementalRefSizes);
    }
    // 根据条件来构建IO操作
    const operations: PrismaPromise<AttachmentModel>[] = [];
    let mongoSession: MongoTransactionCB = async () => {};
    if (references.length > 0) {
      // 创建空间站附件引用记录会话
      mongoSession = async (session) => {
        await db.mongo.spaceAttachment.insertMany(references, { session });
      };
    }
    if (Object.keys(attachmentRefCountMap).length > 0) {
      // 调整附件引用计数操作
      const adjustRefCountOperations = Object.keys(attachmentRefCountMap).map((attachmentId) =>
        AttachmentSO.adjustRefCountOperation(attachmentId, attachmentRefCountMap[attachmentId]),
      );
      operations.push(...adjustRefCountOperations);
    }

    return {
      operations,
      mongoSession,
    };
  }

  /**
   * 构建带附件字段的记录更新会话(仅一行)
   * @returns mongo session
   */
  static async buildUpdateAttachmentFieldCellSession(
    user: UserSO | null,
    space: SpaceSO, // 必须在事务外部获取, 不然外面在循环这个方法的时候就会重复获取
    record: RecordSO,
    updateCells: [string, CellValue][], // 要修改的字段
  ): Promise<{
    operations: PrismaPromise<AttachmentModel>[];
    mongoSessions: MongoTransactionCB[];
  }> {
    // 当发生字段类型转换时，有可能存在 data 不是 Attachment 的情况
    const isAttachmentData = (cellData: CellValue): cellData is AttachmentCellData[] =>
      isArrayOfType(cellData, (item): item is AttachmentCellData => AttachmentCellDataSchema.safeParse(item).success);
    // 需要删除的附件所在字段列表
    const deletedFieldAttachmentRef: { [fieldId: string]: AttachmentCellData } = {};
    // 需要添加新附件所在字段列表
    const addFieldAttachmentRefs: { [fieldId: string]: AttachmentCellData } = {};
    // 附件引用计数计算
    const attachmentRefCountMap: Record<string, number> = {};
    for (const [fieldId, cellValue] of updateCells) {
      // 原字段类型
      const previousField = record.fields.find((f) => f.id === fieldId);
      // 只有附件字段才需要处理
      if (previousField && previousField.type === 'ATTACHMENT') {
        // 之前的附件数据
        const previousCellValue = record.getCellData(fieldId) as AttachmentCellData[];
        // 期望更新的附件数据
        const currentCellValue = cellValue as AttachmentCellData[];
        if (isAttachmentData(previousCellValue)) {
          // 遍历之前的附件数据
          for (const cell of previousCellValue) {
            // 以之前的作为基准, 比较两者的附件数据, 有重复的则不处理, 没有的则减少引用计数
            const found = currentCellValue?.find((c) => c.id === cell.id);
            if (!found) {
              // 没找到, 被删除了, 原附件的refCount -1
              attachmentRefCountMap[cell.id] = attachmentRefCountMap[cell.id] ? attachmentRefCountMap[cell.id] - 1 : -1;
              deletedFieldAttachmentRef[fieldId] = cell;
            }
          }
        }
        if (isAttachmentData(currentCellValue)) {
          // 遍历期望更新的附件数据
          for (const cell of currentCellValue) {
            // 以期望更新的作为基准, 比较两者的附件数据, 有重复的则不处理, 没有的则增加引用计数
            const found = previousCellValue?.find((c) => c.id === cell.id);
            if (!found) {
              // 变更前和变更后不一样，新附件的refCount +1
              attachmentRefCountMap[cell.id] = attachmentRefCountMap[cell.id] ? attachmentRefCountMap[cell.id] + 1 : 1;
              addFieldAttachmentRefs[fieldId] = cell;
            }
          }
        }
      }
    }

    // 调整附件引用计数操作
    const toAdjustRefAttachmentIds = Object.keys(attachmentRefCountMap);
    const toAdjustRefAttachments = await AttachmentSO.findMany(toAdjustRefAttachmentIds);
    const operations: PrismaPromise<AttachmentModel>[] = toAdjustRefAttachments.map((toAdjustRefAttachment) =>
      AttachmentSO.adjustRefCountOperation(toAdjustRefAttachment.id, attachmentRefCountMap[toAdjustRefAttachment.id]),
    );

    // 删除一行里多个指定附件字段的引用记录
    const deletedSession: MongoTransactionCB = async (session) => {
      const attachmentIds = Object.values(deletedFieldAttachmentRef).map((v) => v.id);
      const filter: mongoose.FilterQuery<SpaceAttachmentModel> = {
        spaceId: record.spaceId,
        attachmentId: { $in: attachmentIds },
        'attachmentRef.type': 'RESOURCE',
        'attachmentRef.id': record.databaseId,
        'attachmentRef.fieldId': { $in: Object.keys(deletedFieldAttachmentRef) },
        'attachmentRef.recordId': record.id,
      };
      await db.mongo.spaceAttachment.updateMany(
        filter,
        {
          $set: {
            status: 'DELETED',
            updatedBy: user?.id,
            updatedAt: new Date(),
          },
        },
        { session },
      );
    };

    // 收集添加新附件的引用操作
    const references = Object.entries(addFieldAttachmentRefs).reduce<SpaceAttachmentModel[]>((pre, cur) => {
      const [fieldId, cell] = cur;
      pre.push({
        spaceId: record.spaceId,
        attachmentId: cell.id,
        size: cell.size,
        attachmentRef: {
          type: 'RESOURCE',
          id: record.databaseId,
          recordId: record.id,
          fieldId,
        },
        createdBy: user?.id,
        createdAt: new Date(),
      } as SpaceAttachmentModel);
      return pre;
    }, []);
    // 校验容量
    const incrementalRefSizes = references.reduce((pre, cur) => pre + cur.size, 0);
    await space.checkStorageUsage(incrementalRefSizes);
    // 引用新增操作
    const addSession: MongoTransactionCB = async (session) => {
      await db.mongo.spaceAttachment.insertMany(references, { session });
    };

    return {
      operations,
      mongoSessions: [deletedSession, addSession],
    };
  }

  /**
   * 构建带附件字段的删除记录会话
   * @returns mongo session
   */
  static async buildDeleteRecordAttachmentSession(
    user: UserSO,
    database: DatabaseSO,
    records: RecordSO[],
  ): Promise<{
    operations: PrismaPromise<AttachmentModel>[];
    mongoSession: MongoTransactionCB;
  }> {
    // 附件字段列表
    const fields = database.fieldModels.filter((field) => field.type === 'ATTACHMENT');
    // 附件引用计数计算
    const attachmentRefCountMap: Record<string, number> = {};
    // 遍历附件字段
    for (const field of fields) {
      // 每一行记录的所有附件字段的单元格数据
      const cellEntries: [string, AttachmentCellData[]][] = records.map((record) => [
        record.id,
        record.getCellData(field.id) as AttachmentCellData[],
      ]);
      // 遍历这一行的单元格数据
      for (const [_recordId, cellData] of cellEntries) {
        if (cellData) {
          for (const cell of cellData) {
            // 递减附件引用计数记录
            attachmentRefCountMap[cell.id] = attachmentRefCountMap[cell.id] ? attachmentRefCountMap[cell.id] - 1 : -1;
          }
        }
      }
    }
    // 调整附件引用计数操作
    const operations: PrismaPromise<AttachmentModel>[] = Object.keys(attachmentRefCountMap).map((id) =>
      AttachmentSO.adjustRefCountOperation(id, attachmentRefCountMap[id]),
    );
    // 删除空间站附件引用记录会话
    const filter: mongoose.FilterQuery<SpaceAttachmentModel> = {
      spaceId: database.spaceId,
      'attachmentRef.type': 'RESOURCE',
      'attachmentRef.id': database.id,
      'attachmentRef.recordId': { $in: records.map((record) => record.id) },
    };
    const mongoSession: MongoTransactionCB = async (session) => {
      await db.mongo.spaceAttachment.updateMany(
        filter,
        {
          $set: {
            status: 'DELETED',
            updatedBy: user.id,
            updatedAt: new Date(),
          },
        },
        { session },
      );
    };
    return {
      operations,
      mongoSession,
    };
  }

  /**
   * 构建删除附件字段的会话
   * @returns mongo session
   */
  static async buildDeleteAttachmentFieldSession(
    userId: string,
    field: DatabaseFieldModel,
  ): Promise<{
    operations: PrismaPromise<unknown>[];
    mongoSession: MongoTransactionCB;
  }> {
    const operations: PrismaPromise<unknown>[] = [];
    if (field.type !== 'ATTACHMENT') {
      return { operations, mongoSession: async () => {} };
    }
    // 得到所有该附件列的引用记录
    const references = await db.mongo.spaceAttachment.find<SpaceAttachmentModel>({
      spaceId: field.spaceId,
      'attachmentRef.type': 'RESOURCE',
      'attachmentRef.id': field.databaseId,
      'attachmentRef.fieldId': field.id,
    });
    const attachmentIds = references.map((ref) => ref.attachmentId);
    operations.push(
      db.prisma.attachment.updateMany({
        where: { id: { in: uniq(attachmentIds) } },
        data: {
          refCount: { decrement: 1 },
          updatedBy: userId,
          updatedAt: new Date(),
        },
      }),
    );

    const mongoSession: MongoTransactionCB = async (session) => {
      await db.mongo.spaceAttachment.updateMany(
        {
          spaceId: field.spaceId,
          'attachmentRef.type': 'RESOURCE',
          'attachmentRef.id': field.databaseId,
          'attachmentRef.fieldId': field.id,
        },
        {
          $set: {
            status: 'DELETED',
            updatedBy: userId,
            updatedAt: new Date(),
          },
        },
        { session },
      );
    };

    return { operations, mongoSession };
  }

  /**
   * 构建更改头像/封面的会话
   */
  static async buildChangeAvatarSession(
    userId: string,
    space: SpaceSO,
    attachmentRef: AttachmentReference,
    changes: {
      previous?: AvatarLogo;
      current?: AvatarLogo;
    },
  ): Promise<{
    operations: PrismaPromise<AttachmentModel>[];
    mongoSessions: MongoTransactionCB[];
  }> {
    const { previous, current } = changes;
    const operations: PrismaPromise<AttachmentModel>[] = [];
    const mongoSessions: MongoTransactionCB[] = [];
    const previousAttachment =
      previous && previous.type === 'ATTACHMENT' ? await AttachmentSO.initMaybeNull(previous.attachmentId) : null;
    if (current && current.type === 'ATTACHMENT') {
      // 改成附件
      const attachment = await AttachmentSO.initMaybeNull(current.attachmentId);
      // 跨平台 不处理
      if (!attachment) {
        return { operations, mongoSessions };
      }
      if (previousAttachment) {
        // 之前也是附件
        if (current.attachmentId !== previousAttachment.id) {
          // 附件 替换 附件， 并且附件不是原来的附件, 原附件的引用计数 -1
          operations.push(AttachmentSO.adjustRefCountOperation(previousAttachment.id, -1));
          mongoSessions.push(
            await this.deleteSession(userId, {
              spaceId: space.id,
              attachmentId: previousAttachment.id,
              attachmentRef,
            }),
          );
          // 新附件的引用计数 +1
          operations.push(AttachmentSO.adjustRefCountOperation(current.attachmentId, 1));
          mongoSessions.push(
            await this.createSession(userId, space, { id: attachment.id, size: attachment.size }, attachmentRef),
          );
        }
      } else {
        // 之前不是附件, 现在改成附件, 新附件的引用计数 +1
        operations.push(AttachmentSO.adjustRefCountOperation(current.attachmentId, 1));
        mongoSessions.push(
          await this.createSession(userId, space, { id: attachment.id, size: attachment.size }, attachmentRef),
        );
      }
    } else if (previousAttachment) {
      // 改成其他, 如果之前是附件, 则减少引用计数
      operations.push(AttachmentSO.adjustRefCountOperation(previousAttachment.id, -1));
      mongoSessions.push(
        await this.deleteSession(userId, {
          spaceId: space.id,
          attachmentId: previousAttachment.id,
          attachmentRef,
        }),
      );
    }

    return { operations, mongoSessions };
  }

  static async buildChangeAttachmentSession(
    userId: string,
    space: SpaceSO,
    attachmentRef: AttachmentReference,
    changes: {
      previousAttachmentId: string;
      currentAttachment: AttachmentVO;
    },
  ): Promise<{
    operations: PrismaPromise<AttachmentModel>[];
    mongoSessions: MongoTransactionCB[];
  }> {
    const { previousAttachmentId, currentAttachment } = changes;
    const operations: PrismaPromise<AttachmentModel>[] = [];
    const mongoSessions: MongoTransactionCB[] = [];
    // 原附件的引用计数 -1
    operations.push(AttachmentSO.adjustRefCountOperation(previousAttachmentId, -1));
    mongoSessions.push(
      await this.deleteSession(userId, {
        spaceId: space.id,
        attachmentId: previousAttachmentId,
        attachmentRef,
      }),
    );
    // 新附件的引用计数 +1
    operations.push(AttachmentSO.adjustRefCountOperation(currentAttachment.id, 1));
    mongoSessions.push(
      await this.createSession(
        userId,
        space,
        { id: currentAttachment.id, size: currentAttachment.size },
        attachmentRef,
      ),
    );
    return { operations, mongoSessions };
  }

  static async addImapTriggerAttachmentRef(spaceId: string, automationId: string, attachments: AttachmentCellData[]) {
    if (attachments.length === 0) {
      return;
    }
    // build session
    const models: SpaceAttachmentModel[] = [];
    for (const attachment of attachments) {
      models.push({
        spaceId,
        attachmentId: attachment.id,
        size: attachment.size,
        attachmentRef: { type: 'RESOURCE', id: automationId },
        createdAt: new Date(),
      } as SpaceAttachmentModel);
    }
    const mongoSession: MongoTransactionCB = async (session) => {
      await db.mongo.spaceAttachment.insertMany(models, { session });
    };
    // execute session
    await db.mongo.transaction(async (session) => {
      await mongoSession(session);
    });
  }

  static async uploadAttachmentsByBufferObject(
    userId: string,
    space: SpaceSO,
    attachments: {
      buffer: Buffer;
      contentType: string;
      fileName?: string;
      size: number;
      file?: string[]; // file path
    }[],
    prefix: string = 'default/',
  ): Promise<AttachmentSO[]> {
    const mongoSessions: MongoTransactionCB[] = [];
    const uploadAttachments: AttachmentSO[] = [];
    for (const attachment of attachments) {
      const attachmentSO = await AttachmentSO.createByBufferObject(attachment, prefix);
      if (attachmentSO) {
        mongoSessions.push(
          await this.createSession(
            userId,
            space,
            { id: attachmentSO.id, size: attachmentSO.size },
            { type: 'DEFAULT' },
          ),
        );
        uploadAttachments.push(attachmentSO);
      }
    }
    // record refrence
    await db.mongo.transaction(async (session) => {
      for (const mongoSession of mongoSessions) {
        await mongoSession(session);
      }
    });
    return uploadAttachments;
  }

  static async findSpaceResourceAttachmentIds(spaceId: string, refIds: string[]): Promise<string[]> {
    if (!refIds.length) {
      return [];
    }
    return db.mongo.spaceAttachment
      .find(
        {
          spaceId,
          'attachmentRef.id': {
            $in: refIds,
          },
          status: 'ACTIVE',
        },
        {
          attachmentId: 1,
        },
      )
      .then((values) => values.map((i) => i.attachmentId));
  }

  static async exportAttachments(spaceId: string, refIds: string[], handler: (so: AttachmentSO) => Promise<void>) {
    const attachmentIds = await this.findSpaceResourceAttachmentIds(spaceId, refIds);
    if (!attachmentIds.length) {
      return;
    }
    // 将attachmentIds切割成100个一组
    const attachmentIdsList = _.chunk(_.uniq(attachmentIds), 100);
    for (const ids of attachmentIdsList) {
      const attachmentSOs = await AttachmentSO.findMany(ids);
      // 等待所有promise执行完成
      await Promise.allSettled(attachmentSOs.map((so) => handler(so)));
    }
  }

  public static async findNonExistingAttachments(
    files: { buffer: Buffer; fileName: string }[],
  ): Promise<Record<string, { buffer: Buffer; fileName: string }>> {
    const fileMap = _.keyBy(files, (file) => file.fileName.split('.')[0]);
    const attachmentIds = Object.keys(fileMap);
    const notExistMap: Record<string, { buffer: Buffer; fileName: string }> = {};
    const idChunks = _.chunk(attachmentIds, 1000);
    for (const ids of idChunks) {
      const existsIds = await AttachmentSO.findIdsByIds(ids);
      for (const id of ids) {
        if (!existsIds.includes(id)) {
          notExistMap[id] = fileMap[id];
        }
      }
    }
    return notExistMap;
  }

  static buildUploadCallback(
    space: SpaceSO,
    userId: string,
    file: { buffer: Buffer; fileName: string; ref: AttachmentReference },
  ): { id: string; filePath: string; callback: () => Promise<void> } {
    const { id, path: filePath, uploadFile } = AttachmentSO.createByCallback(file.buffer, path.extname(file.fileName));
    return {
      id,
      filePath,
      callback: async () => {
        await uploadFile();
        await db.mongo.spaceAttachment.insertMany([
          {
            spaceId: space.id,
            attachmentId: id,
            size: file.buffer.length,
            attachmentRef: file.ref,
            createdBy: userId,
            updatedBy: userId,
          },
        ]);
      },
    };
  }

  static async generateImages(
    bo: AIImageBO & Pick<AIGenerateImageProps, 'n' | 'prompt' | 'size'>,
    context: {
      space: SpaceSO;
      user: UserSO;
      checkCoin?: boolean;
    },
  ): Promise<AttachmentSO[]> {
    const { space, user, checkCoin = false } = context;
    await space.checkStorageUsage(1);
    const coinAccount = await space.billing.getCoinsAccount();
    if (checkCoin) {
      const coinEnough = await coinAccount.enough(1);
      if (!coinEnough) {
        throw new ServerError(errors.billing.ai_credit_not_enough);
      }
    }
    const attachments = await AttachmentSO.generateImages(bo, coinAccount);
    const mongoSessions: MongoTransactionCB[] = [];
    for (const attachment of attachments) {
      mongoSessions.push(
        await this.createSession(
          user.id,
          space,
          { id: attachment.id, size: attachment.size },
          { type: 'DEFAULT' },
          false,
        ),
      );
    }
    await db.mongo.transaction(async (session) => {
      for (const mongoSession of mongoSessions) {
        await mongoSession(session);
      }
    });
    return attachments;
  }
}

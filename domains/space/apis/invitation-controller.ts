import { EmailInvitationSO } from '@bika/domains/space/server/invitation/email-invitation-so';
import { LinkInvitationSO } from '@bika/domains/space/server/invitation/link-invitation-so';
import { SpaceAuditLogSO } from '@bika/domains/system/server/audit/space-audit-log-so';
import { UserSO } from '@bika/domains/user/server/user-so';
import {
  EmailInvitationAcceptReq,
  EmailInvitationInfoReq,
  EmailInvitationRejectReq,
  EmailInvitationReq,
  EmailInvite,
  EmailInviteListReq,
  SpaceInvitationAcceptReq,
  SpaceInvitationCreateReq,
  SpaceInvitationDeleteReq,
  SpaceInvitationInfoReq,
  SpaceInvitationListReq,
  SpaceInvitationRefreshReq,
} from '@bika/types/space/dto';
import { SpaceLinkInvitationVO, SpaceLinkInvitationDetailVO, SpaceEmailInvitationVO } from '@bika/types/space/vo';
import { ApiFetchRequestContext } from '@bika/types/user/vo';

/**
 * 获取空间站（公开）链接邀请列表
 */
export async function getSpaceLinkInvitations(
  user: UserSO,
  req: SpaceInvitationListReq,
): Promise<SpaceLinkInvitationVO[]> {
  const { spaceId } = req;
  // check user is in space
  const member = await user.getMember(spaceId);
  const space = await member.getSpace();
  const invitations = await space.getLinkInvitations();
  return Promise.all(invitations.map(async (so) => so.toVO({ locale: user.locale })));
}

/**
 * 创建空间站（公开）链接邀请
 */
export async function createSpaceLinkInvitation(
  ctx: ApiFetchRequestContext,
  req: SpaceInvitationCreateReq,
): Promise<SpaceLinkInvitationVO> {
  const { spaceId, type, teamId, roleIds } = req;
  const { userId } = ctx.session!;
  const user = await UserSO.init(userId);
  const member = await user.getMember(spaceId);
  const space = await member.getSpace();
  await space.getAdminRoleAclSO().authorize(member, 'invite');
  const linkInvitation = await member.createSpaceLinkInvitation({ invitationType: type, teamId, roleIds });
  const invitationVO = await linkInvitation.toVO({ locale: user.locale });
  // 记录审计事件
  SpaceAuditLogSO.createFromRequestContext(ctx, {
    spaceId: space.id,
    type: 'invitation.link.create',
    token: linkInvitation.id,
  });
  return invitationVO;
}

/**
 * 刷新空间站（公开）链接邀请
 */
export async function refreshSpaceLinkInvitation(
  ctx: ApiFetchRequestContext,
  req: SpaceInvitationRefreshReq,
): Promise<SpaceLinkInvitationVO> {
  const { spaceId, inviteToken } = req;
  const { userId } = ctx.session!;
  const user = await UserSO.init(userId);
  const member = await user.getMember(spaceId);
  const space = await member.getSpace();
  await space.getAdminRoleAclSO().authorize(member, 'invite');
  const linkInvitation = await space.getLinkInvitation(inviteToken);
  if (!linkInvitation) {
    throw new Error('Invitation not found');
  }
  const refreshed = await linkInvitation.refresh(user.id);
  return refreshed.toVO({ locale: user.locale });
}

/**
 * 删除空间站（公开）链接邀请
 */
export async function deleteSpaceLinkInvitation(
  ctx: ApiFetchRequestContext,
  req: SpaceInvitationDeleteReq,
): Promise<boolean> {
  const { spaceId, inviteToken } = req;
  const { userId } = ctx.session!;
  const user = await UserSO.init(userId);
  const memberSO = await user.getMember(spaceId);
  const space = await memberSO.getSpace();
  await space.getAdminRoleAclSO().authorize(memberSO, 'invite');
  const linkInvitation = await space.getLinkInvitation(inviteToken);
  if (!linkInvitation) {
    throw new Error('Invitation not found');
  }
  await linkInvitation.delete(user.id);

  // 记录审计事件
  SpaceAuditLogSO.createFromRequestContext(ctx, {
    spaceId: space.id,
    type: 'invitation.link.delete',
    token: linkInvitation.id,
  });
  return true;
}

/**
 * 获取空间站（公开）链接邀请信息（免登API
 */
export async function getSpaceLinkInvitationDetail(req: SpaceInvitationInfoReq): Promise<SpaceLinkInvitationDetailVO> {
  const { inviteToken } = req;
  const invitation = await LinkInvitationSO.init(inviteToken);
  const space = await invitation.getSpace();
  const member = await invitation.getMember();
  return {
    token: inviteToken,
    spaceId: space.id,
    spaceName: space.name,
    spaceLogo: space.logo,
    inviterName: member.getName(),
  };
}

/**
 * 接受空间站（公开）链接邀请
 */
export async function acceptSpaceLinkInvitation(
  ctx: ApiFetchRequestContext,
  req: SpaceInvitationAcceptReq,
): Promise<boolean> {
  const { inviteToken } = req;
  const { userId } = ctx.session!;
  const user = await UserSO.init(userId);
  const invitation = await LinkInvitationSO.init(inviteToken);
  const space = await invitation.getSpace();
  // 校验用量
  const entitlement = await space.getEntitlement();
  if (invitation.isGuestInvitation()) {
    // 校验访客数量用量
    await entitlement.checkUsageExceed({ feature: 'GUESTS' });
  } else {
    // 校验成员数量用量
    await entitlement.checkUsageExceed({ feature: 'SEATS' });
  }

  await user.join(invitation);
  // 记录审计事件
  SpaceAuditLogSO.createFromRequestContext(ctx, {
    spaceId: space.id,
    type: 'invitation.link.accept',
    token: invitation.id,
  });
  return true;
}

export async function listEmailInvitations(user: UserSO, req: EmailInviteListReq) {
  const { spaceId, email, pageNo, pageSize } = req;
  const member = await user.getMember(spaceId);
  const space = await member.getSpace();
  // 校验权限
  await space.getAdminRoleAclSO().authorize(member, 'invite');
  // 获取邮箱邀请列表
  const { list, pagination } = await space.getEmailInvitations({ email }, { pageNo, pageSize });
  const data = await Promise.all(list.map((l) => l.toVO()));
  return { data, pagination };
}

export async function getEmailInvitationInfo(req: EmailInvitationInfoReq): Promise<SpaceEmailInvitationVO> {
  const { inviteId } = req;
  // 获取邮箱邀请信息
  const invitation = await EmailInvitationSO.init(inviteId);
  return invitation.toVO();
}

export async function emailInvite(ctx: ApiFetchRequestContext, req: EmailInvitationReq): Promise<void> {
  const { spaceId, invites } = req;
  const { userId } = ctx.session!;
  const user = await UserSO.init(userId);
  const member = await user.getMember(spaceId);
  const space = await member.getSpace();
  // 校验权限
  await space.getAdminRoleAclSO().authorize(member, 'invite');
  // 根据邮箱地址去重
  const uniqueInvites = invites.reduce<EmailInvite[]>((acc, cur) => {
    if (acc.find((i) => i.email === cur.email)) {
      return acc;
    }
    return [...acc, cur];
  }, []);
  // 校验用量
  const entitlement = await space.getEntitlement();
  // 免费订阅，每日邀请数量限制
  if (entitlement.planFeature.isFree) {
    // 获取当日的邀请数量
    const todayInvites = await EmailInvitationSO.getTodayInvitationCount(space.id, user.timeZone);
    const usageFeature = entitlement.planFeature.getUsageFeature('SEATS');
    const exceed = usageFeature.isUsageExceed(todayInvites + uniqueInvites.length);
    if (exceed) {
      throw new Error('Daily email invitation limit exceeded');
    }
  }
  if (uniqueInvites.some((i) => i.type === 'GUEST')) {
    // 校验访客数量用量
    const validatedGuests = uniqueInvites.filter((i) => i.type === 'GUEST').length;
    await entitlement.checkUsageExceed({ feature: 'GUESTS', value: validatedGuests });
  } else if (uniqueInvites.some((i) => i.type === 'MEMBER')) {
    // 校验成员数量用量
    const validatedSeats = uniqueInvites.filter((i) => i.type === 'MEMBER').length;
    await entitlement.checkUsageExceed({ feature: 'SEATS', value: validatedSeats });
  }
  // 发送邀请
  await member.invite(uniqueInvites);
  // 记录审计事件
  SpaceAuditLogSO.createFromRequestContext(ctx, {
    spaceId: space.id,
    type: 'invitation.email.send',
    emails: uniqueInvites.map((i) => i.email).join(','),
  });
}

export async function resendEmailInvite(ctx: ApiFetchRequestContext, req: EmailInvitationInfoReq): Promise<void> {
  const { inviteId } = req;
  const { userId } = ctx.session!;
  const user = await UserSO.init(userId);
  const invitation = await EmailInvitationSO.init(inviteId);
  const member = await user.getMember(invitation.model.spaceId);
  const space = await member.getSpace();
  // 校验权限
  await space.getAdminRoleAclSO().authorize(member, 'invite');
  // 重发邀请邮件
  await invitation.resend();
  // 记录审计事件
  SpaceAuditLogSO.createFromRequestContext(ctx, {
    spaceId: space.id,
    type: 'invitation.email.resend',
    email: invitation.model.email,
  });
}

export async function deleteEmailInvitation(ctx: ApiFetchRequestContext, req: EmailInvitationInfoReq) {
  const { inviteId } = req;
  const { userId } = ctx.session!;
  const user = await UserSO.init(userId);
  const invitation = await EmailInvitationSO.init(inviteId);
  const member = await user.getMember(invitation.model.spaceId);
  const space = await member.getSpace();
  // 校验权限
  await space.getAdminRoleAclSO().authorize(member, 'invite');
  const invitedEmail = invitation.model.email;
  // 删除邀请
  await invitation.delete();
  // 记录审计事件
  SpaceAuditLogSO.createFromRequestContext(ctx, {
    spaceId: space.id,
    type: 'invitation.email.delete',
    email: invitedEmail,
  });
}

export async function acceptEmailInvitation(ctx: ApiFetchRequestContext, req: EmailInvitationAcceptReq): Promise<void> {
  const { inviteId } = req;
  const { userId } = ctx.session!;
  const user = await UserSO.init(userId);
  const invitation = await EmailInvitationSO.init(inviteId);
  const space = await invitation.getSpace();
  // 校验用量
  const entitlement = await space.getEntitlement();
  if (invitation.isGuestInvitation()) {
    // 校验访客数量用量
    await entitlement.checkUsageExceed({ feature: 'GUESTS' });
  } else {
    // 校验成员数量用量
    await entitlement.checkUsageExceed({ feature: 'SEATS' });
  }
  await user.acceptInvitation(invitation);
  // 记录审计事件
  SpaceAuditLogSO.createFromRequestContext(ctx, {
    spaceId: invitation.model.spaceId,
    type: 'invitation.email.accept',
    email: invitation.model.email,
  });
}

export async function rejectEmailInvitation(user: UserSO, req: EmailInvitationRejectReq) {
  const { inviteId } = req;
  const invitation = await EmailInvitationSO.init(inviteId);
  await invitation.update({ status: 'REJECTED', updatedBy: user.id });
}

import { isEmail, isNotBlank } from 'basenext/utils/string';
import { DatabaseSO } from '@bika/domains/database/server/database-so';
import { FieldSO } from '@bika/domains/database/server/fields/field-so';
import { RecordSO } from '@bika/domains/database/server/record-so';
import { BObuildIdConverter } from '@bika/domains/node/server/bo-builder/bo-build-id-converter';
import { IRelationIdOpts } from '@bika/domains/node/server/types';
import {
  convertStringToArray,
  isVariableTemplate,
  render,
  replaceVariablesInString,
} from '@bika/domains/shared/server';
import { SpaceSO } from '@bika/domains/space/server/space-so';
import { MemberSO } from '@bika/domains/unit/server/member-so';
import { UnitFactory } from '@bika/domains/unit/server/unit-factory';
import {
  To,
  ToEmailField,
  ToMemberField,
  ToSpecifyUnitsSchema,
  ToUnitMemberSchema,
  ToUnitRoleSchema,
  ToUnitTeamSchema,
  ToUserSchema,
} from '@bika/types/unit/bo';

const extractEmails = (value: unknown): string[] => {
  const emails: string[] = [];
  if (value === null || value === undefined) {
    return emails;
  }
  if (Array.isArray(value)) {
    value.forEach((v) => {
      emails.push(...extractEmails(v));
    });
    return emails;
  }
  if (typeof value === 'string') {
    emails.push(
      ...value
        .split(',')
        .map((e) => e.trim())
        .filter((e) => isEmail(e)),
    );
  }
  return emails;
};

interface IParseContext {
  userId?: string;
  templateNodeId?: string;
  props?: { [key: string]: unknown };
}

export class RecipientParser {
  private readonly _spaceId: string;

  constructor(spaceId: string) {
    this._spaceId = spaceId;
  }

  async getSpace(): Promise<SpaceSO> {
    return SpaceSO.init(this._spaceId);
  }

  static convertTo(tos: To[], converter: BObuildIdConverter): To[] {
    const relationFunc = converter.getRelationFunc();

    const convertUnitValue = (unitId: string | string[] | undefined) => {
      if (!unitId) {
        return unitId;
      }
      if (Array.isArray(unitId)) {
        return unitId.length === 1 ? unitId[0] : undefined;
      }
      return unitId;
    };

    const convert = (to: To): To => {
      switch (to.type) {
        case 'USER': {
          const { userId } = ToUserSchema.parse(to);
          return { ...to, userId: isVariableTemplate(userId) ? replaceVariablesInString(userId, relationFunc) : '' };
        }
        case 'SPECIFY_UNITS': {
          const unitIds = to.unitIds
            .filter((u) => isVariableTemplate(u))
            .map((u) => replaceVariablesInString(u, relationFunc));
          return { ...to, unitIds };
        }
        case 'UNIT_ROLE': {
          const { roleId, roleTemplateId } = to;
          const { instanceId, templateId } = converter.convert({
            instanceId: convertUnitValue(roleId),
            templateId: roleTemplateId,
            prevKey: 'ROLE',
            allowBlank: true,
            outputBlank: true,
            notFoundErrMsg: `Recipient linked role [${roleId}] not found`,
          });
          return { ...to, roleId: instanceId, roleTemplateId: templateId };
        }
        case 'UNIT_TEAM': {
          const { teamId, teamTemplateId } = ToUnitTeamSchema.parse(to);
          const { instanceId, templateId } = converter.convert({
            instanceId: convertUnitValue(teamId),
            templateId: teamTemplateId,
            prevKey: 'TEAM',
            allowBlank: true,
            outputBlank: true,
            notFoundErrMsg: `Recipient linked team [${teamId}] not found`,
          });
          return { ...to, teamId: instanceId, teamTemplateId: templateId };
        }
        case 'UNIT_MEMBER': {
          const { memberId } = ToUnitMemberSchema.parse(to);
          const { instanceId } = converter.convert({
            instanceId: convertUnitValue(memberId),
            templateId: undefined,
            prevKey: 'MEMBER',
            allowBlank: true,
            outputBlank: true,
            notFoundErrMsg: `Recipient linked member [${memberId}] not found`,
          });
          return { ...to, memberId: instanceId || '' };
        }
        case 'ADMIN':
        case 'ALL_MEMBERS':
        case 'CURRENT_OPERATOR':
          break;
        case 'EMAIL_FIELD':
        case 'MEMBER_FIELD': {
          const { databaseId, databaseTemplateId, viewId, viewTemplateId, fieldId, fieldTemplateId } = to;
          const database = converter.convert({
            instanceId: databaseId,
            templateId: databaseTemplateId,
            notFoundErrMsg: `Recipient linked database [${databaseId}] not in current folder`,
          });
          const view = converter.convert({
            instanceId: viewId,
            templateId: viewTemplateId,
            prevKey: databaseTemplateId,
            allowBlank: true,
            allowExportNotFound: true,
            notFoundErrMsg: `Recipient linked view [${viewId}] not in database [${databaseId}]`,
          });
          const field = converter.convert({
            instanceId: fieldId,
            templateId: fieldTemplateId,
            prevKey: databaseTemplateId,
            allowBlank: true,
            allowExportNotFound: true,
            notFoundErrMsg: `Recipient linked field [${fieldId}] not in database [${databaseId}]`,
          });
          return {
            ...to,
            databaseId: database.instanceId,
            databaseTemplateId: database.templateId,
            viewId: view.instanceId,
            viewTemplateId: view.templateId,
            fieldId: field.instanceId,
            fieldTemplateId: field.templateId,
          };
        }
        case 'EMAIL_STRING': {
          const { email } = to;
          return { ...to, email: replaceVariablesInString(email, relationFunc) };
        }
        case 'RECIPIENT': {
          const { recipientType, recipientId } = to;
          const types = ['USER', 'MEMBER', 'TEAM'];
          if (types.includes(recipientType)) {
            return {
              ...to,
              recipientId: isVariableTemplate(recipientId) ? replaceVariablesInString(recipientId, relationFunc) : '',
            };
          }
          return { ...to, recipientId: replaceVariablesInString(recipientId, relationFunc) };
        }
        default:
          break;
      }
      return to;
    };

    return tos.map(convert);
  }

  static relationInstanceId(tos: To[], opts: IRelationIdOpts) {
    const { convertToInstanceId, replaceInstanceId } = opts;
    for (const to of tos) {
      switch (to.type) {
        case 'SPECIFY_UNITS': {
          const { unitIds } = ToSpecifyUnitsSchema.parse(to);
          if (unitIds && convertToInstanceId) {
            to.unitIds = unitIds.map((u) => replaceVariablesInString(u, convertToInstanceId));
          }
          if (unitIds && replaceInstanceId) {
            to.unitIds = unitIds.map((u) => replaceVariablesInString(u, replaceInstanceId));
          }
          break;
        }
        case 'UNIT_ROLE': {
          const { roleId, roleTemplateId } = ToUnitRoleSchema.parse(to);
          if (convertToInstanceId && !roleId && roleTemplateId) {
            to.roleId = convertToInstanceId(`ROLE:${roleTemplateId}`);
            to.roleTemplateId = undefined;
          }
          break;
        }
        case 'UNIT_TEAM': {
          const { teamId, teamTemplateId } = ToUnitTeamSchema.parse(to);
          if (convertToInstanceId && !teamId && teamTemplateId) {
            to.teamId = convertToInstanceId(`TEAM:${teamTemplateId}`);
            to.teamTemplateId = undefined;
          }
          break;
        }
        case 'EMAIL_FIELD':
        case 'MEMBER_FIELD': {
          const { databaseId, databaseTemplateId, viewId, viewTemplateId, fieldId, fieldTemplateId } = to;
          if (convertToInstanceId) {
            if (!databaseId && databaseTemplateId) {
              to.databaseId = convertToInstanceId(databaseTemplateId);
              to.databaseTemplateId = undefined;
            }
            if (!viewId && isNotBlank(viewTemplateId)) {
              to.viewId = convertToInstanceId(`${databaseTemplateId}:${viewTemplateId}`);
              to.viewTemplateId = undefined;
            }
            if (!fieldId && fieldTemplateId) {
              to.fieldId = convertToInstanceId(`${databaseTemplateId}:${fieldTemplateId}`);
              to.fieldTemplateId = undefined;
            }
            break;
          }
          if (replaceInstanceId) {
            if (databaseId) {
              to.databaseId = replaceInstanceId(databaseId);
            }
            if (viewId) {
              to.viewId = replaceInstanceId(viewId);
            }
            if (fieldId) {
              to.fieldId = replaceInstanceId(fieldId);
            }
            break;
          }
          break;
        }
        case 'EMAIL_STRING': {
          const { email } = to;
          if (replaceInstanceId && email) {
            to.email = replaceVariablesInString(email, replaceInstanceId);
          }
          if (email && convertToInstanceId) {
            to.email = replaceVariablesInString(email, convertToInstanceId);
          }
          break;
        }
        default:
          break;
      }
    }
  }

  static toTemplate(tos: To[], getTemplateId?: (id: string) => string | undefined): To[] {
    const toTemplate: To[] = [];
    for (const to of tos) {
      switch (to.type) {
        case 'SPECIFY_UNITS': {
          const { unitIds } = to;
          if (unitIds.length && getTemplateId) {
            toTemplate.push({
              ...to,
              unitIds: unitIds
                .filter((u) => isVariableTemplate(u))
                .map((u) => replaceVariablesInString(u, getTemplateId)),
            });
          }
          break;
        }
        // 模版发布目前还不包括role
        // 模版发布目前还不包括team
        // 模版发布目前还不包括member
        case 'UNIT_ROLE':
        case 'UNIT_TEAM':
        case 'UNIT_MEMBER': {
          break;
        }
        case 'EMAIL_FIELD':
        case 'MEMBER_FIELD': {
          const databaseTemplateId = to.databaseId ? getTemplateId?.(to.databaseId) : undefined;
          const viewTemplateId = to.viewId ? getTemplateId?.(to.viewId!) : undefined;
          const fieldTemplateId = to.fieldId ? getTemplateId?.(to.fieldId) : undefined;
          toTemplate.push({
            ...to,
            databaseTemplateId,
            viewTemplateId,
            fieldTemplateId,
            databaseId: undefined,
            viewId: undefined,
            fieldId: undefined,
          });
          break;
        }
        case 'EMAIL_STRING':
          toTemplate.push({
            ...to,
            email:
              isVariableTemplate(to.email) && getTemplateId ? replaceVariablesInString(to.email, getTemplateId) : '',
          });
          break;
        case 'ALL_MEMBERS':
        case 'CURRENT_OPERATOR':
        case 'ADMIN': {
          toTemplate.push(to);
          break;
        }
        case 'USER': {
          toTemplate.push({
            type: 'USER',
            userId:
              isVariableTemplate(to.userId) && getTemplateId ? replaceVariablesInString(to.userId, getTemplateId) : '',
          });
          break;
        }
        case 'RECIPIENT': {
          toTemplate.push({
            ...to,
            recipientId:
              isVariableTemplate(to.recipientId) && getTemplateId
                ? replaceVariablesInString(to.recipientId, getTemplateId)
                : '',
          });
          break;
        }
        default:
          break;
      }
    }
    return toTemplate;
  }

  static async fillInTo(tos: To[], templateNodeId?: string): Promise<To[]> {
    const filledTos = [];
    for (const to of tos) {
      switch (to.type) {
        case 'EMAIL_FIELD':
        case 'MEMBER_FIELD': {
          const { databaseId, databaseTemplateId, viewId, viewTemplateId, fieldId, fieldTemplateId } = to;
          if (databaseId || !databaseTemplateId || !templateNodeId) {
            filledTos.push(to);
            break;
          }
          let datId = databaseId;
          let fldId = fieldId;
          let viwId = viewId;
          try {
            const database = await DatabaseSO.findDatabase({ templateNodeId, databaseTemplateId });
            datId = database.id;
            if (!fieldId && fieldTemplateId) {
              const field = database.getFields().find((f) => f.templateId === fieldTemplateId);
              fldId = field?.id;
            }
            if (!viewId && isNotBlank(viewTemplateId)) {
              const view = await database.getViewByViewKey({ viewTemplateId });
              viwId = view.id;
            }
          } catch (_e) {
            // do nothing
          }
          filledTos.push({ ...to, databaseId: datId, fieldId: fldId, viewId: viwId });
          break;
        }
        default:
          filledTos.push(to);
          break;
      }
    }
    return filledTos;
  }

  /**
   * 此方法可能会有性能问题, 返回的成员可能会很庞大占用内存, 遇到瓶颈时需要优化
   */
  async parseToMembers(tos: To[], parseContext?: IParseContext): Promise<MemberSO[]> {
    const memberSOs: MemberSO[] = [];
    const { userId, templateNodeId, props } = parseContext || {};
    for (const to of tos) {
      switch (to.type) {
        case 'ADMIN': {
          const space = await this.getSpace();
          const mainAdmin = await space.getOwner();
          memberSOs.push(mainAdmin);
          break;
        }
        case 'ALL_MEMBERS': {
          const space = await this.getSpace();
          // 分页获取所有成员
          await space.findMembersAsPage((records) => memberSOs.push(...records), 30);
          // TODO: Include Unit Guest
          break;
        }
        case 'SPECIFY_UNITS': {
          const { unitIds, requireRoleTemplateId } = ToSpecifyUnitsSchema.parse(to);
          if (unitIds.length === 0) {
            break;
          }
          const uIds = unitIds.map((unitId) => render(unitId, props)).flatMap((val) => convertStringToArray(val));
          const members = await UnitFactory.extractMembersFromUnits(this._spaceId, uIds);
          if (requireRoleTemplateId) {
            const space = await this.getSpace();
            const roleSO = await space.getRoleByTemplateId(requireRoleTemplateId);
            if (roleSO) {
              const filterMembers = await roleSO.getMembers();
              memberSOs.push(...members.filter((member) => filterMembers.some((m) => m.id === member.id)));
              break;
            }
          }
          memberSOs.push(...members);
          break;
        }
        case 'UNIT_ROLE': {
          const { roleId, roleTemplateId } = ToUnitRoleSchema.parse(to);
          if (roleId) {
            const members = await UnitFactory.extractMembersFromUnits(
              this._spaceId,
              Array.isArray(roleId) ? roleId : [roleId],
            );
            memberSOs.push(...members);
            break;
          }
          if (!roleTemplateId) {
            break;
          }
          const space = await this.getSpace();
          const roleSO = await space.getRoleByTemplateId(roleTemplateId);
          if (roleSO) {
            const members = await UnitFactory.extractMembersFromUnits(this._spaceId, [roleSO.id]);
            memberSOs.push(...members);
          }
          break;
        }
        case 'UNIT_TEAM': {
          const { teamId } = ToUnitTeamSchema.parse(to);
          if (teamId) {
            const teamIds = Array.isArray(teamId) ? teamId : [teamId];
            const uIds = teamIds.map((i) => render(i, props));
            const members = await UnitFactory.extractMembersFromUnits(this._spaceId, uIds);
            memberSOs.push(...members);
            break;
          }
          break;
        }
        case 'UNIT_MEMBER': {
          const { memberId } = ToUnitMemberSchema.parse(to);
          if (memberId) {
            const memberIds = Array.isArray(memberId) ? memberId : [memberId];
            const uIds = memberIds.map((i) => render(i, props));
            const members = await UnitFactory.extractMembersFromUnits(this._spaceId, uIds);
            memberSOs.push(...members);
            break;
          }
          break;
        }
        case 'MEMBER_FIELD': {
          const unitIds: string[] = [];
          const userIds: string[] = [];
          const processor = (records: RecordSO[], field: FieldSO) => {
            const isMemberField = field.type === 'MEMBER';
            records.forEach((record) => {
              const value = record.getCellData(field.id);
              if (!value) {
                return;
              }
              // 成员字段
              if (isMemberField) {
                if (!Array.isArray(value)) {
                  return;
                }
                value.filter((v) => v && typeof v === 'string').forEach((v) => unitIds.push(v as string));
                return;
              }
              // 创建人、修改人字段
              if (value && typeof value === 'string') {
                userIds.push(value);
              }
            });
          };
          await this.extractFieldData(to, processor, templateNodeId);
          if (unitIds.length > 0) {
            const members = await UnitFactory.extractMembersFromUnits(this._spaceId, unitIds);
            memberSOs.push(...members);
          } else if (userIds.length > 0) {
            const members = await UnitFactory.findMembers(userIds, this._spaceId);
            memberSOs.push(...members);
          }
          break;
        }
        case 'CURRENT_OPERATOR': {
          if (!userId) {
            break;
          }
          const member = await UnitFactory.findMember(userId, this._spaceId);
          if (member) {
            memberSOs.push(member);
          }
          break;
        }
        case 'USER': {
          const { userId: toUserIdTemplateStr } = ToUserSchema.parse(to);
          const toUserId = render(toUserIdTemplateStr, props);
          if (toUserId && toUserId.length > 0) {
            const member = await UnitFactory.findMember(toUserId, this._spaceId);
            if (member) {
              memberSOs.push(member);
            }
          }
          break;
        }
        default:
          throw new Error(`Not Implement ToType: ${to.type}`);
      }
    }
    // 过滤重复的成员, 有可能成员在多个小组中
    return memberSOs.filter((member, index, self) => self.findIndex((m) => m.id === member.id) === index);
  }

  async parseToEmails(tos: To[], parseContext?: IParseContext): Promise<string[]> {
    const { templateNodeId, props } = parseContext || {};
    const emails: string[] = [];
    for (const to of tos) {
      switch (to.type) {
        case 'EMAIL_STRING': {
          const emailStr = to.email;
          const parsedEmails = render(emailStr, props);
          // 合并到emails
          emails.push(...extractEmails(parsedEmails));
          break;
        }
        case 'EMAIL_FIELD': {
          const emailStrings = await this.extractFieldData(
            to,
            (records, field) => records.flatMap((record) => extractEmails(record.getCellData(field.id))),
            templateNodeId,
          );
          emails.push(...emailStrings);
          break;
        }
        default: {
          const memberSOs = await this.parseToMembers([to], parseContext);
          memberSOs.forEach((member) => {
            const email = member.email;
            if (email) {
              emails.push(email);
            }
          });
          break;
        }
      }
    }
    return emails.filter((email, index, self) => self.findIndex((e) => e === email) === index);
  }

  private async extractFieldData<T extends unknown[] | void>(
    toField: ToEmailField | ToMemberField,
    processor: (records: RecordSO[], field: FieldSO) => T,
    templateNodeId?: string,
  ): Promise<T> {
    const { databaseId, databaseTemplateId, viewId, viewTemplateId, fieldId, fieldTemplateId, type } = toField;
    const fieldKey = fieldId || fieldTemplateId;
    if (!fieldKey) {
      throw new Error('fieldId or fieldTemplateId is required');
    }
    const databaseSO = await DatabaseSO.findDatabase({ templateNodeId, databaseId, databaseTemplateId });
    const field = databaseSO
      .getFields()
      .find((f) => f.id === fieldId || (fieldTemplateId && f.templateId === fieldTemplateId));
    if (!field) {
      throw new Error(`Field ${fieldKey} not found`);
    }
    // 字段类型校验
    const fieldTypeValidate = () => {
      switch (type) {
        case 'MEMBER_FIELD':
          if (field.type !== 'MEMBER' && field.type !== 'CREATED_BY' && field.type !== 'MODIFIED_BY') {
            throw new Error(`Field ${fieldKey} is not member type`);
          }
          break;
        case 'EMAIL_FIELD':
          if (field.type !== 'EMAIL') {
            throw new Error(`Field ${fieldKey} is not email type`);
          }
          break;
        default:
          break;
      }
    };
    fieldTypeValidate();

    if (isNotBlank(viewId) || isNotBlank(viewTemplateId)) {
      return databaseSO.processViewRecordsByPage({ viewId, viewTemplateId }, async (records) =>
        processor(records, field),
      );
    }
    return databaseSO.getRecordsAsStream(async (records) => processor(records, field), { pageSize: 1000 });
  }
}

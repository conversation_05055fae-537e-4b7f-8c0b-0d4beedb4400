/* eslint-disable max-classes-per-file */
import type { NodeTreeVO } from '@bika/types/node/vo';
import { NodeSO } from './node-so';

/**
 * 节点排序器，for NodeSO
 */
export class NodeSorter {
  private firstNodes: (NodeSO | NodeTreeVO)[] = [];
  private preNodeMap: Record<string, NodeSO | NodeTreeVO> = {};

  private constructor(nodes: (NodeSO | NodeTreeVO)[]) {
    nodes.forEach((node) => {
      if (!node.preNodeId) {
        // 将所有 preNodeId 为 null 的节点都收集到 firstNodes 中
        this.firstNodes.push(node);
      } else {
        this.preNodeMap[node.preNodeId] = node;
      }
    });
  }

  static init(nodes: (NodeSO | NodeTreeVO)[]) {
    return new NodeSorter(nodes);
  }

  sort() {
    const sortedNodes: (NodeSO | NodeTreeVO)[] = [];

    // 如果没有 preNodeId 为 null 的节点，返回空数组
    if (this.firstNodes.length === 0) {
      return sortedNodes;
    }

    // 处理所有 preNodeId 为 null 的节点，将它们都排在前面
    this.firstNodes.forEach((firstNode) => {
      this.traverse(firstNode, sortedNodes);
    });

    return sortedNodes;
  }

  private traverse(currentNode: NodeSO | NodeTreeVO, sortedNodes: (NodeSO | NodeTreeVO)[]) {
    // 检查节点是否已经被添加到结果中，避免重复
    if (sortedNodes.some((node) => node.id === currentNode.id)) {
      return;
    }

    sortedNodes.push(currentNode);
    const matchNode = Object.keys(this.preNodeMap).find((id) => id === currentNode.id);
    if (matchNode) {
      this.traverse(this.preNodeMap[currentNode.id], sortedNodes);
    }
  }
}

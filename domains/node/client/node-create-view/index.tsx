import _ from 'lodash';
import React, { useState, useEffect } from 'react';
import toast from 'react-hot-toast';
import { getAppEnv } from 'sharelib/app-env';
import { useApiCaller } from '@bika/api-caller/context';
import { errors } from '@bika/contents/config/server/error/errors';
import { useLocale } from '@bika/contents/i18n';
import type { NodeResourceType } from '@bika/types/node/bo';
import type { ExampleVO } from '@bika/types/node/vo';
import { useSpaceRouter, useSpaceContextForce } from '@bika/types/space/context';
import { Button } from '@bika/ui/button';
import { AutomationNode, DatabaseNode } from '@bika/ui/editor/flow-editor';
import AttachmentOutlined from '@bika/ui/icons/components/attachment_outlined';
import AutomationOutlined from '@bika/ui/icons/components/automation_outlined';
import CheckOutlined from '@bika/ui/icons/components/check_outlined';
import DashboardOutlined from '@bika/ui/icons/components/dashboard_outlined';
import DatabaseOutlined from '@bika/ui/icons/components/datasheet_outlined';
import FileOutlined from '@bika/ui/icons/components/file_outlined';
import FolderAddOutlined from '@bika/ui/icons/components/folder_add_outlined';
import FormAddOutlined from '@bika/ui/icons/components/form_add_outlined';
import ImportOutlined from '@bika/ui/icons/components/import_outlined';
import MirrorOutlined from '@bika/ui/icons/components/mirror_outlined';
import RobotOutlined from '@bika/ui/icons/components/robot_outlined';
import { Stack } from '@bika/ui/layouts';
import { NodeIcon } from '@bika/ui/node/icon';
import { TemplateInfoCard } from '@bika/ui/template-card';
import { Typography } from '@bika/ui/texts';

// file_outlined
// check_outlined
export interface CreateNodeViewProps {
  defaultNodeType?: string;
  parentId: string;
  isRedirect?: boolean;
}
// http://localhost:3000/space/spc2QIN5shsNSfEqyZdAW7SA/node/atoPFEorQDPmAFANa7U8virA?modal=JTdCJTIydHlwZSUyMiUzQSUyMmNyZWF0ZS1ub2RlJTIyJTJDJTIycHJvcHMlMjIlM0ElN0IlMjJkZWZhdWx0Tm9kZVR5cGUlMjIlM0ElMjJBVVRPTUFUSU9OJTIyJTJDJTIycGFyZW50SWQlMjIlM0ElMjJub2R0cGxBUGRBOXNubTNaWERtbDQwZmVmenElMjIlMkMlMjJpc1JlZGlyZWN0JTIyJTNBdHJ1ZSU3RCU3RA%3D%3D
export function NodeCreateView(props: CreateNodeViewProps) {
  const { t, i } = useLocale();
  const [type, setType] = useState(props.defaultNodeType || 'TEMPLATE');
  const [select, setSelect] = useState('');
  const [isCreating, setIsCreating] = useState(false);
  const [data, setData] = useState<ExampleVO[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { trpc, trpcQuery } = useApiCaller();
  const ctx = useSpaceContextForce();
  const router = useSpaceRouter();

  const appEnv = getAppEnv();

  const menu = _.compact([
    {
      type: 'TEMPLATE',
      label: t.resource.type.folder,
      icon: <FolderAddOutlined color="var(--text-secondary)" />,
      defaultName: t.resource.title_create_folder,
      description: t.resource.create_from_blank_folder_description,
    },
    {
      type: 'DATABASE',
      label: t.resource.type.database,
      icon: <DatabaseOutlined color="var(--text-secondary)" />,
      defaultName: t.resource.title_new_database,
      description: t.resource.create_from_blank_database_description,
    },
    {
      type: 'AUTOMATION',
      label: t.resource.type.automation,
      icon: <AutomationOutlined color="var(--text-secondary)" />,
      defaultName: t.resource.title_new_automation,
      description: t.resource.create_from_blank_automation_description,
    },

    {
      type: 'FORM',
      label: t.resource.type.form,
      icon: <FormAddOutlined color="var(--text-secondary)" />,
      defaultName: t.resource.title_new_form,
    },
    {
      type: 'MIRROR',
      label: t.resource.type.mirror,
      icon: <MirrorOutlined color="var(--text-secondary)" />,
      defaultName: t.resource.title_new_mirror,
    },
    {
      type: 'DASHBOARD',
      label: t.resource.type.dashboard,
      icon: <DashboardOutlined color="var(--text-secondary)" />,
      defaultName: t.resource.title_new_dashboard,
      description: t.resource.create_from_blank_dashboard_description,
    },
    {
      type: 'DOCUMENT',
      label: t.resource.type.doc,
      icon: <FileOutlined color="var(--text-secondary)" />,
      defaultName: t.resource.title_new_document,
      description: t.resource.create_from_blank_document_description,
    },
    {
      type: 'FILE',
      label: t.resource.type.file,
      icon: <AttachmentOutlined color="var(--text-secondary)" />,
      defaultName: t.resource.title_new_file,
    },
    appEnv !== 'PRODUCTION' && {
      type: 'AI',
      label: t.resource.type.ai,
      icon: <RobotOutlined color="var(--text-secondary)" />,
      defaultName: t.resource.type.ai_description,
    },
    {
      type: 'IMPORT',
      label: t.space.import,
      icon: <ImportOutlined color="var(--text-secondary)" />,
      description: t.space.import_description,
    },
  ]);

  useEffect(() => {
    setSelect('');
    setIsLoading(true);

    if (type === 'TEMPLATE' || type === 'AUTOMATION' || type === 'DATABASE') {
      trpc.node.examples
        .query({ exampleType: type })
        .then((res) => {
          setData(res);
          setIsLoading(false);
        })
        .catch(() => {
          setData([]);
          setIsLoading(false);
        })
        .finally(() => {
          setIsLoading(false);
        });
    } else {
      setData([]);
      setIsLoading(false);
    }
  }, [type]);

  const utils = trpcQuery.useUtils();

  const renderFlag = () => (
    <Stack
      sx={{
        position: 'absolute',
        right: 0,
        bottom: 0,
        width: 0,
        height: 0,
        borderLeft: '30px solid transparent',
        borderBottom: '30px solid var(--brand)',
        zIndex: 9,
      }}
    >
      <Stack
        sx={{
          position: 'absolute',
          top: 14,
          right: 1,
        }}
      >
        <CheckOutlined />
      </Stack>
    </Stack>
  );

  const renderBox = (key: string, icon: React.ReactNode, title: string, description?: string, type?: string) => (
    // consl
    <Stack
      onClick={() => setSelect(key)}
      onDoubleClick={handleCreate}
      sx={{
        border: select === key ? '1px solid var(--brand)' : '1px solid transparent',
        height: type === 'TEMPLATE' ? '100px' : '264px',
        display: 'flex',
        background: 'var(--bg-controls)',
        borderRadius: '4px',
        alignItems: 'center',
        justifyContent: 'center',
        fledDirection: 'column',
        cursor: 'pointer',
        position: 'relative',
        ':hover': {
          background: 'var(--active)',
        },
      }}
    >
      {select === key && (
        <Stack
          sx={{
            position: 'absolute',
            right: 0,
            bottom: 0,
            width: 0,
            height: 0,
            borderLeft: '30px solid transparent',
            borderBottom: '30px solid var(--brand)',
          }}
        >
          <Stack
            sx={{
              position: 'absolute',
              top: 14,
              right: 1,
            }}
          >
            <CheckOutlined />
          </Stack>
        </Stack>
      )}
      <Stack mb={2}>
        {type === 'IMPORT' ? (
          <NodeIcon value={{ kind: 'enum', enum: 'IMPORT' }} size={16} color="var(--text-secondary)" />
        ) : (
          <NodeIcon
            value={{ kind: 'node-resource', nodeType: type as NodeResourceType }}
            size={16}
            color="var(--text-secondary)"
          />
        )}
      </Stack>
      <Typography textColor="var(--text-primary)" level="b2">
        {title}
      </Typography>
      <Typography px={3} textColor="var(--text-secondary)" level="b3">
        {description}
      </Typography>
    </Stack>
  );
  const renderContent = () => (
    // 每行2个 中间空16px
    <Stack
      sx={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fill, minmax(264px, 1fr))',
        gap: '16px',
      }}
    >
      {type === 'IMPORT' ? (
        <>
          {renderBox(
            '',
            menu.find((item) => item.type === type)?.icon,
            t.node.import_bika_file,
            menu.find((item) => item.type === type)?.description,
            type,
          )}
          {renderBox(
            'EXCEL',
            menu.find((item) => item.type === type)?.icon,
            t.resource.import_from_excel,
            menu.find((item) => item.type === type)?.description,
            type,
          )}
        </>
      ) : (
        renderBox(
          '',
          menu.find((item) => item.type === type)?.icon,
          t.resource.create_from_blank,
          menu.find((item) => item.type === type)?.description,
          type,
        )
      )}
      {(data || []).map((item, index) => {
        if (item.type === 'TEMPLATE') {
          return (
            <Stack
              onDoubleClick={handleCreate}
              key={index}
              sx={{
                border: select === item.bo.templateId ? '1px solid var(--brand)' : '1px solid transparent',
                display: 'flex',
                background: 'var(--bg-controls)',
                borderRadius: '4px',
                alignItems: 'center',
                justifyContent: 'center',
                fledDirection: 'column',
                cursor: 'pointer',
                position: 'relative',
                ':hover': {
                  background: 'var(--active)',
                },
              }}
            >
              {/* CheckOutlined */}
              {/* 如果选中状态 右下角一个三角形 里面一个CheckOutlined的icon  */}
              {select === item.bo.templateId && renderFlag()}

              <TemplateInfoCard
                onClick={() => setSelect(item.bo.templateId)}
                templateInfo={item.bo}
                backgroundColor="var(--bg-controls)"
              />
            </Stack>
          );
        }
        if (item.type === 'DATABASE') {
          return (
            <Stack
              key={index}
              sx={{
                position: 'relative',
              }}
            >
              <Stack
                onDoubleClick={handleCreate}
                onClick={() => {
                  console.log('click', item.bo.templateId);
                  setSelect(item.bo.templateId as string);
                }}
                sx={{
                  border: select === item.bo.templateId ? '1px solid var(--brand)' : '1px solid transparent',
                  background: 'var(--bg-controls)',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  height: '264px',
                  overflow: 'hidden',
                  overflowY: 'overlay',
                  overflowX: 'hidden',
                  '::-webkit-scrollbar': {
                    width: 0,
                    background: 'transparent',
                  },
                }}
              >
                {select === item.bo.templateId && renderFlag()}
                <DatabaseNode embed data={item.bo} />
              </Stack>
            </Stack>
          );
        }
        if (item.type === 'AUTOMATION') {
          return (
            <Stack
              key={index}
              sx={{
                position: 'relative',
              }}
            >
              <Stack
                onDoubleClick={handleCreate}
                onClick={() => {
                  console.log('click', item.bo.templateId);
                  setSelect(item.bo.templateId as string);
                }}
                sx={{
                  border: select === item.bo.templateId ? '1px solid var(--brand)' : '1px solid transparent',
                  background: 'var(--bg-controls)',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  height: '264px',
                  overflow: 'hidden',
                  overflowY: 'auto',
                  overflowX: 'hidden',
                  '::-webkit-scrollbar': {
                    width: 0,
                    background: 'transparent',
                  },
                  // ':hover': {
                  //   background: 'var(--active)',
                  // },
                }}
              >
                {select === item.bo.templateId && renderFlag()}
                <AutomationNode embed data={item.bo} />
              </Stack>
            </Stack>
          );
        }
        return null;
      })}
    </Stack>
  );

  async function handleCreate() {
    if (isCreating || isLoading) {
      return;
    }
    try {
      setIsCreating(true);
      if (type === 'IMPORT') {
        if (select === 'EXCEL') {
          setTimeout(() => {
            ctx.showUIModal({
              type: 'import-excel',
              folderId: props.parentId,
              importEnabled: true,
            });
          }, 0);
        } else {
          setTimeout(() => {
            ctx.showUIModal({
              type: 'import-bika-file',
              folderId: props.parentId,
              importEnabled: true,
            });
          }, 0);
        }
        ctx.showUIModal(null);
      } else if (select === '') {
        ctx.showUIDrawer({
          type: 'resource-editor',
          props: {
            screenType: 'NODE_RESOURCE',
            nodeId: 'new',
            parentId: props.parentId,
            // @ts-expect-error 不影响
            resourceType: type === 'TEMPLATE' ? 'FOLDER' : type,
            isRedirect: 'true',
          },
        });
        ctx.showUIModal(null);
      } else if (type === 'TEMPLATE') {
        await trpc.space.installTemplate
          .mutate({
            spaceId: ctx.data.id,
            templateId: select,
            parentId: props.parentId,
          })
          .then((res) => {
            toast.success(t.template.install_toast);
            utils.my.reddots.invalidate();
            utils.space.info.invalidate();
            utils.node.detail.invalidate();
            // todo 手动更新rootNode而不是调用接口
            if (window.bikaRefetchRootNode) {
              window.bikaRefetchRootNode();
            }
            setTimeout(() => {
              router.push(`/space/${ctx.data.id}/node/${res}`);
            }, 0);
            ctx.showUIModal(null);
          })
          .catch((e) => {
            if (e?.data?.code === errors.billing.usage_exceed_limit.code) {
              toast.error(e.data.message);
            } else {
              toast.error(e?.message);
            }
          });
      } else {
        // todo 这里应该用editor的创建节点方法
        const bo = (data || []).find((item) => item.bo.templateId === select)?.bo;
        if (bo) {
          await trpc.node.create
            .mutate({
              spaceId: ctx.data.id,
              parentId: props.parentId,
              // @ts-expect-error 不影响
              data: bo,
            })
            .then((res) => {
              if (window.bikaRefetchRootNode) {
                window.bikaRefetchRootNode();
              }
              setTimeout(() => {
                router.push(`/space/${ctx.data.id}/node/${res.id}`);
              }, 0);
              ctx.showUIModal(null);
            })
            .catch((e) => {
              if (e?.data?.code === errors.billing.usage_exceed_limit.code) {
                toast.error(e.data.message);
              } else {
                toast.error(e?.message);
              }
            });
        } else {
          toast.error('bo not found');
        }
      }
    } finally {
      setIsCreating(false);
    }
  }

  return (
    <Stack display="flex" sx={{ width: '953px', minWidth: '953px' }}>
      <Stack
        flex={1}
        sx={{
          maxHeight: '522px',
          minHeight: '60vh',
          display: 'flex',
          flexDirection: 'row',
        }}
      >
        <Stack
          width={224}
          maxWidth={224}
          display="flex"
          direction="column"
          sx={{
            flex: 1,
            overflow: 'auto',
            pr: 2,
            borderRight: '1px solid var(--border-default)',
          }}
        >
          {menu.map((item, index) => (
            <Stack
              sx={{
                cursor: 'pointer',
                borderRadius: '4px',
                background: type === item.type ? 'var(--active)' : '',
                ':hover': {
                  background: 'var(--active)',
                },
                ':active': {
                  background: 'var(--active)',
                },
              }}
              key={index}
              px={1}
              py={1.5}
              display="flex"
              alignItems="center"
              direction="row"
              onClick={() => {
                if (isLoading) {
                  return;
                }
                if (item.type !== type) {
                  setIsLoading(true);
                  setType(item.type);
                }
              }}
            >
              <Stack pr={1}>
                {item.type === 'IMPORT' ? (
                  <NodeIcon value={{ kind: 'enum', enum: 'IMPORT' }} size={16} />
                ) : (
                  <NodeIcon value={{ kind: 'node-resource', nodeType: item.type as NodeResourceType }} size={16} />
                )}
              </Stack>
              <Typography level="b3">{item.label}</Typography>
            </Stack>
          ))}
        </Stack>
        <Stack
          p={3}
          flex={1}
          sx={{
            overflow: 'auto',
            height: '522px',
          }}
        >
          {isLoading ? (
            <Typography level="b3">loading...</Typography>
          ) : (
            <>
              <Typography level="b3">{t.resource.create_from_template}</Typography>
              <Stack mt={2}>{renderContent()}</Stack>
            </>
          )}
        </Stack>
      </Stack>
      {/* 下结构 右侧 取消和确定按钮 */}
      <Stack pt={2} px={2} display="flex" direction="row" justifyContent="flex-end">
        <Stack>
          <Button size="lg" variant="soft" color="neutral" onClick={() => ctx.showUIModal(null)}>
            {t.action.cancel}
          </Button>
        </Stack>
        <Stack pl={2}>
          <Button disabled={isLoading} loading={isCreating} onClick={handleCreate} size="lg">
            {t.action.create}
          </Button>
        </Stack>
      </Stack>
    </Stack>
  );
}

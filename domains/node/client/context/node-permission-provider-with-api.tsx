'use client';

import React, { useEffect } from 'react';
import { useApiCaller } from '@bika/api-caller';
import { SpaceError } from '@bika/domains/space/client/error';
import { NodePermissionProvider } from '@bika/types/node/context';
import { useSpaceId } from '@bika/types/space/context';
import { Skeleton } from '@bika/ui/skeleton';
import { useTalksDB } from '../../../space/client/talks-db';

interface IProps {
  nodeId: string;
  // shareNodeTree: NodeTreeVO[];
  // sharing: boolean;
  // changeSharing?: (sharing: boolean) => void;
  // hasShareLock: boolean;
  // spaceInfo?: SpaceRenderVO;

  // validatePassword: () => void;
}

export function NodePermissionProviderWithApi({ value, children }: { value: IProps; children: React.ReactNode }) {
  const { trpcQuery, trpc } = useApiCaller();
  const {
    data: permission,
    isLoading,
    error,
  } = trpcQuery.node.permission.useQuery(
    { id: value.nodeId },
    {
      enabled: value.nodeId.length > 0,
    },
  );
  const spaceId = useSpaceId();
  const spaceTalksDB = useTalksDB(spaceId);

  useEffect(() => {
    if (error?.message === 'Node not found') {
      trpc.talk.removeTalk.mutate({
        spaceId,
        talkId: `node_${value.nodeId}`,
      });

      spaceTalksDB.delete(`node_${value.nodeId}`).then(() => {
        window.dispatchEvent(
          new CustomEvent('localStorageChange', {
            detail: { key: 'LAST_TIME_UPDATE_TALK', value: Date.now().toString() },
          }),
        );
      });
    }
  }, [error]);

  if (permission && !isLoading && !permission.privilege.abilities.readNode) {
    return <SpaceError errorType={'NeedRequestPermission'} />;
  }

  return (
    <>
      {permission && !isLoading ? (
        <NodePermissionProvider
          value={{
            isPublicAccess: permission.isPublicAccess,
            nodeId: value.nodeId,
            privilege: permission.privilege,
            sharing: permission.sharing,
            sharePassword: permission.sharePassword,
            needLogin: false,
            shareScope: permission.shareScope,
          }}
        >
          {children}
        </NodePermissionProvider>
      ) : (
        <Skeleton pos="NODE_PERMISSION_PROVIDER" />
      )}
    </>
  );
}

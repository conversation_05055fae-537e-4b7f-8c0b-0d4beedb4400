/* eslint-disable max-lines */

'use client';

import Button from '@mui/joy/Button';
import React, { useMemo, useEffect } from 'react';
import { TemplateCategoryStringConfig } from '@bika/contents/config/client';
import { useLocale } from '@bika/contents/i18n/context';
import { NodeIcon } from '@bika/domains/node/client';
import { ResponsiveDisplay } from '@bika/domains/shared/client/components/responsive-display/index';
import type { NodeResource } from '@bika/types/node/bo';
import type { NodeTreeVO, StoreTemplateAuthorVO } from '@bika/types/node/vo';
import { NodePrivilegeVO } from '@bika/types/permission/vo';
import type { AvatarLogo, iString } from '@bika/types/system';
import { AvatarImg, AvatarSize } from '@bika/ui/components/avatar/index';
import { PermissionTag } from '@bika/ui/components/permission-tag/index';
import { jsonToText, textTo<PERSON><PERSON> } from '@bika/ui/editor/rich-text-editor/index';
import { useUIFrameworkContext } from '@bika/ui/framework/context';
import ArrowLeftFilled from '@bika/ui/icons/components/arrow_left_filled';
import ChevronRightOutlined from '@bika/ui/icons/components/chevron_right_outlined';
import DownloadOutlined from '@bika/ui/icons/components/download_outlined';
import StarFilled from '@bika/ui/icons/components/star_filled';
import StarOutlined from '@bika/ui/icons/components/star_outlined';
import CertificationOutlined from '@bika/ui/icons/doc_hide_components/certification_outlined';
import { Stack } from '@bika/ui/layouts';
import { Markdown } from '@bika/ui/markdown';
import { ModalComponent } from '@bika/ui/modal';
import { EllipsisText } from '@bika/ui/text';
import { Typography } from '@bika/ui/texts';
import { Tooltip } from '@bika/ui/tooltip';

export interface Preview {
  architecture?: (templateId: string, inline?: boolean, onClose?: () => void) => React.ReactNode;
  releaseNotes?: (templateId: string) => React.ReactNode;
  node?: (templateId: string, nodeId: string) => React.ReactNode;
}

interface Props {
  // 隐藏节点信息
  hiddenIncludeResources?: boolean;
  // 隐藏版本历史吗
  hiddenReleaseNotes?: boolean;

  ignoreBG?: boolean;
  templateId?: string;
  title: string;
  visibility?: string;
  verified?: boolean;
  description?: string;
  version?: string;
  author?: StoreTemplateAuthorVO;
  cover?: AvatarLogo;
  category?: string[];
  readme?: iString;

  permission?: NodePrivilegeVO;

  // node tree, without resource detail
  childNodes?: NodeTreeVO[];

  useWorkflowResources?: () => {
    data: NodeResource[] | undefined;
    isLoading: boolean;
    refetch: () => void;
  };

  emptyResources?: React.ReactNode;
  showInstall?: boolean;
  onInstall?: (isComingSoon?: boolean) => void;
  onClickNode?: (nodeId: string) => void;
  star?: {
    count?: number;
    isStarred?: boolean;
    onStar: () => Promise<boolean>;
  };
  // 内联显示tab
  inline?: boolean;
  preview?: Preview;
}

/**
 * 与NodeFolder和NodeTemplateFolder相比，这个没有header，并被外部template center和modal复用
 *
 * @param props
 * @returns
 */
export function FolderRenderer(props: Props) {
  const {
    ignoreBG,
    templateId,
    category,
    title,
    verified,
    visibility,
    version,
    author,
    description,
    cover,
    readme,
    childNodes,
    showInstall,
    onInstall,
    onClickNode,
    inline,
    permission,
  } = props;

  // const ctx = useGlobalContext();
  const frameworkContext = useUIFrameworkContext();
  const { t, i: iStr } = useLocale();
  const [preview, _setPreview] = React.useState<React.ReactNode>(null);
  const [previewName, setPreviewName] = React.useState('');
  const [color, setColor] = React.useState<number[]>([]);
  const [openModal, setOpenModal] = React.useState(false);
  const [isFavorite, setIsFavorite] = React.useState(props.star?.isStarred || false);
  const { i } = useLocale();

  useEffect(() => {
    if (ignoreBG) {
      return;
    }
    if (cover && cover.type === 'URL') {
      const img = document.createElement('img');
      img.src = cover.url;

      img.onload = () => {
        const canvas = document.createElement('canvas');
        canvas.width = img.width;
        canvas.height = img.height;
        const canvasCtx = canvas.getContext('2d');
        if (canvasCtx) {
          canvasCtx.drawImage(img, 0, 0, img.width, img.height);

          const imageData = canvasCtx.getImageData(0, 0, img.width, img.height);
          const data = imageData.data;
          let r = 0;
          let g = 0;
          let b = 0;

          for (let i = 0; i < data.length; i += 4) {
            r += data[i];
            g += data[i + 1];
            b += data[i + 2];
          }

          r = Math.floor(r / (data.length / 4));
          g = Math.floor(g / (data.length / 4));
          b = Math.floor(b / (data.length / 4));
          setColor([r, g, b]);
          // document.body.style.background = `linear-gradient(to bottom, rgba(${r}, ${g}, ${b}, 0.7), rgba(${r}, ${g}, ${b}, 0.3))`;
        }
        img.remove();
      };
    }
  }, []);

  const setPreview = (tab: React.ReactNode) => {
    if (tab) {
      if (!inline) {
        setOpenModal(true);
      }
    } else {
      setOpenModal(false);
    }
    _setPreview(tab);
  };

  const data = useMemo(() => {
    if (childNodes) {
      return childNodes.map((node) => ({
        id: node.id,
        name: node.name,
        icon: node.icon,
        description: node.description || '',
        resourceType: node.type,
        templateId: node.templateId,
      }));
    }
    if (props.useWorkflowResources) {
      const { data: architecture } = props.useWorkflowResources();
      return (architecture || []).map((node) => ({
        id: node.id,
        name: node.name,
        icon: node.icon,
        description: node.description || '',
        resourceType: node.resourceType,
        templateId: node.templateId,
      }));
    }
    return [];
  }, [childNodes]);

  const isAllowClickNode = onClickNode || props.preview?.node;

  const renderResources = () => {
    if (data.length === 0) {
      return props.emptyResources || null;
    }
    return (
      <>
        <Stack mb={2}>
          <Typography mb={1} level={'h8'}>
            {t.resource.included_resources}
          </Typography>
          <Stack gap={1} overflow="auto">
            {data.map((node, index) => (
              <Stack
                p={1.5}
                key={index}
                direction={'row'}
                alignItems={'center'}
                sx={{
                  backgroundColor: 'var(--bg-controls)',
                  border: '1px solid var(--border-default)',
                  borderRadius: '8px',
                  ':hover': {
                    backgroundColor: isAllowClickNode && 'var(--bg-controls-hover)',
                  },
                  ':active': {
                    backgroundColor: isAllowClickNode && 'var(--bg-controls-active)',
                  },
                  cursor: isAllowClickNode ? 'pointer' : 'default',
                }}
                onClick={() => {
                  if (onClickNode) {
                    onClickNode((node.id || node.templateId) as string);
                  } else if (props.preview?.node && (node.id || node.templateId)) {
                    const ele = props.preview.node(templateId as string, (node.id || node.templateId) as string);
                    setPreview(ele);
                    setPreviewName(iStr(node.name));
                  }
                }}
              >
                <Stack mr={2} width={32} height={32} flexShrink={0}>
                  <NodeIcon
                    title={i(node.name)}
                    value={{ kind: 'node-resource', nodeType: node.resourceType, customIcon: node.icon }}
                    color="var(--static)"
                  />
                </Stack>
                <Stack flex={1} overflow="hidden">
                  <Typography textColor="var(--text-primary)" level={'b2'}>
                    {iStr(node.name)}
                  </Typography>
                  <EllipsisText>
                    <Typography textColor="var(--text-secondary)" level={'b4'}>
                      {jsonToText(textToJson(iStr(node.description)))}
                    </Typography>
                  </EllipsisText>
                </Stack>
                {isAllowClickNode && (
                  <Stack ml={1} width={16}>
                    <ChevronRightOutlined color={'var(--text-secondary)'} />
                  </Stack>
                )}
              </Stack>
            ))}
          </Stack>
        </Stack>

        {props.useWorkflowResources && (
          <Stack mb={2}>
            <Typography mb={1} level={'h8'}>
              {t.template.architecture}
            </Typography>
            <Stack gap={1} overflow="auto">
              <Stack
                p={1.5}
                direction={'row'}
                alignItems={'center'}
                sx={{
                  backgroundColor: 'var(--bg-controls)',
                  border: '1px solid var(--border-default)',
                  borderRadius: '8px',
                  ':hover': {
                    backgroundColor: 'var(--bg-controls-hover)',
                  },
                  ':active': {
                    backgroundColor: 'var(--bg-controls-active)',
                  },
                  cursor: 'pointer',
                }}
                onClick={() => {
                  if (props.preview?.architecture) {
                    const ele = props.preview.architecture(templateId as string, props.inline, () => {
                      setPreview(null);
                    });
                    setPreview(ele);
                    setPreviewName(t.template.architecture);
                  }
                }}
              >
                <Stack mr={2} width={32} height={32} flexShrink={0}>
                  <NodeIcon value={{ kind: 'enum', enum: 'workflow' }} color="var(--static)" />
                </Stack>
                <Stack flex={1} overflow="hidden">
                  <Typography textColor={'var(--text-primary)'} level={'b2'}>
                    {t.template.architecture}
                  </Typography>
                  <Typography textColor="var(--text-secondary)" level={'b4'}>
                    {t('template.architecture_description', {
                      name: title,
                    })}
                  </Typography>
                </Stack>
                <Stack ml={1} width={16}>
                  <ChevronRightOutlined color={'var(--text-secondary)'} />
                </Stack>
              </Stack>
            </Stack>
          </Stack>
        )}
        {templateId && !props.hiddenReleaseNotes && (
          <Stack mb={2}>
            <Typography mb={1} level={'h8'}>
              {t.template.release_notes}
            </Typography>
            <Stack gap={1} overflow="auto">
              <Stack
                p={1.5}
                direction={'row'}
                alignItems={'center'}
                sx={{
                  backgroundColor: 'var(--bg-controls)',
                  borderRadius: '4px',
                  ':hover': {
                    backgroundColor: 'var(--bg-controls-hover)',
                  },
                  ':active': {
                    backgroundColor: 'var(--bg-controls-active)',
                  },
                  cursor: 'pointer',
                }}
                onClick={() => {
                  if (props.preview?.releaseNotes) {
                    const ele = props.preview.releaseNotes(templateId);
                    setPreview(ele);
                    setPreviewName(t.template.release_notes);
                  }
                }}
              >
                <Stack mr={2} width={32} height={32} flexShrink={0}>
                  <NodeIcon value={{ kind: 'enum', enum: 'release_notes' }} color="var(--static)" />
                </Stack>
                <Stack flex={1} overflow="hidden">
                  <Typography level={'b2'}>{t.template.release_notes}</Typography>
                  <Typography textColor="var(--text-secondary)" level={'b4'}>
                    {t('template.release_notes_description', {
                      name: title,
                    })}
                  </Typography>
                </Stack>
                <Stack ml={1} width={16}>
                  <ChevronRightOutlined />
                </Stack>
              </Stack>
            </Stack>
          </Stack>
        )}
      </>
    );
  };

  const renderVerified = () => {
    if (verified) {
      return (
        <Tooltip title={t.template.official_certification} variant="solid" arrow color="neutral" placement="top">
          <Stack direction="row" ml={1}>
            <CertificationOutlined size={16} color={'var(--brand)'} />
          </Stack>
        </Tooltip>
      );
    }
  };

  const renderVersion = () => {
    if (version && visibility !== 'WAITING_LIST') {
      return (
        <Stack
          sx={{
            padding: '0 4px',
            backgroundColor: 'var(--bg-controls)',
            color: 'var(--text-secondary)',
            borderRadius: '4px',
          }}
        >
          <Typography level={'b4'}>v{version}</Typography>
        </Stack>
      );
    }
  };

  const renderComingSoon = () => {
    if (visibility === 'WAITING_LIST') {
      return (
        <Tooltip title={t.template.coming_soon_tooltip} variant="solid" arrow color="neutral" placement="top">
          <Typography
            sx={{
              padding: '0 4px',
              backgroundColor: 'var(--bg-controls)',
              color: 'var(--text-secondary)',
              borderRadius: '4px',
            }}
            level="b4"
            textColor="var(--text-primary)"
          >
            Coming soon
          </Typography>
        </Tooltip>
      );
    }
  };

  const renderAuthor = () => {
    if (author) {
      return (
        <Stack mb={1} alignItems="center" direction="row">
          <AvatarImg customSize={AvatarSize.Size20} name={author.name ?? ''} avatar={author.avatar} />
          <Typography
            level={'b4'}
            fontSize={'12px'}
            sx={{
              color: 'var(--text-secondary)',
              ml: 1,
            }}
          >
            {/* 正则过滤 <email> */}
            {author.name.replace(/<[^>]+>/g, '')}
          </Typography>
        </Stack>
      );
    }
  };

  const renderDescription = () => (
    <Stack
      mb={1}
      sx={{
        color: 'var(--text-secondary)',
        display: '-webkit-box',
        overflow: 'hidden',
        WebkitBoxOrient: 'vertical',
        WebkitLineClamp: 2,
      }}
    >
      <Typography level={'b2'}>{description}</Typography>
    </Stack>
  );

  const renderCategory = () => {
    if (category) {
      return (
        <Stack mb={2} spacing={2} flexShrink={0} direction={'row'}>
          {category.map((item, index) => (
            <Stack
              key={index}
              px={1}
              py={0.4}
              borderRadius={4}
              sx={{
                border: '1px solid var(--border-default)',
                // backgroundColor: 'var(--bg-controls)',
              }}
            >
              {/* @ts-expect-error */}
              <Typography level={'b4'}>{iStr(TemplateCategoryStringConfig[item]?.name) || item}</Typography>
            </Stack>
          ))}
        </Stack>
      );
    }
  };

  if (inline && preview) {
    return (
      <Stack
        sx={{
          height: '100%',
        }}
      >
        {/* header 左 中 右 flex */}
        <Stack
          sx={{
            borderBottom: '1px solid var(--border-default)',
          }}
          p={2}
          direction="row"
          justifyContent="space-between"
          alignItems="center"
        >
          <Stack
            sx={{ cursor: 'pointer' }}
            onClick={() => {
              setPreview(null);
            }}
          >
            <ArrowLeftFilled />
          </Stack>
          <Stack>
            {iStr(title)} - {previewName}
          </Stack>
          <Stack pr={9}>
            {showInstall && onInstall && (
              <Button
                fullWidth={false}
                onClick={() => onInstall(visibility === 'WAITING_LIST')}
                // 实际宽度
                sx={{ flexShrink: 0, width: 'max-content' }}
                startDecorator={<DownloadOutlined />}
              >
                {t.template.get}
              </Button>
            )}
          </Stack>
        </Stack>
        <Stack
          sx={{
            overflow: 'auto',
            height: '100%',
            width: '100%',
          }}
          p={2}
        >
          {preview}
        </Stack>
      </Stack>
    );
  }

  return (
    // p-4 space-y-4 flex flex-col
    <>
      <Stack
        display="flex"
        direction={'column'}
        sx={{
          height: '100%',
          background: 'var(--bg-surface)',
        }}
      >
        <Stack
          p={frameworkContext.isMobile ? 1 : 3}
          display="flex"
          direction={'row'}
          // alignItems="center"
          sx={{
            background: color.length
              ? `linear-gradient(to bottom, rgba(${color.join(',')}, 0.3), var(--bg-surface))`
              : undefined,
          }}
        >
          <Stack
            width={180}
            height={180}
            mr={frameworkContext.isMobile ? 1 : 3}
            overflow={'hidden'}
            position={'relative'}
            flexShrink={0}
          >
            <AvatarImg
              shape={'SQUARE'}
              customSize={AvatarSize.Size180}
              name={title}
              avatar={cover || { type: 'COLOR', color: 'BLUE' }}
            />
          </Stack>
          <Stack display="flex" direction={'column'}>
            <Stack mb={0.5} display="flex" direction="row" alignItems="center" spacing={1}>
              <div className="flex items-center space-x-1">
                <Typography textColor={'var(--text-primary)'} level={'h5'}>
                  {title}
                </Typography>
                {!templateId && <PermissionTag permission={permission?.privilege} />}
              </div>
              <ResponsiveDisplay>
                <>
                  {renderVersion()}
                  {renderVerified()}
                  {renderComingSoon()}
                </>
              </ResponsiveDisplay>
            </Stack>
            <ResponsiveDisplay mobile>
              <Stack direction="row" alignItems="center" spacing={1}>
                {renderVersion()}
                {renderVerified()}
                {renderComingSoon()}
              </Stack>
            </ResponsiveDisplay>
            <ResponsiveDisplay>{renderAuthor()}</ResponsiveDisplay>
            {renderDescription()}
            <ResponsiveDisplay>{renderCategory()}</ResponsiveDisplay>
            <Stack direction={'row'} spacing={2}>
              {showInstall && onInstall && (
                <Button
                  fullWidth={false}
                  onClick={() => onInstall(visibility === 'WAITING_LIST')}
                  // 实际宽度
                  sx={{ flexShrink: 0, width: 'max-content' }}
                  startDecorator={<DownloadOutlined />}
                >
                  {t.template.get}
                </Button>
              )}
              {/* 收藏 */}
              {props.star?.onStar && (
                <Button
                  variant="plain"
                  fullWidth={false}
                  onClick={async () => {
                    if (!props.star?.isStarred && props.star?.onStar) {
                      const ret = await props.star.onStar();
                      if (ret) setIsFavorite(true);
                    }
                  }}
                  sx={{
                    flexShrink: 0,
                    width: 'max-content',
                    color: isFavorite ? 'var(--rainbow-orange5)' : 'var(--text-primary)',
                    background: 'var(--bg-controls)',
                  }}
                  startDecorator={
                    isFavorite ? (
                      <StarFilled color={'var(--rainbow-orange5)'} />
                    ) : (
                      <StarOutlined color="var(--text-primary)" />
                    )
                  }
                >
                  {`${t.template.favorite}(${isFavorite ? (props.star?.count || 0) + 1 : props.star?.count})`}
                </Button>
              )}
            </Stack>
          </Stack>
        </Stack>
        <ResponsiveDisplay mobile>
          <Stack direction={'column'} spacing={2} py={1} px={2}>
            {renderAuthor()}
            {renderCategory()}
          </Stack>
        </ResponsiveDisplay>
        <Stack
          sx={{
            background: 'var(--bg-surface)',
          }}
          p={frameworkContext.isMobile ? 1 : 3}
          pt={0}
          flex={1}
          width={'100%'}
          display="flex"
          direction={'row'}
        >
          {/* 左included resources */}
          <Stack
            direction={'row'}
            sx={{
              overflowX: 'hidden',
              flex: 1,
              width: '100%',
            }}
          >
            <Stack
              display={props.hiddenIncludeResources || frameworkContext.isMobile ? 'none' : 'block'}
              mr={2}
              sx={{
                width: readme ? '30%' : '100%',
                minHeight: '100%',
                minWidth: readme ? 320 : '100%',
              }}
            >
              {renderResources()}
            </Stack>

            {readme && (
              <Stack mb={2} flex={1}>
                <Markdown markdown={iStr(readme)} />
              </Stack>
            )}
          </Stack>
        </Stack>
      </Stack>
      {openModal && (
        <ModalComponent
          minHeight={600}
          minWidth={800}
          title={`${iStr(title)} - ${previewName}`}
          disableEscapeKeyDown={true}
          // className={style.modal}
          onClose={(e, reason) => {
            setOpenModal(false);
          }}
          closable={true}
        >
          {preview}
        </ModalComponent>
      )}
    </>
  );
}

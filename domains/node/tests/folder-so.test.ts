import assert from 'assert';
import fs from 'fs';
import path from 'path';
import { generateNanoID } from 'basenext/utils/nano-id';
import { sleep } from 'sharelib/sleep';
import { uploadFileToUrl } from 'sharelib/upload-file-node';
import { describe, expect, test } from 'vitest';
import { MockContext } from '@bika/domains/__tests__/mock';
import { AttachmentSO } from '@bika/domains/attachment/server';
import { TmpAttachmentSO } from '@bika/domains/attachment/server/tmp-attachment-so';
import { db } from '@bika/server-orm';
import { Attachment, Database, MultiSelect, SingleText } from '@bika/types/database/bo';
import { Folder, NodeResource } from '@bika/types/node/bo';
import { defaultTemplate } from '@bika/types/utils';
import { AdminDebugSO } from '../../admin/server/admin-debug-so';
import { AISearchSO } from '../../ai/server/ai-search-so';
import { DatabaseSO } from '../../database/server/database-so';
import { FieldSO } from '../../database/server/fields/field-so';
import { ViewSO } from '../../database/server/views/view-so';
import { FormSO } from '../../form/server/form-so';
import { StoreTemplateSO } from '../../store/server/store-template-so';
import { TemplateRepoSO } from '../../template/server';
import { FolderSO } from '../server/folder-so';
import { NodeSO } from '../server/node-so';

describe('folder create children test', () => {
  test('test create database node with name', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    // 创建一个文件夹节点
    const folderNode = await rootFolder.createFolderNode({ userId: user.id }, 'create a folder', undefined, undefined, {
      cover: {
        type: 'COLOR',
        color: 'BLUE',
      },
    });

    // 有封面字段，为空
    expect(folderNode.cover).toStrictEqual({ type: 'COLOR', color: 'BLUE' });

    // 在folder下创建数据库节点
    const name = 'new database';
    const databaseNodeSO = await folderNode.createChildSimple(user, {
      name,
      resourceType: 'DATABASE',
    });
    expect(databaseNodeSO).toBeDefined();
    expect(databaseNodeSO.name).toBe(name);
    const children = await folderNode.getChildren();
    expect(children).toHaveLength(1);
  });

  test('test create database node without name', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    // 创建一个文件夹节点
    const folderNode = await rootFolder.createFolderNode({ userId: user.id }, 'folder');
    // 在folder下创建数据库节点
    const dataBaseNodeSO = await folderNode.createChildSimple(user, { name: 'New Database', resourceType: 'DATABASE' });
    expect(dataBaseNodeSO).toBeDefined();
    // 检查默认创建的名称
    expect(dataBaseNodeSO.name).toBe('New Database');
    const children = await folderNode.getChildren();
    expect(children).toHaveLength(1);
  });

  test('test create database should init three records and fields', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    // 在folder下创建数据库节点
    const node = await rootFolder.createChildSimple(
      user,
      {
        resourceType: 'DATABASE',
        name: 'New Database',
      },
      { createDefaultRecords: true },
    );
    const databaseSO = await node.toResourceSO<DatabaseSO>();
    const views: ViewSO[] = await databaseSO.getViews();
    const fields: FieldSO[] = databaseSO.getFields();
    const records = await databaseSO.getRecordsAsPage();
    expect(views.length).toBe(1);
    expect(fields.length).toBe(3);
    expect(records.length).toBe(3);
    const fieldsType = fields.map((f) => f.type);
    expect(fieldsType).toStrictEqual([SingleText.value, MultiSelect.value, Attachment.value]);
  });
  test('test create folder node with name', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    // 创建一个文件夹节点
    const folderNode = await rootFolder.createFolderNode({ userId: user.id }, 'folder');
    // 在folder下创建文件夹节点
    const name = 'new folder';
    const folderNodeSO = await folderNode.createFolderNode({ userId: user.id }, name);
    expect(folderNodeSO).toBeDefined();
    expect(folderNodeSO.name).toBe(name);
    const children = await folderNode.getChildren();
    expect(children).toHaveLength(1);
  });
  test('create a folder with children', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    const folderId = await rootFolder.createChildren(user, [
      {
        resourceType: 'FOLDER',
        name: 'test',
        templateId: 'folder',
        children: defaultTemplate.resources,
      },
    ]);

    const folderSO = await FolderSO.init(folderId);
    const bo = await folderSO.toBORecursive();
    expect(bo.children!.length).toBe(defaultTemplate.resources.length);
  });

  test('create a folder with b2b', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    const storeTemplate = await StoreTemplateSO.init('crm-b2b-sales');
    const template = await storeTemplate.getTemplate();
    const resource: Folder = {
      resourceType: 'FOLDER',
      name: 'test',
      children: template.resources,
    };

    const folderId = await rootFolder.createChildren(user, [resource]);
    const folderSO = await FolderSO.init(folderId);
    const bo = await folderSO.toBORecursive();
    expect(bo.children!.length).toBe(template.resources.length);
    expect(folderSO.parentId).toBe(rootFolder.id);
    const findResourceById = (id: string): NodeResource | undefined => resource.children?.find((i) => i.id === id);
    for (const child of await folderSO.getChildren()) {
      if (child.isDatabase) {
        const databaseSO = await child.toResourceSO<DatabaseSO>();
        const database = findResourceById(databaseSO.id) as Database;
        if (database.views) {
          expect(database!.views!.length).toEqual((await databaseSO.getViews()).length);
        }
        expect(database.records!.length).toEqual((await databaseSO.getRecordsAsPage()).length);
      }
    }
  });

  test('export bikafile', async () => {
    const resources: Folder[] = JSON.parse(fs.readFileSync(path.join(__dirname, 'test-resources.json'), 'utf8'));
    const { rootFolder } = await MockContext.initUserContext();
    const url = await rootFolder.toNodeSO().exportBikafileAndGetDownloadUrl({
      format: 'RESOURCES',
      resources,
    });
    expect(url).not.toBeNull();
  });

  test('create a folder with b2b folder resource', async () => {
    const resources: Folder[] = JSON.parse(
      fs.readFileSync(path.join(__dirname, 'test-expression-resources.json'), 'utf8'),
    );
    const { user, rootFolder, space } = await MockContext.initUserContext();
    const folderId = await rootFolder.createChildren(user, resources);
    expect(folderId).not.toBeNull();

    const findResourceById = (id: string): NodeResource | undefined => {
      const children = resources.reduce<NodeResource[]>((pre, cur) => {
        pre.push(...cur.children!);
        return pre;
      }, []);
      return children.find((resource) => resource.id === id);
    };

    const folderSO = await FolderSO.init(folderId);
    for (const child of await folderSO.getChildren()) {
      if (child.isDatabase) {
        const databaseSO = await child.toResourceSO<DatabaseSO>();
        const database = findResourceById(databaseSO.id) as Database;
        expect(database!.views!.length).toEqual((await databaseSO.getViews()).length);
        expect(database.records!.length).toEqual((await databaseSO.getRecordsAsPage()).length);
      }
    }

    // publish
    const templateId = await folderSO.publish(user, {
      type: 'TEMPLATE_CENTER',
      data: {
        category: 'sales',
        visibility: 'PUBLIC',
        detach: true,
        version: '0.0.1',
        templateId: generateNanoID('tps'),
      },
    });
    // install
    const templateFolder = await space.installTemplateById(user, templateId);
    expect(templateFolder.id).toBeDefined();
  });

  test('import select and attachment field', async () => {
    const resources: Folder[] = JSON.parse(fs.readFileSync(path.join(__dirname, 'resources.json'), 'utf8'));
    const { rootFolder, user } = await MockContext.initUserContext();
    const folderId = await rootFolder.createChildren(user, resources);
    expect(folderId).not.toBeNull();
  });

  test('create from export bo -- database and form', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    const folder = await rootFolder.createChildSimple(user, {
      resourceType: 'FOLDER',
      name: 'folder',
    });
    const folderSO = await folder.toResourceSO<FolderSO>();
    const database = await folderSO.createChildSimple(user, {
      resourceType: 'DATABASE',
      name: 'database',
    });
    const databaseSO = await database.toResourceSO<DatabaseSO>();
    const views = await databaseSO.getViews();
    await folderSO.createChildSimple(user, {
      resourceType: 'FORM',
      name: 'form',
      formType: 'DATABASE',
      databaseId: databaseSO.id,
      viewId: views[0].id,
    });
    const exportBO = await (await FolderSO.init(folder.id)).export();

    const newFolderId = await rootFolder.createChildren(user, [exportBO]);
    const newFolder = await FolderSO.init(newFolderId);
    const children = await newFolder.getChildren();
    const newDatabaseSO = await DatabaseSO.init(children[1].id);
    const newViews = await newDatabaseSO.getViews();
    const newForm = await FormSO.init(children[0].id);
    expect(newForm.databaseId).toBe(newDatabaseSO.id);
    expect(newForm.viewId).toBe(newViews[0].id);
  });

  test('folder create children index', async () => {
    const { user, space, rootFolder } = await MockContext.initUserContext();
    await rootFolder.createChildren(user, [
      {
        resourceType: 'FOLDER',
        name: 'folder',
        children: [
          {
            resourceType: 'FOLDER',
            name: 'folder-folder',
            children: [
              {
                resourceType: 'FOLDER',
                name: 'folder-folder-folder',
              },
              {
                resourceType: 'DATABASE',
                name: {
                  en: 'folder-folder-database',
                  'zh-CN': '文件夹-文件夹-数据库',
                },
              },
            ],
          },
        ],
      },
    ]);
    await sleep(2000);
    const data = await AISearchSO.searchNodesResources(user, space, { query: 'database' });
    assert(data[0].type === 'NODE_MENU');
    expect(data[0].text).toBe('folder-folder-<em>database</em>');
    expect(data[0].highlight).toStrictEqual('folder-folder-<em>database</em> 文件夹-文件夹-数据库');
  });
});

describe('folder children test', () => {
  test('test node children should be sorted', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    // 创建子节点，节点顺序: E -> D -> C -> B -> A
    const folderNodeA = await rootFolder.createFolderNode({ userId: user.id }, 'A');
    const folderNodeB = await rootFolder.createFolderNode({ userId: user.id }, 'B');
    const folderNodeC = await rootFolder.createFolderNode({ userId: user.id }, 'C');
    const folderNodeD = await rootFolder.createFolderNode({ userId: user.id }, 'D');
    const folderNodeE = await rootFolder.createFolderNode({ userId: user.id }, 'E');
    // 获取子节点,里面自动重新加载children节点,并且子节点都是有序的
    const children = await rootFolder.getChildren();
    // 验证子节点顺序
    expect(children).toHaveLength(5);
    expect(children[0].id).toBe(folderNodeE.id);
    expect(children[1].id).toBe(folderNodeD.id);
    expect(children[2].id).toBe(folderNodeC.id);
    expect(children[3].id).toBe(folderNodeB.id);
    expect(children[4].id).toBe(folderNodeA.id);
  });
  test('get all children test', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    // 创建一个文件数, 顶层文件夹 -> 二级文件夹 -> 三级文件夹 -> 四级文件夹
    const topFolderSO = await rootFolder.createFolderNode({ userId: user.id }, 'folder-1');
    const twoFolderSO = await topFolderSO.createFolderNode({ userId: user.id }, 'folder-1-1');
    const threeFolderSO = await twoFolderSO.createFolderNode({ userId: user.id }, 'folder-1-1-1');
    const fourFolderSO = await threeFolderSO.createFolderNode({ userId: user.id }, 'folder-1-1-1-1');
    // 递归获取所有子节点
    const allChildren = await rootFolder.getAllChildren();
    expect(allChildren.length).toBe(4);
    // 有序判断
    expect(allChildren[0].id).toBe(topFolderSO.id);
    expect(allChildren[1].id).toBe(twoFolderSO.id);
    expect(allChildren[2].id).toBe(threeFolderSO.id);
    expect(allChildren[3].id).toBe(fourFolderSO.id);
  });
  test('test traverse node tree vo', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    // 创建一个文件数, 顶层文件夹 -> 二级文件夹 -> 三级文件夹 -> 四级文件夹
    const topFolderSO = await rootFolder.createFolderNode({ userId: user.id }, 'folder-1');
    const twoFolderSO = await topFolderSO.createFolderNode({ userId: user.id }, 'folder-1-1');
    const threeFolderSO = await twoFolderSO.createFolderNode({ userId: user.id }, 'folder-1-1-1');
    const fourFolderSO = await threeFolderSO.createFolderNode({ userId: user.id }, 'folder-1-1-1-1');
    // 根节点结构
    // const rootTreeVO = await rootFolder.toNodeSO().toTreeVO();
    // 只加载两级节点树试试
    const partialTreeVO = await rootFolder.toVO({ userId: user.id });
    // console.log(`partial tree vo: ${JSON.stringify(partialTreeVO)} `);
    expect(partialTreeVO).toBeDefined();
    let expectTopFolderChildVO = partialTreeVO.children![0];
    expect(expectTopFolderChildVO.id).toBe(topFolderSO.id);
    let expectTwoFolderChildVO = expectTopFolderChildVO.children![0];
    expect(expectTwoFolderChildVO.id).toBe(twoFolderSO.id);
    expect(expectTwoFolderChildVO.children).toHaveLength(0);

    // 加载全部子节点树
    const fullTreeVO = await rootFolder.toVO({ depth: 4, userId: user.id });
    // console.log(`full tree vo: ${JSON.stringify(fullTreeVO)}`);
    expectTopFolderChildVO = fullTreeVO.children![0];
    expect(expectTopFolderChildVO.id).toBe(topFolderSO.id);
    expectTwoFolderChildVO = expectTopFolderChildVO.children![0];
    expect(expectTwoFolderChildVO.id).toBe(twoFolderSO.id);
    const expectThreeFolderChildVO = expectTwoFolderChildVO.children![0];
    expect(expectThreeFolderChildVO.id).toBe(threeFolderSO.id);
    const expectFourFolderChildVO = expectThreeFolderChildVO.children![0];
    expect(expectFourFolderChildVO.id).toBe(fourFolderSO.id);
  });

  test('test load children node tree vo only', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    // 创建一个文件数, 顶层文件夹 -> 二级文件夹 -> 三级文件夹 -> 四级文件夹
    const topFolderSO = await rootFolder.createFolderNode({ userId: user.id }, 'folder-1');
    const twoFolderSO = await topFolderSO.createFolderNode({ userId: user.id }, 'folder-1-1');
    const threeFolderSO = await twoFolderSO.createFolderNode({ userId: user.id }, 'folder-1-1-1');
    await threeFolderSO.createFolderNode({ userId: user.id }, 'folder-1-1-1-1');
    // 只加载两级节点树试试
    const treeVO = await rootFolder.toVO({ depth: 1, userId: user.id });
    const childrenVO = treeVO.children;
    expect(childrenVO).toHaveLength(1);
    const expectTopFolderChildVO = childrenVO![0];
    expect(expectTopFolderChildVO.id).toBe(topFolderSO.id);
    expect(expectTopFolderChildVO.children).toHaveLength(0);
  });

  test('test difference node to vo', async () => {
    const { user, space, rootFolder } = await MockContext.initUserContext();
    const userId = user.id;

    // ======== 下面是按序创建各种类型节点 =========

    // 安装一个模板节点
    const templateId = '@vika/scrum-standup';
    const templateFolder = await space.installTemplateById(user, templateId);

    // 新建一个文件夹节点
    const folderNodeSO = await rootFolder.createFolderNode({ userId }, 'test folder', templateFolder.id);
    // 在文件夹下面随意创建几个节点,但是不去获取
    await folderNodeSO.createFolderNode({ userId }, 'test folder - child 1');
    await folderNodeSO.createFolderNode({ userId }, 'test folder - child 2');
    await folderNodeSO.createFolderNode({ userId }, 'test folder - child 3');

    // 新建一个数据表节点
    const databaseNodeSO = await rootFolder.createChildSimple(user, {
      name: 'A',
      resourceType: 'DATABASE',
      fields: [
        {
          name: 'Name',
          type: 'LONG_TEXT',
        },
        {
          name: 'Age',
          type: 'NUMBER',
          property: {},
        },
      ],
    });

    await databaseNodeSO.move(user, { preNodeId: folderNodeSO.id });

    await rootFolder.reloadChildren();

    // 获取节点树,然后toVO()生成统一的vo
    const rootTreeVO = await rootFolder.toVO({ depth: 1, userId: user.id });
    expect(rootTreeVO).toBeDefined();
    // console.log(`root tree vo: ${JSON.stringify(rootTreeVO)}`);
    const expectTemplateFolderVO = rootTreeVO.children![0];
    expect(expectTemplateFolderVO.id).toBe(templateFolder.id);

    const expectFolderNodeVO = rootTreeVO.children![1];
    expect(expectFolderNodeVO.id).toBe(folderNodeSO.id);
    expect(expectFolderNodeVO.children).toHaveLength(0);

    const expectDatabaseNodeVO = rootTreeVO.children![2];
    expect(expectDatabaseNodeVO.id).toBe(databaseNodeSO.id);
  });
});

describe('folder update test', () => {
  test('update folder name', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    // 创建一个文件夹节点
    const folderNode = await rootFolder.createFolderNode({ userId: user.id }, 'folder');
    // 更新文件夹节点
    await folderNode.toNodeSO().update(user, { resourceType: 'FOLDER', name: 'new name' });
    // 验证文件夹节点是否被更新
    await rootFolder.reloadChildren();
    const children = await rootFolder.getChildren();
    expect(children[0].name).toEqual('new name');
  });
  test('update folder cover', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    // 创建一个文件夹节点
    const folderNode = await rootFolder.createFolderNode({ userId: user.id }, 'folder');
    // 更新文件夹节点
    await folderNode.toNodeSO().update(user, {
      resourceType: 'FOLDER',
      name: 'new name',
      cover: {
        type: 'COLOR',
        color: 'BLUE',
      },
    });
    // 验证文件夹节点是否被更新
    await rootFolder.reloadChildren();
    let children = await rootFolder.getChildren();
    let folderSO = await children[0].toResourceSO<FolderSO>();
    expect(folderSO.cover).toMatchObject({ type: 'COLOR', color: 'BLUE' });

    // 验证VO
    let folderVO = await folderSO.toVO({ userId: user.id });
    expect(folderVO.cover).toMatchObject({ type: 'COLOR', color: 'BLUE' });

    // 更新封面为图片
    const filePath = `${__dirname}/folder-cover.png`;
    const fileExt = path.extname(filePath);

    const { path: uploadPath, presignedPutUrl } = await TmpAttachmentSO.getPresignedPut(user, fileExt);
    await uploadFileToUrl(presignedPutUrl, filePath);

    const attachment = await AttachmentSO.createByPresignedPut(user, uploadPath, 'folder/');
    const previousRefCount = attachment.refCount;

    await folderNode.toNodeSO().update(user, {
      resourceType: 'FOLDER',
      cover: {
        type: 'ATTACHMENT',
        attachmentId: attachment.id,
        relativePath: attachment.previewUrl!,
      },
    });
    const reloadAttachment = await AttachmentSO.init(attachment.id);
    expect(reloadAttachment.refCount).toBe(previousRefCount + 1);

    // 验证文件夹节点是否被更新
    await rootFolder.reloadChildren();
    children = await rootFolder.getChildren();
    folderSO = await children[0].toResourceSO<FolderSO>();
    expect(folderSO.cover).toMatchObject({
      type: 'ATTACHMENT',
      attachmentId: attachment.id,
      relativePath: attachment.previewUrl,
    });

    // 验证VO
    folderVO = await folderSO.toVO({ userId: user.id });
    expect(folderVO.cover).toMatchObject({
      type: 'ATTACHMENT',
      attachmentId: attachment.id,
      relativePath: attachment.previewUrl,
    });

    // 只改名字，不改封面
    await folderNode.toNodeSO().update(user, {
      resourceType: 'FOLDER',
      name: 'new name 2',
    });
    await rootFolder.reloadChildren();
    children = await rootFolder.getChildren();
    folderSO = await children[0].toResourceSO<FolderSO>();
    expect(folderSO.name).toEqual('new name 2');
    expect(folderSO.cover).toMatchObject({
      type: 'ATTACHMENT',
      attachmentId: attachment.id,
      relativePath: attachment.previewUrl,
    });

    // 验证VO
    folderVO = await folderSO.toVO({ userId: user.id });
    expect(folderVO.cover).toMatchObject({
      type: 'ATTACHMENT',
      attachmentId: attachment.id,
      relativePath: attachment.previewUrl,
    });

    await reloadAttachment.delete();
  });
});

describe('folder delete test', () => {
  test('test delete folder node', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    // 创建一个文件夹节点
    const folderNode = await rootFolder.createFolderNode({ userId: user.id }, 'folder');
    // 删除文件夹节点
    await folderNode.toNodeSO().delete(user);
    // 验证文件夹节点是否被删除
    await rootFolder.reloadChildren();
    const children = await rootFolder.getChildren();
    expect(children).toHaveLength(0);
  });
});

describe('folder toBO', () => {
  test('export folder BO', async () => {
    const { user, space } = await MockContext.initUserContext();
    // 安装一个模板节点
    const templateId = '@vika/scrum-standup';
    const templateFolder = await space.installTemplateById(user, templateId);
    await templateFolder.toNodeSO().update(user, { resourceType: 'FOLDER', name: 'new name' });
    const folderBO = await (await (await NodeSO.init(templateFolder.id)).toResourceSO<FolderSO>()).toBORecursive();
    expect(folderBO.name).toEqual('new name');
    expect(folderBO.templateId).toEqual(templateId);
    expect(folderBO.children).not.toBeUndefined(); // 包括children，用于editor
  });

  test('export folder bo have child', async () => {
    const { user, space } = await MockContext.initUserContext();
    // 安装一个模板节点
    const templateId = '@vika/scrum-standup';
    const templateFolder = await space.installTemplateById(user, templateId);
    await templateFolder.toNodeSO().update(user, { resourceType: 'FOLDER', name: 'new name' });
    const folderBO = await (await (await NodeSO.init(templateFolder.id)).toResourceSO<FolderSO>()).toBORecursive();
    expect(folderBO.name).toEqual('new name');
    expect(folderBO.templateId).toEqual(templateId);
    expect(folderBO.children?.length).toEqual(4);
  });
});

describe('folder publish', () => {
  test('publish folder to template center', async () => {
    const { user, space, rootFolder } = await MockContext.initUserContext();
    const folder = await rootFolder.createChildSimple(user, { name: 'test archive folder', resourceType: 'FOLDER' });
    const folderSO = await folder.toResourceSO<FolderSO>();
    await folderSO.createChildSimple(user, {
      resourceType: 'DATABASE',
      name: 'test database',
    });
    const newFolder = await FolderSO.init(folder.id);
    const templateId = await newFolder.publish(user, {
      type: 'TEMPLATE_CENTER',
      data: {
        category: 'sales',
        visibility: 'PUBLIC',
        detach: true,
        version: '0.0.1',
        templateId: generateNanoID('tps'),
      },
    });
    const storeTemplateSO = await StoreTemplateSO.init(templateId);
    expect(storeTemplateSO.name).equal('test archive folder');
    const publishedFolder = await FolderSO.init(folder.id);
    expect(publishedFolder.isTemplate).toEqual(true);
    const children = await publishedFolder.getChildren();
    for (const child of children) {
      expect(child.templateId).not.toEqual(null);
    }
    // install template
    const templateFolder = await space.installTemplateById(user, templateId);
    expect(templateFolder.id).not.toEqual(null);
  });

  test('should not publish folder which include another folder resource', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    const folder = await rootFolder.createChildSimple(user, { name: 'test archive folder', resourceType: 'FOLDER' });
    const databaseNode1 = await rootFolder.createChildSimple(user, {
      resourceType: 'DATABASE',
      name: 'test database',
    });
    const database1 = await databaseNode1.toResourceSO<DatabaseSO>();
    const folderSO = await folder.toResourceSO<FolderSO>();
    const databaseNode = await folderSO.createChildSimple(user, {
      resourceType: 'DATABASE',
      name: 'test database',
    });
    // add a link field
    const database2 = await databaseNode.toResourceSO<DatabaseSO>();
    await database2.createField(user, {
      type: 'LINK',
      name: 'LINK',
      property: {
        foreignDatabaseId: database1.id,
        brotherFieldId: database1.getFields()[0].id,
      },
    });
    const newFolder = await FolderSO.init(folder.id);
    try {
      await newFolder.publish(user, {
        type: 'TEMPLATE_CENTER',
        data: {
          category: 'sales',
          visibility: 'PUBLIC',
          detach: true,
          version: '0.0.1',
          templateId: generateNanoID('tps'),
        },
      });
    } catch (e) {
      expect(e).toBeDefined();
    }
  });

  test('test zip folder', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    const folder = await rootFolder.createChildSimple(user, { name: 'test archive folder', resourceType: 'FOLDER' });
    const folderSO = await folder.toResourceSO<FolderSO>();
    await folderSO.createChildSimple(user, {
      resourceType: 'DATABASE',
      name: 'test database',
    });
    const newFolder = await FolderSO.init(folder.id);
    await newFolder.publish(user, {
      type: 'TEMPLATE_CENTER',
      data: {
        category: 'sales',
        visibility: 'PUBLIC',
        detach: true,
        version: '0.0.1',
        templateId: generateNanoID('tps'),
      },
    });
    const templateFolder = await FolderSO.init(newFolder.id);
    const url = await templateFolder.publish(user, { type: 'LOCAL' });
    expect(url).toBeDefined();
  });

  test('new folder to template', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    const folder = await rootFolder.createChildSimple(user, { name: 'test archive folder', resourceType: 'FOLDER' });
    const folderSO = await folder.toResourceSO<FolderSO>();
    await folderSO.createChildSimple(user, {
      resourceType: 'DATABASE',
      name: 'test database',
    });
    const newFolder = await FolderSO.init(folder.id);
    const bo = await newFolder.toTemplate();
    expect(bo.templateId).toBeUndefined();
    expect(bo.id).toBeUndefined();
    const database = (bo as Folder).children![0] as Database;
    expect(database.fields![0].id).toBeUndefined();
  });

  test('publish resource permission check', async () => {
    // init space member
    const { user: user1, rootFolder, space } = await MockContext.initUserContext();
    const rootTeam = await space.getRootTeam();
    const { user: user2 } = await MockContext.initUserContext();
    const member2 = await space.joinUser(user2.id, rootTeam.id);
    // user2 init folder
    const folderId = await rootFolder.createChildren(user2, [
      {
        resourceType: 'FOLDER',
        name: 'test',
        children: [
          {
            resourceType: 'DATABASE',
            name: 'test',
          },
        ],
      },
    ]);
    // test publish folder without permission, use user2, because user1 is space admin
    await expect(space.getAdminRoleAclSO().authorize(member2, 'publishResource')).rejects.toThrowError(
      'Unauthorized to perform action',
    );
    // add publishResource permission to member2
    const role = await space.createRole(user1.id, {
      name: 'publishResource',
      manageSpace: true,
      setting: { permissions: ['publish'] },
    });
    await role.addUnit(user1.id, member2.id);
    // test publish folder with permission
    const templateId = generateNanoID('tps');
    await expect(space.getAdminRoleAclSO().authorize(member2, 'publishResource')).resolves.not.toThrowError();
    const folder = await FolderSO.init(folderId);
    const publishTemplateId = await folder.publish(user2, {
      type: 'TEMPLATE_CENTER',
      data: {
        templateId,
        category: 'ai',
        visibility: 'PUBLIC',
        version: '0.0.1',
        detach: true,
      },
    });
    expect(publishTemplateId).toBe(templateId);
  });

  test('publish modified installed template folder', async () => {
    const { user, space } = await MockContext.initUserContext();
    // install template should be a folder and the templateId should be original
    const templateFolder = await space.installTemplateById(user, 'ai-automated-ticket-system');
    expect(templateFolder.model.type).toBe('FOLDER');
    expect(templateFolder.templateId).toBe('ai-automated-ticket-system');

    // allow user modify folder and publish as themes template
    const node = templateFolder.toNodeSO();
    await node.update(user, { name: 'new template from template', resourceType: 'FOLDER' });
    const folderSO = await FolderSO.init(node.id);
    const templateId = await folderSO.publish(user, {
      type: 'TEMPLATE_CENTER',
      data: {
        templateId: generateNanoID('tps'),
        category: 'ai',
        visibility: 'PUBLIC',
        version: '0.0.1',
        detach: true,
      },
    });
    // template author should be the publisher
    const template = await StoreTemplateSO.init(templateId);
    expect(template.createdBy).toBe(user.id);

    // republish
    await folderSO.publish(user, {
      type: 'TEMPLATE_CENTER',
      data: {
        templateId,
        category: 'ai',
        visibility: 'PUBLIC',
        version: '0.0.2',
        detach: true,
      },
    });
    const reTemplate = await StoreTemplateSO.init(templateId);
    expect(reTemplate.createdBy).toBe(user.id);
  });

  test('publish all resources to template center', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    //  create all resources instance in the folder
    const childrenBO = defaultTemplate.resources.filter((i) => i.resourceType !== 'DASHBOARD');
    const folderId = await rootFolder.createChildren(user, [
      {
        resourceType: 'FOLDER',
        name: 'test',
        templateId: 'folder',
        children: childrenBO,
      },
    ]);
    const folderSO = await FolderSO.init(folderId);
    const templateId = await folderSO.publish(user, {
      type: 'TEMPLATE_CENTER',
      data: {
        templateId: generateNanoID('tps'),
        category: 'ai',
        visibility: 'PUBLIC',
        version: '0.0.1',
        detach: true,
      },
    });
    const children = await folderSO.getChildren();
    const templateRepo = await TemplateRepoSO.init(templateId);
    expect(templateRepo.currentTemplate.resources.length).toBe(childrenBO.length);
    expect(templateRepo.currentTemplate.resources[0].templateId).toBe(children[0].templateId);
  });

  test('publish a database with records', async () => {
    const { user, member, rootFolder } = await MockContext.initUserContext();
    const node = await rootFolder.createChildSimple(user, {
      resourceType: 'FOLDER',
      name: 'test_folder',
    });
    const folderSO = await FolderSO.init(node.id);
    const databaseNode = await folderSO.createChildSimple(
      user,
      {
        resourceType: 'DATABASE',
        name: 'test_database',
      },
      { createDefaultRecords: true },
    );
    const database = await databaseNode.toResourceSO<DatabaseSO>();
    const fields = database.getFields();
    const fieldId = fields.filter((f) => f.type === SingleText.value).map((f) => f.id)[0];
    const insertRecords = [];
    for (let i = 0; i < 3; i++) {
      insertRecords.push({ [fieldId]: `record-${i + 1}` });
    }
    await database.createRecords(user, member, insertRecords);
    const templateId = await folderSO.publish(user, {
      type: 'TEMPLATE_CENTER',
      data: {
        templateId: generateNanoID('tps'),
        category: 'ai',
        visibility: 'PUBLIC',
        version: '0.0.1',
        detach: true,
        withRecords: true,
      },
    });
    const template = await TemplateRepoSO.init(templateId);
    const records = (template.currentTemplate.resources[0] as Database).records!;
    expect(records.length).toBe(6);
    expect(records[3].data[fieldId]).toBe('record-1');
  });
});

describe('folder export test', () => {
  test('export a single form should throw an error', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    const folder = await rootFolder.createChildSimple(user, { name: 'test folder', resourceType: 'FOLDER' });
    const folderSO = await folder.toResourceSO<FolderSO>();
    const databaseNode = await folderSO.createChildSimple(user, {
      resourceType: 'DATABASE',
      name: 'test database',
    });
    const databaseSO = await databaseNode.toResourceSO<DatabaseSO>();
    const formNode = await folderSO.createChildSimple(user, {
      resourceType: 'FORM',
      name: 'test form',
      formType: 'DATABASE',
      databaseId: databaseNode.id,
      viewId: (await databaseSO.getViews())[0].id,
    });

    const formSo = await formNode.toResourceSO<FormSO>();
    await expect(() => formSo.export()).rejects.toThrowError(
      `The form ${formSo.id} linked database ${databaseNode.id} not in current folder`,
    );
  });

  test('export three depth tree', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    const folder = await rootFolder.createChildSimple(user, { name: 'test folder', resourceType: 'FOLDER' });
    const folderSO = await folder.toResourceSO<FolderSO>();
    await folderSO.createChildSimple(user, {
      resourceType: 'DATABASE',
      name: 'test database-1',
    });
    const folder1 = await folderSO.createChildSimple(user, {
      resourceType: 'FOLDER',
      name: 'test folder-1',
    });
    const folder1SO = await folder1.toResourceSO<FolderSO>();
    await folder1SO.createChildSimple(user, {
      resourceType: 'DATABASE',
      name: 'test database-2',
    });
    const folder2 = await folder1SO.createChildSimple(user, {
      resourceType: 'FOLDER',
      name: 'test folder-2',
    });
    const folder2SO = await folder2.toResourceSO<FolderSO>();
    await folder2SO.createChildSimple(user, {
      resourceType: 'DATABASE',
      name: 'test database-3',
    });
    // publish folder
    const exportFolder = await FolderSO.init(folder.id);
    const exportBO = await exportFolder.export();
    expect(exportBO.children?.length).toBe(2);
    expect(exportBO.children?.[0]?.resourceType).toBe('FOLDER');
    expect((exportBO.children?.[0] as Folder)?.children?.length).toBe(2);
    const threeDepthFolder = (exportBO.children?.[0] as Folder)?.children?.[0] as Folder;
    expect(threeDepthFolder.children?.length).toBe(1);
  });

  test('export all templates and import', { timeout: 5 * 68 * 1000 }, async () => {
    const { user, space, rootFolder } = await MockContext.initUserContext();
    const folderId = await AdminDebugSO.installAllTemplate(user, space);
    const folder = await FolderSO.init(folderId);
    const folderBO = await folder.export();
    const newFolderId = await rootFolder.createChildren(user, [folderBO]);
    console.log(newFolderId);
  });
});

describe('find children test', () => {
  test('find children loop parents', async () => {
    const { user, space, rootFolder } = await MockContext.initUserContext();
    await rootFolder.createChildren(user, [
      {
        resourceType: 'FOLDER',
        name: 'folder1',
        children: [
          {
            resourceType: 'DATABASE',
            name: 'database2',
          },
          {
            resourceType: 'FOLDER',
            name: 'folder2',
            children: [
              {
                resourceType: 'DATABASE',
                name: 'database3',
              },
              {
                resourceType: 'FOLDER',
                name: 'folder3',
                children: [
                  {
                    resourceType: 'DATABASE',
                    name: 'database4',
                  },
                ],
              },
            ],
          },
        ],
      },
    ]);
    const children = await rootFolder.findChildren({ loopParents: true });
    for (const child of children) {
      expect(child.parents).toBeDefined();
    }

    const databases = await space.findNodes({ loopParents: true, type: 'DATABASE' });
    for (const database of databases) {
      const vo = await database.toVO({ locale: user.locale, userId: user.id });
      if (vo.name === 'database2') {
        expect(vo.path).eq('/folder1');
      }
      if (vo.name === 'database3') {
        expect(vo.path).eq('/folder1/folder2');
      }
      if (vo.name === 'database4') {
        expect(vo.path).eq('/folder1/folder2/folder3');
      }
    }
  });

  test('find children with scope', async () => {
    const { user, space, rootFolder } = await MockContext.initUserContext();
    await rootFolder.createChildren(user, [
      {
        resourceType: 'FOLDER',
        name: 'folder-public',
        children: [
          {
            resourceType: 'DATABASE',
            name: 'database-public',
          },
        ],
      },
    ]);
    await rootFolder.createChildren(
      user,
      [
        {
          resourceType: 'FOLDER',
          name: 'folder-private',
          children: [
            {
              resourceType: 'DATABASE',
              name: 'database-private',
            },
          ],
        },
      ],
      {
        scope: 'PRIVATE',
      },
    );

    const publicChildren = await rootFolder.findChildren({ type: 'DATABASE' });
    expect(publicChildren.length).toBe(1);
    expect(publicChildren[0].name).toBe('database-public');

    const privateChildren = await rootFolder.findChildren({
      type: 'DATABASE',
      privateUnitId: await user.getMemberId(space.id),
    });
    expect(privateChildren.length).toBe(1);
    expect(privateChildren[0].name).toBe('database-private');
  });

  test('search with i18n name', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    await rootFolder.createChildren(user, [
      {
        resourceType: 'FOLDER',
        name: 'folder-public',
        children: [
          {
            resourceType: 'DATABASE',
            name: {
              'zh-CN': '数据库',
              en: 'database',
              'zh-TW': '資料庫',
              ja: 'データベース',
            },
          },
          {
            resourceType: 'DATABASE',
            name: 'test',
          },
        ],
      },
    ]);
    const nodes = await rootFolder.findChildren({ name: '数据库' });
    expect(nodes.length).toBe(1);
    const testNodes = await rootFolder.findChildren({ name: 'test' });
    expect(testNodes.length).toBe(1);
  });

  test('find child resource count correctly', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    await rootFolder.createChildren(user, [
      {
        resourceType: 'FOLDER',
        name: 'folder1',
        children: [
          {
            resourceType: 'DATABASE',
            name: 'database1',
          },
          {
            resourceType: 'DATABASE',
            name: 'database2',
          },
          {
            resourceType: 'AUTOMATION',
            name: 'automation1',
            actions: [],
            triggers: [],
          },
          {
            resourceType: 'AUTOMATION',
            name: 'automation2',
            actions: [],
            triggers: [],
          },
        ],
      },
      {
        resourceType: 'FOLDER',
        name: 'folder2',
        children: [
          {
            resourceType: 'DATABASE',
            name: 'database3',
          },
          {
            resourceType: 'FOLDER',
            name: 'folder3',
            children: [
              {
                resourceType: 'AUTOMATION',
                name: 'automation3',
                actions: [],
                triggers: [],
              },
              {
                resourceType: 'AUTOMATION',
                name: 'automation4',
                actions: [],
                triggers: [],
              },
            ],
          },
        ],
      },
    ]);
    const children = await rootFolder.getAllChildren();
    const folder1 = children.find((c) => c.name === 'folder1');
    const folder2 = children.find((c) => c.name === 'folder2');
    const folder3 = children.find((c) => c.name === 'folder3');
    const folder1Children = await FolderSO.getChildResourceCount(folder1!.id);
    expect(folder1Children).toBe(4);
    const folder2Children = await FolderSO.getChildResourceCount(folder2!.id);
    expect(folder2Children).toBe(3);
    const folder3Children = await FolderSO.getChildResourceCount(folder3!.id);
    expect(folder3Children).toBe(2);
  });

  test('has child resource error correctly', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    const folderId = await rootFolder.createChildren(user, [
      {
        resourceType: 'FOLDER',
        name: 'folder1',
        children: [
          {
            resourceType: 'AUTOMATION',
            name: 'automation1',
            actions: [],
            triggers: [],
          },
          {
            resourceType: 'AUTOMATION',
            name: 'automation2',
            actions: [],
            triggers: [],
          },
        ],
      },
    ]);
    await sleep(2000);
    console.log('folderId', folderId);
    const hasError = await FolderSO.hasChildResourceError(folderId);
    expect(hasError).toBe(true);
  });
});

describe('import from vika test', () => {
  test(
    'export vika datasource',
    async () => {
      const { user, space, rootFolder } = await MockContext.initUserContext();

      const userId = user.id;

      const mockToken = 'uskpJ1Y2L9yrESOsCHZ';

      // add vika datasource to space integration (vika apikey)
      const integrationSO = await space.createIntegration(userId, {
        type: 'VIKA',
        name: 'vika 数据源集成',
        description: 'vika 数据源集成',
        token: mockToken,
      });

      const folderId = await rootFolder.importResourceFromVika(user, integrationSO, ['dstQ7TPMUj35KXth78']);
      const folder = await FolderSO.init(folderId);
      const children = await folder.getChildren();
      for (const child of children) {
        const vo = await child.toVO({ userId });
        expect(vo.name).toBeDefined();
      }
    },
    { skip: true },
  );
});

describe('root tree test', () => {
  test('test multiple children preNodeId is null', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    const first = await rootFolder.createChildSimple(user, {
      resourceType: 'DATABASE',
      name: `database1`,
    });
    // normal nodes
    let preNodeId: string = first.id;
    for (let i = 2; i < 4; i++) {
      const normal = await rootFolder.createChildSimple(user, {
        resourceType: 'DATABASE',
        name: `database${i}`,
      });
      await normal.move(user, { parentId: rootFolder.id, preNodeId });
      const newNode = await NodeSO.init(normal.id);
      expect(newNode.preNodeId).toBe(preNodeId);
      preNodeId = normal.id;
    }
    // preNodeId is null
    for (let i = 4; i < 6; i++) {
      const node = await rootFolder.createChildSimple(user, {
        resourceType: 'DATABASE',
        name: `database${i}`,
      });
      await db.prisma.node.update({
        where: { id: node.id },
        data: { nextNode: { disconnect: true } },
      });
      const newNode = await NodeSO.init(node.id);
      const nextNode = await newNode.getNextNode();
      expect(nextNode).toBe(null);
    }
    const vo = await rootFolder.toNodeTreeVOWithSQLRecursive({ userId: user.id });
    expect(vo.children?.length).toBe(5);
  });
});

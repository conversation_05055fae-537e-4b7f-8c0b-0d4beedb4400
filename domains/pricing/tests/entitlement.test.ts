import { describe, expect, test, vi } from 'vitest';
import { MockContext, waitForMatchToBeMet } from '@bika/domains/__tests__/mock';
import { DatabaseSO } from '@bika/domains/database/server/database-so';
import { NodeSO } from '@bika/domains/node/server';
import { UsageType } from '@bika/types/pricing/bo';

/**
 * Test Case Collections: 免费权益
 */
describe('free entitlement test', () => {
  /**
   * 一个用户没有订阅时，应该只有免费权益
   */
  test('should only has free entitlement when no subscription', async () => {
    const { space } = await MockContext.initUserContext();
    const entitlement = await space.getEntitlement();
    expect(entitlement.planFeature.getBooleanFeature('BROWSER_NOTIFICATIONS').isEnable).toBe(true);
    expect(entitlement.planFeature.getBooleanFeature('MOBILE_NOTIFICATIONS').isEnable).toBe(true);
    expect(entitlement.planFeature.getBooleanFeature('PUBLIC_SHARE').isEnable).toBe(true);
    expect(entitlement.planFeature.getBooleanFeature('VIEW_RESOURCE').isEnable).toBe(true);
    expect(entitlement.planFeature.getBooleanFeature('SUB_ADMINS').isEnable).toBe(false);
    expect(entitlement.planFeature.getBooleanFeature('RESTRICT_DOMAINS').isEnable).toBe(false);
    expect(entitlement.planFeature.getBooleanFeature('USER_SESSIONS_LOGS').isEnable).toBe(false);
    expect(entitlement.planFeature.getBooleanFeature('PUBLISH_TEMPLATE').isEnable).toBe(true);
    expect(entitlement.planFeature.getBooleanFeature('COMMUNITY').isEnable).toBe(true);
    expect(entitlement.planFeature.getBooleanFeature('HELP_CENTER').isEnable).toBe(true);
    expect(entitlement.planFeature.getBooleanFeature('WEBINAR').isEnable).toBe(true);
    expect(entitlement.planFeature.getBooleanFeature('EMAIL_SUPPORT').isEnable).toBe(false);
    expect(entitlement.planFeature.getBooleanFeature('IM_SUPPORT').isEnable).toBe(false);
    expect(entitlement.planFeature.getBooleanFeature('PROFESSIONAL_SERVICES').isEnable).toBe(false);

    expect(entitlement.planFeature.getUsageFeature('SEATS').value).toBe(5);
    expect(entitlement.planFeature.getUsageFeature('GUESTS').value).toBe(5);
    expect(entitlement.planFeature.getUsageFeature('RECORDS_PER_SPACE').value).toBe(100000);
    expect(entitlement.planFeature.getUsageFeature('RECORDS_PER_DATABASE').value).toBe(10000);
    expect(entitlement.planFeature.getUsageFeature('STORAGES').value).toBe(5);
    expect(entitlement.planFeature.getUsageFeature('RESOURCES').value).toBe(100);
    expect(entitlement.planFeature.getUsageFeature('MISSIONS').value).toBe(10000);
    expect(entitlement.planFeature.getUsageFeature('REPORTS').value).toBe(10000);
    expect(entitlement.planFeature.getUsageFeature('MANAGED_EMAILS').value).toBe(5);
    expect(entitlement.planFeature.getUsageFeature('SMTP_EMAILS').isInfinite).toBe(true);
    expect(entitlement.planFeature.getUsageFeature('AUTOMATION_RUNS').value).toBe(200);
    expect(entitlement.planFeature.getDateFeature('AUTOMATION_RUN_HISTORY').value).toBe(7);
    expect(entitlement.planFeature.getUsageFeature('SPACE_INTEGRATIONS').value).toBe(3);
    expect(entitlement.planFeature.getUsageFeature('API_REQUEST').value).toBe(10000);
    expect(entitlement.planFeature.getUsageFeature('CREDITS_PER_SEAT').value).toBe(1000);
    expect(entitlement.planFeature.getUsageFeature('RESOURCE_PERMISSION').value).toBe(3);

    expect(entitlement.planFeature.getFixedNumberFeature('OPENAPI_RATE').value).toBe(2);

    expect(entitlement.planFeature.getDateFeature('SPACE_SESSIONS_LOGS').value).toBe(7);
    expect(entitlement.planFeature.getDateFeature('SPACE_AUDIT_LOGS').value).toBe(0.1);
  });
});

describe('Free Plan - Feature Usage Test', () => {
  test('Free - Check Seats Usage', async () => {
    // 初始化空间站
    const { space } = await MockContext.initUserContext();
    const rootTeam = await space.getRootTeam();
    // 先把席位占满
    const aUser = await MockContext.createUser('a');
    await space.joinUser(aUser.id, rootTeam.id);
    const bUser = await MockContext.createUser('b');
    await space.joinUser(bUser.id, rootTeam.id);
    const cUser = await MockContext.createUser('c');
    await space.joinUser(cUser.id, rootTeam.id);
    const dUser = await MockContext.createUser('d');
    await space.joinUser(dUser.id, rootTeam.id);

    const entitlement = await space.getEntitlement();
    // 检查现在的空间站的人数
    const usage = entitlement.getUsage();
    const usedUsageValue = await usage.getUsedUsageValue('SEATS');
    expect(usedUsageValue).toBe(5);

    // 期望再加一个席位, 应该超量了
    const { exceed } = await entitlement.isUsageExceed({ feature: 'SEATS' });
    expect(exceed).toBe(true);
  });

  test('Free - Check Resources Usage', async () => {
    // 初始化空间站
    const { user, space, rootFolder } = await MockContext.initUserContext();

    // 先把资源节点数量占满, 直接新建100个表格
    await Promise.all(
      Array.from({ length: 100 }).map((_, index) =>
        rootFolder.createChildSimple(user, { name: `DATABASE_${index}`, resourceType: 'DATABASE' }),
      ),
    );

    const entitlement = await space.getEntitlement();

    // 检查现在的空间站的资源数量
    const usage = entitlement.getUsage();
    const usedUsageValue = await usage.getUsedUsageValue('RESOURCES');
    expect(usedUsageValue).toBe(100);

    // 期望再加一个资源, 应该超量了
    const { exceed } = await entitlement.isUsageExceed({ feature: 'RESOURCES' });
    expect(exceed).toBe(true);
  });

  test('Free - Check Total Records Usage', async () => {
    // 初始化空间站
    const { user, space, rootFolder } = await MockContext.initUserContext();

    // 创建一个表格, 默认表格有3行记录
    const databaseNode = await rootFolder.createChildSimple(
      user,
      {
        resourceType: 'DATABASE',
        name: 'database',
      },
      { createDefaultRecords: true },
    );
    await waitForMatchToBeMet(
      async () => {
        const node = await NodeSO.init(databaseNode.id);
        const numberState = node.state.find((state) => state.state === 'NUMBER');
        return numberState !== undefined && numberState.number === 3;
      },
      2000,
      100,
    ).catch((error: Error) => {
      throw new Error(error.message);
    });

    const entitlement = await space.getEntitlement();
    const usage = entitlement.getUsage();
    const usedUsageValue = await usage.getUsedUsageValue('RECORDS_PER_SPACE');
    expect(usedUsageValue).toBe(3);

    // 先模拟替换usage对象
    vi.spyOn(entitlement, 'getUsage').mockImplementation(() => usage);

    // mock 最大值为3行
    vi.spyOn(usage, 'getMaxUsageValue').mockImplementation((type: UsageType) => {
      if (type === 'RECORDS_PER_SPACE') {
        return 3;
      }
      return undefined;
    });
    const max = usage.getMaxUsageValue('RECORDS_PER_SPACE');
    expect(max).toBe(3);

    // 期望再加一个记录, 应该超量了
    const { exceed: isRecordExceeded } = await entitlement.isUsageExceed({ feature: 'RECORDS_PER_SPACE' });
    expect(isRecordExceeded).toBe(true);
  });

  test('Free - Check Storage Usage', async () => {
    // 初始化空间站
    const { user, member, space, rootFolder } = await MockContext.initUserContext();

    // 创建一个表格, 默认表格有3行记录
    const databaseNode = await rootFolder.createChildSimple(
      user,
      {
        resourceType: 'DATABASE',
        name: 'database',
      },
      { createDefaultRecords: true },
    );
    const database = await databaseNode.toResourceSO<DatabaseSO>();

    // 创建一个附件列
    const attachField = await database.createField(user, {
      type: 'ATTACHMENT',
      name: 'attachment',
    });
    // 创建附件
    const attachment = await MockContext.createMockAttachment(user);
    const attach = attachment.toVO();
    const attachmentBO = [
      {
        id: attach.id,
        mimeType: attach.mimeType,
        bucket: attach.bucket,
        name: 'test',
        size: attach.size,
        path: attach.path,
      },
    ];
    // 给附件列上传文件
    await database.createRecord(user, member, {
      [attachField.id]: attachmentBO,
    });

    const entitlement = await space.getEntitlement();
    const usage = entitlement.getUsage();

    // 先模拟替换usage对象
    vi.spyOn(entitlement, 'getUsage').mockImplementation(() => usage);

    // 检查现在的空间站的附件使用
    const attachUsedUsageValue = await usage.getUsedUsageValue('STORAGES');
    expect(attachUsedUsageValue).toBe(attachment.size);

    // mock附件最大值为当前附件大小，测试超量
    vi.spyOn(usage, 'getMaxUsageValue').mockImplementation((type: UsageType) => {
      if (type === 'STORAGES') {
        return attachment.size;
      }
      return undefined;
    });

    // 期望再加一个附件, 应该超量了
    const { exceed } = await entitlement.isUsageExceed({
      feature: 'STORAGES',
    });
    expect(exceed).toBe(true);
  });

  test('Free - Check Resource Permission Usage', async () => {
    // 初始化空间站
    const { user, member, space, rootFolder } = await MockContext.initUserContext();

    // 创建3个表格
    const databaseNodeA = await rootFolder.createChildSimple(user, {
      resourceType: 'DATABASE',
      name: 'A',
    });
    const databaseA = await databaseNodeA.toResourceSO<DatabaseSO>();

    const databaseNodeB = await rootFolder.createChildSimple(user, {
      resourceType: 'DATABASE',
      name: 'B',
    });
    const databaseB = await databaseNodeB.toResourceSO<DatabaseSO>();

    const databaseNodeC = await rootFolder.createChildSimple(user, {
      resourceType: 'DATABASE',
      name: 'C',
    });
    const databaseC = await databaseNodeC.toResourceSO<DatabaseSO>();

    // 加2个成员
    const rootTeam = await space.getRootTeam();
    const jackUser = await MockContext.createUser('jack');
    const jack = await space.joinUser(jackUser.id, rootTeam.id);
    const kelvinUser = await MockContext.createUser('kelvin');
    const kelvin = await space.joinUser(kelvinUser.id, rootTeam.id);

    const entitlement = await space.getEntitlement();
    const usage = entitlement.getUsage();

    // 检查
    const maxUsageValue = await usage.getMaxUsageValue('RESOURCE_PERMISSION');
    expect(maxUsageValue).toBe(3);
    let usedUsageValue = await usage.getUsedUsageValue('RESOURCE_PERMISSION');
    expect(usedUsageValue).toBe(0);

    // 按节点分配, 不是按分配成员数来计算

    // A表分配多个权限
    const aclOfA = await databaseA.toAclSO();
    await aclOfA.operator(user.id).grant(jack, 'FULL_ACCESS');
    await aclOfA.operator(user.id).grant(kelvin, 'FULL_ACCESS');

    // B表分配一个权限
    const aclOfB = await databaseB.toAclSO();
    await aclOfB.operator(user.id).grant(jack, 'FULL_ACCESS');

    // C表分配一个权限
    const aclOfC = await databaseC.toAclSO();
    await aclOfC.operator(user.id).grant(kelvin, 'FULL_ACCESS');

    // 检查, 应该还没超限, 刚好3个
    usedUsageValue = await usage.getUsedUsageValue('RESOURCE_PERMISSION');
    expect(usedUsageValue).toBe(3);

    const { exceed } = await entitlement.isUsageExceed({
      feature: 'RESOURCE_PERMISSION',
    });
    expect(exceed).toBe(true);
  });
});

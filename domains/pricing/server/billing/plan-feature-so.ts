// eslint-disable-next-line max-classes-per-file
import dayjs from 'dayjs';
import { getPlanFeature } from '@bika/contents/config/server/pricing/feature/feature';
import {
  BillingBooleanPlanFeature,
  BillingDatePlanFeature,
  BillingUsagePlanFeature,
  BillingPlanFeature,
  BillingPlanFeatureMap,
  BillingBooleanPlanFeatureSchema,
  BillingDatePlanFeatureSchema,
  BillingFixNumberPlanFeature,
  BillingFixNumberPlanFeatureSchema,
} from '@bika/types/pricing/bo';

/**
 * 规格
 */
abstract class PlanFeature<K extends BillingPlanFeature, V> {
  protected readonly _type: K;

  protected readonly _value: V;

  constructor(type: K, value: V) {
    this._type = type;
    this._value = value;
  }

  get type(): K {
    return this._type;
  }

  get value(): V {
    return this._value;
  }
}

class BooleanFeature extends PlanFeature<BillingBooleanPlanFeature, boolean> {
  get isEnable(): boolean {
    return this._value;
  }
}

class NumberFeature extends PlanFeature<BillingUsagePlanFeature, number> {
  /**
   * 是否无限量
   */
  get isInfinite(): boolean {
    return this._value === -1 || this._value === -2;
  }

  get isZero(): boolean {
    return this._value === 0;
  }

  isUsageExceed(value: number): boolean {
    // 无限量不限制
    if (this.isInfinite) {
      return false;
    }
    // 零量不允许超过
    if (this.isZero) {
      return true;
    }
    // 判断是否超过最大值
    return value > this._value;
  }
}

class DateFeature extends PlanFeature<BillingDatePlanFeature, number> {
  get startDate(): Date {
    // 今天开始倒退value作为开始期
    return dayjs(new Date())
      .subtract(this._value * 24, 'hour') // 可能为小数0.1天
      .toDate();
  }

  get endDate(): Date {
    // 今天开始作为结束期
    return new Date();
  }
}

class FixedNumberFeature extends PlanFeature<BillingFixNumberPlanFeature, number> {
  get value(): number {
    return this._value;
  }
}

/**
 * 订阅计划规格对象
 * 给一个plan,就能知道这个plan的规格
 */
export class PlanFeatureSO {
  private readonly _planSpec: string;

  private _cachePlanFeatureMap?: BillingPlanFeatureMap;

  constructor(plan?: string) {
    this._planSpec = plan || 'FREE';
  }

  get plan(): string {
    return this._planSpec;
  }

  get isFree(): boolean {
    return this._planSpec === 'FREE';
  }

  /**
   * 订阅计划的规格
   */
  get featureMap(): BillingPlanFeatureMap {
    if (!this._cachePlanFeatureMap) {
      this._cachePlanFeatureMap = getPlanFeature(this._planSpec);
    }
    return this._cachePlanFeatureMap;
  }

  /**
   * 是否拥有指定规格
   * @param feature 规格
   * @returns true | false
   */
  hasFeature(feature: keyof BillingPlanFeatureMap): boolean {
    const value = this.featureMap[feature];
    return value !== undefined;
  }

  /**
   * 获取所有的开关型规格
   * @returns 所有的开关型规格
   */
  getBooleanFeatures(): BooleanFeature[] {
    return Object.keys(this.featureMap)
      .filter((key): key is BillingBooleanPlanFeature => BillingBooleanPlanFeatureSchema.options.some((f) => f === key))
      .map((key) => this.getBooleanFeature(key));
  }

  /**
   * 获取指定开关型类型的规格
   */
  getBooleanFeature(feature: BillingBooleanPlanFeature): BooleanFeature {
    return new BooleanFeature(feature, this.featureMap[feature] ?? false);
  }

  /**
   * 获取指定用量型的规格
   */
  getUsageFeature(feature: BillingUsagePlanFeature): NumberFeature {
    return new NumberFeature(feature, this.featureMap[feature] ?? 0);
  }

  /**
   * 获取所有日期范围型的规格
   */
  getDateFeatures(): DateFeature[] {
    return Object.keys(this.featureMap)
      .filter((key): key is BillingDatePlanFeature => BillingDatePlanFeatureSchema.options.some((f) => f === key))
      .map((key) => this.getDateFeature(key));
  }

  /**
   * 获取指定日期范围型的规格
   */
  getDateFeature(feature: BillingDatePlanFeature): DateFeature {
    return new DateFeature(feature, this.featureMap[feature] ?? 0);
  }

  getFixedNumberFeatures(): FixedNumberFeature[] {
    return Object.keys(this.featureMap)
      .filter((key): key is BillingFixNumberPlanFeature =>
        BillingFixNumberPlanFeatureSchema.options.some((f) => f === key),
      )
      .map((key) => this.getFixedNumberFeature(key));
  }

  getFixedNumberFeature(feature: BillingFixNumberPlanFeature): FixedNumberFeature {
    return new FixedNumberFeature(feature, this.featureMap[feature] ?? 0);
  }
}

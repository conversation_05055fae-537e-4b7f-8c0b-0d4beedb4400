// eslint-disable-next-line max-classes-per-file
import { Logger } from '@bika/domains/shared/server';
import { db, IntegrationRelationType, NodeResourceType } from '@bika/server-orm';
import { SendEmailTypeSchema } from '@bika/types/automation/bo';
import { UsageType } from '@bika/types/pricing/bo';

type UsageSelector = {
  dateRange: { begin: Date; end: Date };
};

/**
 * 用量计数器
 */
interface UsageCounter {
  getUsageType(): UsageType;
  getUsedUsage(selector?: UsageSelector): Promise<number>;
}

abstract class AbstractUsageCounter<T extends UsageType> implements UsageCounter {
  protected readonly _spaceId: string;

  protected readonly _usageType: T;

  constructor(spaceId: string, usageType: T) {
    this._spaceId = spaceId;
    this._usageType = usageType;
  }

  getUsageType(): T {
    return this._usageType;
  }

  abstract getUsedUsage(selector?: UsageSelector): Promise<number>;
}

class SeatUsageCounter extends AbstractUsageCounter<'SEATS'> {
  constructor(spaceId: string) {
    super(spaceId, 'SEATS');
  }

  async getUsedUsage(): Promise<number> {
    return db.prisma.unitMember.count({
      where: {
        unit: { spaceId: this._spaceId },
        isGuest: false,
        OR: [{ relationType: { not: 'AI' } }, { relationType: null }],
      },
    });
  }
}

class GuestUsageCounter extends AbstractUsageCounter<'GUESTS'> {
  constructor(spaceId: string) {
    super(spaceId, 'GUESTS');
  }

  async getUsedUsage(): Promise<number> {
    return db.prisma.unitMember.count({
      where: {
        unit: { spaceId: this._spaceId },
        isGuest: true,
      },
    });
  }
}

class StoragePerMonthUsageCounter extends AbstractUsageCounter<'STORAGES'> {
  constructor(spaceId: string) {
    super(spaceId, 'STORAGES');
  }

  async getUsedUsage(): Promise<number> {
    // 附件是总量, 不是按月的
    const sums = await db.mongo.spaceAttachment.aggregate([
      {
        $match: {
          spaceId: this._spaceId,
          status: 'ACTIVE',
        },
      },
      {
        $group: {
          _id: null,
          total: { $sum: '$size' },
        },
      },
      {
        $project: {
          _id: 0,
          total: 1,
        },
      },
    ]);
    return sums.length > 0 ? sums[0].total : 0;
  }
}

class ResourceUsageCounter extends AbstractUsageCounter<'RESOURCES'> {
  constructor(spaceId: string) {
    super(spaceId, 'RESOURCES');
  }

  async getUsedUsage(): Promise<number> {
    return db.prisma.node.count({
      where: {
        spaceId: this._spaceId,
        // 排除文件夹
        type: { notIn: [NodeResourceType.ROOT, NodeResourceType.FOLDER, NodeResourceType.TEMPLATE] },
      },
    });
  }
}

class RecordsUsageCounter extends AbstractUsageCounter<'RECORDS_PER_SPACE'> {
  constructor(spaceId: string) {
    super(spaceId, 'RECORDS_PER_SPACE');
  }

  async getUsedUsage(): Promise<number> {
    return db.mongo.databaseRecord(this._spaceId).countDocuments({ spaceId: this._spaceId, status: 'OPEN' });
  }
}

class MissionPerMonthUsageCounter extends AbstractUsageCounter<'MISSIONS'> {
  constructor(spaceId: string) {
    super(spaceId, 'MISSIONS');
  }

  async getUsedUsage(selector: UsageSelector): Promise<number> {
    const {
      dateRange: { begin, end },
    } = selector;
    return db.mongo.mission.countDocuments({ spaceId: this._spaceId, createdAt: { $gte: begin, $lte: end } });
  }
}

class ReportsPerMonthUsageCounter extends AbstractUsageCounter<'REPORTS'> {
  constructor(spaceId: string) {
    super(spaceId, 'REPORTS');
  }

  async getUsedUsage(selector: UsageSelector): Promise<number> {
    const {
      dateRange: { begin, end },
    } = selector;
    return db.mongo.report.countDocuments({
      spaceId: this._spaceId,
      createdAt: { $gte: begin, $lte: end },
    });
  }
}

class AutomationRunsPerMonthUsageCounter extends AbstractUsageCounter<'AUTOMATION_RUNS'> {
  constructor(spaceId: string) {
    super(spaceId, 'AUTOMATION_RUNS');
  }

  async getUsedUsage(selector: UsageSelector): Promise<number> {
    const {
      dateRange: { begin, end },
    } = selector;
    return db.mongo.automationRunHistory.countDocuments({
      spaceId: this._spaceId,
      createdAt: { $gte: begin, $lte: end },
    });
  }
}

class ServerEmailPerMonthUsageCounter extends AbstractUsageCounter<'MANAGED_EMAILS'> {
  constructor(spaceId: string) {
    super(spaceId, 'MANAGED_EMAILS');
  }

  async getUsedUsage(selector: UsageSelector): Promise<number> {
    const {
      dateRange: { begin, end },
    } = selector;
    return db.mongo.email.countDocuments({
      spaceId: this._spaceId,
      type: SendEmailTypeSchema.Enum.SERVICE,
      createdAt: { $gte: begin, $lte: end },
    });
  }
}

class IntegrationUsageCounter extends AbstractUsageCounter<'SPACE_INTEGRATIONS'> {
  constructor(spaceId: string) {
    super(spaceId, 'SPACE_INTEGRATIONS');
  }

  async getUsedUsage(): Promise<number> {
    return db.prisma.integration.count({
      where: { relationId: this._spaceId, relationType: IntegrationRelationType.SPACE },
    });
  }
}

class ApiRequestCounter extends AbstractUsageCounter<'API_REQUEST'> {
  constructor(spaceId: string) {
    super(spaceId, 'API_REQUEST');
  }

  async getUsedUsage(selector: UsageSelector): Promise<number> {
    const {
      dateRange: { begin, end },
    } = selector;

    // 如果足够慢的话, 得用Redis才能
    // const start = Date.now();
    const total = await db.log.count('OPENAPI_REQUEST_LOG', {
      startTime: begin,
      endTime: end,
      where: { space_id: this._spaceId, success: true },
    });
    // const cost = Date.now() - start;
    // console.log(`Api Request stat count cost: ${cost}ms, total: ${total}`);
    return total;
  }
}

/**
 * 资源权限计数器
 */
class ResourcePermissionCounter extends AbstractUsageCounter<'RESOURCE_PERMISSION'> {
  constructor(spaceId: string) {
    super(spaceId, 'RESOURCE_PERMISSION');
  }

  async getUsedUsage(): Promise<number> {
    const group = await db.prisma.permission.groupBy({
      by: ['resourceId'],
      where: { resourceType: 'NODE', unit: { spaceId: this._spaceId } },
      _count: { _all: true },
    });
    return group.length; // 返回资源权限的数量
  }
}

export const getUsageCounter = <T extends UsageType>(spaceId: string, usageType: T): UsageCounter => {
  switch (usageType) {
    case 'SEATS':
      return new SeatUsageCounter(spaceId);
    case 'GUESTS':
      return new GuestUsageCounter(spaceId);
    case 'STORAGES':
      return new StoragePerMonthUsageCounter(spaceId);
    case 'RESOURCES':
      return new ResourceUsageCounter(spaceId);
    case 'RECORDS_PER_SPACE':
      return new RecordsUsageCounter(spaceId);
    case 'MISSIONS':
      return new MissionPerMonthUsageCounter(spaceId);
    case 'REPORTS':
      return new ReportsPerMonthUsageCounter(spaceId);
    case 'AUTOMATION_RUNS':
      return new AutomationRunsPerMonthUsageCounter(spaceId);
    case 'MANAGED_EMAILS':
      return new ServerEmailPerMonthUsageCounter(spaceId);
    case 'SPACE_INTEGRATIONS':
      return new IntegrationUsageCounter(spaceId);
    case 'API_REQUEST':
      return new ApiRequestCounter(spaceId);
    case 'RESOURCE_PERMISSION':
      return new ResourcePermissionCounter(spaceId);
    default:
      Logger.error('Unknown usage type', { spaceId, usageType });
      throw new Error(`Unknown usage type: ${usageType}`);
  }
};

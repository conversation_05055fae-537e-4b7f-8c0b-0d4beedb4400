import { errors } from '@bika/contents/config/server/error/errors';
import { ServerError } from '@bika/contents/config/server/error/server_error';
import { UsageReminderEmail } from '@bika/contents/email/UsageReminderEmail';
import { EmailSO } from '@bika/domains/email/server/email-so';
import { SpaceSO } from '@bika/domains/space/server/space-so';
import { db } from '@bika/server-orm';
import {
  BillingUsagePlanFeature,
  BillingPlanFeature,
  UsageType,
  UsageTypes,
  SpacePlanType,
} from '@bika/types/pricing/bo';
import { EntitlementFeatureVO, FeatureLockedErrorData, UsageExceedLimitErrorData } from '@bika/types/pricing/vo';
import { PlanFeatureSO } from './plan-feature-so';
import { SubscriptionSO } from './subscription';
import { UsageSO } from './usage';
import { UsageCheck } from './usage/types';

/**
 * 权益对象(一个空间站拥有的权益, 可用于检查是否可以操作某个权益)
 */
export class EntitlementSO {
  private readonly _space: SpaceSO;

  /**
   * 一个空间的订阅对象,为空即代表是免费的权益
   */
  private readonly _subscription: SubscriptionSO | null;

  /**
   * 空间站的订阅计划规格
   */
  private _planFeature: PlanFeatureSO;

  constructor(space: SpaceSO, subscription: SubscriptionSO | null) {
    this._space = space;
    this._subscription = subscription;
    this._planFeature = this._subscription ? this._subscription.getPlanFeature() : new PlanFeatureSO();
  }

  get planFeature(): PlanFeatureSO {
    return this._planFeature;
  }

  /**
   * 获取当前空间的订阅对象, 没有订阅就是免费的权益
   */
  getSubscription(): SubscriptionSO | null {
    return this._subscription;
  }

  /**
   * 获取当前空间的订阅计划
   */
  getPlan(): SpacePlanType {
    return this._subscription?.skuConfig.plan || 'FREE';
  }

  getEndDate(): Date | null {
    if (this._subscription) {
      return this._subscription.expiresAt;
    }
    return null;
  }

  async cancel(userId: string): Promise<void> {
    if (!this._subscription) {
      return;
    }
    await this._subscription.cancel(userId);
  }

  /**
   * 是否拥有指定权益(有可能没配置, 没配置就是false, 不要觉得没配置就是false, 没配置代表没有这个功能, 就不限制)
   * @param feature 规格
   * @returns true | false
   */
  hasFeature(feature: BillingPlanFeature): boolean {
    return this.planFeature.hasFeature(feature);
  }

  checkFeature(feature: BillingPlanFeature): void {
    if (!this.hasFeature(feature)) {
      const errorData: FeatureLockedErrorData = {
        plan: this.getPlan(),
        feature,
      };
      throw new ServerError(errors.billing.feature_locked, errorData);
    }
  }

  /**
   * 获取用量服务对象
   * @returns 用量服务对象
   */
  getUsage(): UsageSO {
    return new UsageSO(this._space.id, this._subscription, this._planFeature);
  }

  /**
   * 是否超出用量
   * @param condition 检查条件
   */
  async isUsageExceed(condition: { feature: BillingUsagePlanFeature; value?: number }): Promise<UsageCheck> {
    const { feature, value = 1 } = condition;
    const hasFeature = this.hasFeature(feature);
    if (!hasFeature) {
      // 没有配置此权益, 默认不限制
      return { exceed: false };
    }
    const usageFeature = this.planFeature.getUsageFeature(feature);
    if (usageFeature.isInfinite) {
      // 无限, 不限制
      return { exceed: false };
    }
    if (usageFeature.isZero) {
      // 不用继续走下去了,直接超限
      return { exceed: true, current: 0, limit: 0 };
    }
    const usage = this.getUsage();
    // 是否可以增加用量
    return usage.can({ type: feature as UsageType, value });
  }

  /**
   * 检查是否用量超限
   * @param condition 检查条件
   */
  async checkUsageExceed(condition: { feature: BillingUsagePlanFeature; value?: number }): Promise<void> {
    const { feature, value = 1 } = condition;
    const usageCheck = await this.isUsageExceed({ feature, value });
    if (usageCheck.exceed === true) {
      const admin = await this._space.getOwner();
      const email = admin.email;
      if (email) {
        const remindEmail = new UsageReminderEmail({
          locale: admin.locale,
          space: await this._space.toVO(),
          feature,
          current: usageCheck.current,
          limit: usageCheck.limit,
        });
        EmailSO.send({
          type: 'SYSTEM',
          to: email,
          subject: remindEmail.subject(),
          react: remindEmail.render(),
        });
      }
      // 超限的数据结构
      const errorData: UsageExceedLimitErrorData = {
        plan: this.getPlan(),
        feature,
        max: usageCheck.limit,
        current: usageCheck.current,
      };
      throw new ServerError(errors.billing.usage_exceed_limit, errorData);
    }
  }

  /**
   * 检查单表记录数用量
   * 区分于其他用量类型, 需要传入数据表ID
   */
  async checkRecordsPerDatabaseUsage(databaseId: string, value: number): Promise<void> {
    const count = await db.mongo.databaseRecord(this._space.id).countDocuments({
      spaceId: this._space.id,
      databaseId,
      status: 'OPEN',
    });
    const usageFeature = this.planFeature.getUsageFeature('RECORDS_PER_DATABASE');
    if (usageFeature.isInfinite) {
      // 无限, 不限制
      return;
    }
    const max = usageFeature.value;
    // 低于10%以下用量, 每降1%提醒一次邮件
    const threshold = Math.floor(max * 0.1);

    const isExceeded = count + value <= max;
    if (!isExceeded) {
      const admin = await this._space.getOwner();
      const email = admin.email;
      if (email) {
        const remindEmail = new UsageReminderEmail({
          locale: admin.locale,
          space: await this._space.toVO(),
          feature: 'RECORDS_PER_DATABASE',
          current: count,
          limit: max,
        });
        EmailSO.send({
          type: 'SYSTEM',
          to: email,
          subject: remindEmail.subject(),
          react: remindEmail.render(),
        });
      }
      // 超限的数据结构
      const errorData: UsageExceedLimitErrorData = {
        plan: this.getPlan(),
        feature: 'RECORDS_PER_DATABASE',
        max,
        current: count,
      };
      throw new ServerError(errors.billing.usage_exceed_limit, errorData);
    }
  }

  async toVO(): Promise<EntitlementFeatureVO[]> {
    const features: EntitlementFeatureVO[] = [];
    // 用量型
    const usage = this.getUsage();
    const usedUsage = await usage.getUsedUsageCount();
    const usageFeatures = UsageTypes.map((feature) => {
      const maxValue = usage.getMaxUsageValue(feature);
      const max = maxValue ?? 0;
      let current = 0;
      if (feature in usedUsage) {
        const used = usedUsage[feature];
        if (used) {
          current = used;
        }
      }
      return { feature, current, max };
    });
    features.push(...usageFeatures);

    // 固定数字类型
    const fixedNumberFeatures = this.planFeature.getFixedNumberFeatures();
    features.push(...fixedNumberFeatures.map((feature) => ({ feature: feature.type, value: feature.value })));

    // 支持型
    const supportedFeatures = this.planFeature.getBooleanFeatures();
    // console.log(`supportedFeatures: ${JSON.stringify(supportedFeatures)}`);
    features.push(...supportedFeatures.map((feature) => ({ feature: feature.type, active: feature.value })));

    // 日期型
    const dateFeatures = this.planFeature.getDateFeatures();
    features.push(...dateFeatures.map((feature) => ({ feature: feature.type, value: feature.value })));

    return features;
  }
}

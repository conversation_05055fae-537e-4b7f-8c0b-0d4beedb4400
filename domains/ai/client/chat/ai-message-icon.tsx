import { ReactNode } from 'react';
import type { AIIntentParams } from '@bika/types/ai/bo';
import { TalkExpertKey } from '@bika/types/space/bo';
import type { MemberVO } from '@bika/types/unit/vo';
import type { UserVO } from '@bika/types/user/vo';
import { AvatarSize } from '@bika/ui/components/avatar/index';
import { LogoGraph } from '@bika/ui/components/logo/index';
import { Stack, Box } from '@bika/ui/layouts';
import { NodeIcon } from '@bika/ui/node/icon';

type MessageIconProps =
  | {
      type: 'assistant';
      initAIIntent?: AIIntentParams;
    }
  | {
      type: 'user';
      user?: UserVO | MemberVO | null;
    };

export const MessageIcon = (props: MessageIconProps) => {
  const { type } = props;
  if (type === 'user') {
    const user = props.user;
    return (
      <Stack ml={2} mt={'6px'} flexShrink={0} width={32}>
        <NodeIcon size={AvatarSize.Size32} value={{ kind: 'avatar', avatar: user?.avatar, name: user?.name }} />
      </Stack>
    );
  }

  let messageIcon: ReactNode = null;
  if (type === 'assistant') {
    const initAIIntent = props.initAIIntent;
    const isNode = initAIIntent?.type === 'AI_NODE';
    const isExpert = initAIIntent?.type === 'BUILDER' || initAIIntent?.type === 'SUPERVISOR';
    const expertIcon: { kind: 'expert'; expertKey: TalkExpertKey } =
      isExpert && initAIIntent?.type === 'BUILDER'
        ? { kind: 'expert', expertKey: 'builder' }
        : { kind: 'expert', expertKey: 'supervisor' };
    const avatar = isNode && initAIIntent.icon;
    const nodeName = props.type === 'assistant' && isNode ? initAIIntent?.nodeName : undefined;

    if (avatar) {
      messageIcon = <NodeIcon size={32} value={{ kind: 'avatar', avatar, name: nodeName }} isRound={false} />;
    } else if (!isNode && isExpert) {
      messageIcon = <NodeIcon size={32} value={expertIcon} isRound={false} />;
    } else {
      messageIcon = <NodeIcon size={32} value={{ kind: 'node-resource', nodeType: 'AI' }} isRound={false} />;
    }
  }

  return (
    <Stack mr={2} mt={'6px'} flexShrink={0} width={32}>
      {messageIcon || (
        <Box
          sx={{
            width: '32px',
            height: '32px',
            borderRadius: '4px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            border: '1px solid var(--border-default)',
            background: 'var(--bg-controls)',
            padding: '4px',
          }}
        >
          <LogoGraph size={20} />
        </Box>
      )}
    </Stack>
  );
};

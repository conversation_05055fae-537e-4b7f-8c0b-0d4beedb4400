import assert from 'assert';
import * as fs from 'fs';
import path from 'path';
import { OpenAIWhisperAudio } from '@langchain/community/document_loaders/fs/openai_whisper_audio';
import { BaseLanguageModelInput } from '@langchain/core/language_models/base';
import { AIMessageChunk } from '@langchain/core/messages';
import type { IterableReadableStream } from '@langchain/core/utils/stream';
import { ChatOpenAI } from '@langchain/openai';
import {
  streamObject,
  generateText,
  streamText,
  experimental_generateImage as generateImage,
  type LanguageModelUsage,
  appendResponseMessages,
  JSONValue,
  CoreMessage,
} from 'ai';
import _ from 'lodash';
import { zodResponseFormat } from 'openai/helpers/zod';
import OpenAI from 'openai/index';
import { Stream } from 'openai/streaming';
import { z } from 'zod';
import {
  PresetImageModelsServerConfig,
  PresetLanguageModelServerConfig,
  type IAILanguageModelConfig,
} from '@bika/contents/config/server/ai/ai-model-config';
import { getAIModelPricesConfig } from '@bika/contents/config/server/pricing/ai/ai-models-price';
import { createToolCallRequestContext } from '@bika/domains/ai-skillset/utils';
import { AttachmentSO } from '@bika/domains/attachment/server/attachment-so';
import Logger from '@bika/domains/shared/server/utils/logger/logger';
import { MemberSO } from '@bika/domains/unit/server/member-so';
import {
  AIUsage,
  AIIntentParams,
  AIGenerateImageProps,
  toAIMessageBO,
  toAISDKMessages,
  AISDKMessage,
  PresetLanguageAIModelDef,
} from '@bika/types/ai/bo';
import type { IAIModelSelectBO } from '@bika/types/ai/bo';
import type { AIMessageVO } from '@bika/types/ai/vo';
import { AIChatSO } from './ai-chat/ai-chat-so';
import { AIModelPicker } from './ai-model-picker';
import { createResumableDataStream } from './stream-context';
import {
  convertAIMessageBOArrayToChatMessages,
  AIModelOptions,
  IPromptWithSchema,
  IPrompt,
  IAIStreamYield,
  type IStreamTextReturn,
  type IStreamTextPrompt,
  type IStreamChatReturn,
  type IStreamChatPrompt,
} from './types';
import { AISkillsetServerRegistry } from '../../ai-skillset/server-registry';

export { tool } from 'ai';

export class AISO {
  static async newWizardByMember(unitMemberID: string, intent: AIIntentParams) {
    const newWizardSO = await AIChatSO.create('MEMBER', unitMemberID, intent);
    return newWizardSO;
  }

  static async parseAICreditCost(
    aiModelDef: PresetLanguageAIModelDef | IAIModelSelectBO | undefined,
    aiTokenUsage: LanguageModelUsage,
  ): Promise<AIUsage> {
    let costCredit = 0;

    // 强制弄一个 preset，以作为计算基础，即使是 auto 模型
    let calcModelSelect: IAIModelSelectBO;
    if (!aiModelDef || (typeof aiModelDef === 'object' && aiModelDef.kind === 'auto')) {
      calcModelSelect = { kind: 'preset', model: AIModelPicker.getSystemAIModel() };
    } else if (typeof aiModelDef === 'string') {
      calcModelSelect = { kind: 'preset', model: aiModelDef as PresetLanguageAIModelDef };
    } else if (typeof aiModelDef === 'object' && aiModelDef.kind === 'preset') {
      calcModelSelect = aiModelDef;
    } else {
      assert(aiModelDef.kind === 'custom');
      calcModelSelect = aiModelDef as IAIModelSelectBO;
    }
    assert(calcModelSelect && calcModelSelect.kind !== 'auto', 'AI model select kind must not be auto');

    if (calcModelSelect.kind === 'preset') {
      const modelPriceConfig = getAIModelPricesConfig(calcModelSelect.model);
      const { promptTokens, completionTokens } = aiTokenUsage;
      // filter NAN
      if (!_.isNaN(promptTokens) && !_.isNaN(completionTokens)) {
        // 1000 tokens = 1 credit 四舍五入
        costCredit = Math.round(
          (modelPriceConfig.inputCredit / 1000) * promptTokens +
            (modelPriceConfig.outputCredit / 1000) * completionTokens,
        );
      } else {
        return {
          promptTokens: 0,
          completionTokens: 0,
          totalTokens: 0,
          model: calcModelSelect,
          costCredit: 0,
        };
      }
    }

    return {
      ...aiTokenUsage,
      model: calcModelSelect,
      costCredit,
    };
  }

  static async newWizardByUser(userID: string, intent: AIIntentParams) {
    const newWizardSO = await AIChatSO.create('USER', userID, intent);
    return newWizardSO;
  }

  /**
   * 给指定成员发送消息。
   */
  static async sendMessageToMember(member: MemberSO, intent: AIIntentParams, message: string) {
    const chat = await AISO.newWizardByMember(member.id, intent);

    let aiMessage: AIMessageVO | undefined;

    const user = await member.getUser();
    const ctx = createToolCallRequestContext(user, '');
    const stream = await createResumableDataStream({
      chatId: chat.id,
      execute: async (dataStream) => {
        const result = await chat.resolve(
          ctx,
          {
            type: 'MESSAGE',
            message,
          },
          'en',
          dataStream,
        );
        // console.log('result', result);
        aiMessage = result.message;
      },
      onError: (error) => {
        console.log('call agent action error', error);
        return 'error';
      },
    });
    // read stream
    if (stream) {
      const reader = stream.getReader();
      while (true && reader) {
        const { done, value } = await reader.read();
        // console.log('value----', value);
        if (done) {
          break;
        }
      }
    }
    return aiMessage;
  }

  /**
   * 将附件的音频转换成文字
   *
   * @param attachmentSO
   * @returns
   */
  public static async audio2TextByAttachment(attachmentSO: AttachmentSO) {
    const blob = await attachmentSO.getObjectBlob();
    const buffer = await blob.arrayBuffer();

    // 创建临存文件到/tmp，用完就删
    const localTmpFilePath = `/tmp/${attachmentSO.path}`;

    // Ensure the directory path
    const directoryPath = path.dirname(localTmpFilePath);
    if (!fs.existsSync(directoryPath)) {
      // If it doesn't exist, create the directory
      fs.mkdirSync(directoryPath, { recursive: true });
    }

    fs.writeFileSync(localTmpFilePath, new Uint8Array(buffer));

    const res = await AISO.audio2Text(localTmpFilePath);

    fs.unlinkSync(localTmpFilePath);
    return res;
  }

  private static getLangChainModelConfigFromOptions(options: AIModelOptions): IAILanguageModelConfig {
    const theOptions = options;
    assert(theOptions);

    const theModel = theOptions.model || 'openai/gpt-4.1';
    assert(typeof theModel === 'string', 'Model must be a string identifier in this method');

    const modelConfig = PresetLanguageModelServerConfig[theModel];
    return modelConfig;
  }

  private static getLangChainModelFromOptions(options: AIModelOptions) {
    const theOptions = options;
    assert(theOptions);
    const modelConfig = this.getLangChainModelConfigFromOptions(options);

    assert(modelConfig.type === 'OPENAI', 'Only OPENAI model is supported for this method');

    const config = {
      model: modelConfig.modelId,
      configuration: {
        baseURL: modelConfig.baseUrl,
        response_format: theOptions.json
          ? {
              type: 'json_object',
            }
          : undefined,
      },
      apiKey: modelConfig.apiKey,
      temperature: theOptions.temperature || 0.5,
    };
    const chatModel = new ChatOpenAI(config);

    return chatModel;
  }

  /**
   * Bika内部系统调用AI，不可选择模型和参数，通过环境变量设置好了
   *
   * @param prompt
   * @param options
   */
  public static async systemInvoke(prompt: string): Promise<string> {
    return this.invoke(prompt, {
      model: AIModelPicker.getSystemAIModel(),
      temperature: 0,
    });
  }

  /**
   * Bika内部系统调用AI，不可选择模型和参数，通过环境变量设置好了
   * @param prompt
   * @param options
   */
  public static async systemStream(prompt: BaseLanguageModelInput): Promise<IterableReadableStream<AIMessageChunk>> {
    return this.streamByLangchain(prompt, {
      model: AIModelPicker.getSystemAIModel(),
      temperature: 0,
    });
  }

  public static async invoke(prompt: string, options: AIModelOptions) {
    return this.invokeByOpenAI(prompt, options);
  }

  /**
   * 基于 AI SDK 的流式调用，支持DeepSeek Reasoning （Danger，不支持 user 和 log记录调用，仅限于测试、Make 命令试用
   *
   * @param prompt
   * @param options
   * @returns
   */
  public static async *dangerStreamYield(
    prompt: Pick<IPrompt, 'prompt'>,
    options: AIModelOptions,
    printConsoleDebug: boolean = true,
  ): AsyncGenerator<IAIStreamYield> {
    const model = await AIModelPicker.getLanguageModel(options);

    const aiSDKStream = await streamText({
      prompt: prompt.prompt,
      model,
    });
    // const aiSDKStream = await this.streamText(prompt, options);

    let isReasoning = false;
    let reasoningContent = '';
    let content = '';

    if (printConsoleDebug) process.stdout.write(`${new Date()} - ${options.model} - Start AI streaming......\n`);

    const { fullStream } = aiSDKStream;

    for await (const sPart of fullStream) {
      let chunkStr;
      if (sPart.type === 'reasoning') {
        chunkStr = sPart.textDelta;
        reasoningContent = reasoningContent.concat(chunkStr);
        isReasoning = true;
      } else if (sPart.type === 'text-delta') {
        chunkStr = sPart.textDelta;
        content = content.concat(chunkStr);
        isReasoning = false;
      } else if (sPart.type === 'step-start' || sPart.type === 'step-finish' || sPart.type === 'finish') {
        // ignore
      } else {
        console.error(sPart);
        throw new Error(`Unknown stream part type: ${sPart.type}`);
      }

      if (printConsoleDebug) process.stdout.write(chunkStr || '');

      yield {
        isReasoning,
        chunkContent: chunkStr,
        reasoningContent,
        content,
      };
    }
  }

  /**
   *
   * @param prompt
   * @param options
   *
   * @deprecated 改用 AI SDK
   */
  public static async *streamYield2(prompt: string, options: AIModelOptions): AsyncGenerator<IAIStreamYield> {
    const aistream = await this.streamByOpenAI(prompt, options);

    let isReasoning = false;
    let reasoningContent = '';
    let content = '';

    process.stdout.write(`${new Date()} - Start AI streaming......`);

    for await (const chunk of aistream) {
      let chunkStr;

      const delta = chunk.choices[0].delta as { reasoning_content?: string };
      if (delta.reasoning_content) {
        chunkStr = delta.reasoning_content;
        reasoningContent = reasoningContent.concat(chunkStr);
        isReasoning = true;
        process.stdout.write(chunkStr);
      } else {
        chunkStr = chunk.choices[0].delta?.content || '';
        content = content.concat(chunkStr);
        isReasoning = false;
        process.stdout.write(chunkStr);
      }
      yield {
        isReasoning,
        chunkContent: chunkStr,
        reasoningContent,
        content,
      };
    }
  }

  /**
   *
   * @param prompt
   * @param options
   * @returns
   *
   * @deprecated 改用 AI SDK
   */
  public static async streamByOpenAI(prompt: string, options: AIModelOptions) {
    const stream = this.createOpenAIChatCompletion(prompt, options, true);
    return stream as Promise<Stream<OpenAI.Chat.Completions.ChatCompletionChunk>>;
  }

  /**
   *
   * @param prompt
   * @param options
   * @param streaming
   * @returns
   *
   * @deprecated 改用 AI SDK
   *
   */
  public static async createOpenAIChatCompletion(
    prompt: string | OpenAI.Chat.Completions.ChatCompletionMessageParam[],
    options: AIModelOptions,
    streaming: boolean,
  ) {
    const theModel = options.model || AIModelPicker.getSystemAIModel();
    assert(typeof theModel === 'string', 'Model must be a string identifier in this method');
    const modelConfig = PresetLanguageModelServerConfig[theModel];

    assert(modelConfig.type === 'OPENAI', 'Only OPENAI model is supported for this method');

    const client = new OpenAI({
      apiKey: options.apiKey || modelConfig.apiKey, // This is the default and can be omitted
      baseURL: options.baseUrl || modelConfig.baseUrl,
    });

    let params: OpenAI.Chat.ChatCompletionCreateParams['response_format'];
    if (options.json === true) {
      params = { type: 'json_object' };
    } else if (options.json && options.json instanceof z.ZodType) {
      params = zodResponseFormat(options.json, 'data');
      // console.log(params);
      // console.log(JSON.stringify(params, undefined, 2));
      // console.log(zodToJsonSchema(options.json, 'data'));
    } else if (options.json && typeof options.json === 'object') {
      params = options.json;
    }
    const chatCompletion = await client.chat.completions.create({
      messages: typeof prompt === 'string' ? [{ role: 'user', content: prompt }] : prompt,
      model: modelConfig?.modelId || theModel,
      response_format: params,
      stream: streaming,
    });
    return chatCompletion;
  }

  /**
   *
   * 快速执行AI指令，使用OpenAI原生SDK，支持structure output严谨格式输出
   *
   * @param prompt
   * @param zodSchema
   *
   * @deprecated 改用 AI SDK
   */
  public static async invokeByOpenAI(prompt: string, options: AIModelOptions) {
    const chatCompletion = (await this.createOpenAIChatCompletion(
      prompt,
      options,
      false,
    )) as OpenAI.Chat.Completions.ChatCompletion;

    const content = chatCompletion.choices[0].message.content;
    return content!;
  }

  /**
   *
   * @param prompt
   * @param debug 是否打印AI过程
   * @returns
   */
  public static async systemInvokeJSON(prompt: string): Promise<any> {
    const res = await this.invoke(prompt, {
      model: AIModelPicker.getSystemAIModel(),
      json: true,
    });

    const jsonStr = res as string;
    try {
      return JSON.parse(jsonStr);
    } catch (e) {
      console.log('AI invokeJSON parseJson error', e, ' return Str', jsonStr);
      return {};
    }
  }

  public static async invokeByAISDK(userPrompt: string, options: AIModelOptions) {
    const model = await AIModelPicker.getLanguageModel(options);
    const { text } = await generateText({
      model,
      prompt: userPrompt,
    });
    return text;
  }

  // public static async dataStreamText(
  //   prompt: IPrompt,
  //   options: AIModelOptions,
  //   dataStreamWriter?: DataStreamWriter,
  // ): Promise<StreamTextResult<ToolSet, any>> {
  //   const streamResult = await AISO.streamByAISDK(prompt, options);
  //   if (dataStreamWriter) streamResult.mergeIntoDataStream(dataStreamWriter);
  //   return streamResult;
  // }

  // private static async *_dataStreamGenerator(
  //   streamResult: StreamTextResult<ToolSet, any>,
  // ): AsyncGenerator<DataStreamString> {
  //   const dataStream = streamResult.toDataStreamResponse({ sendReasoning: true });

  //   const reader = dataStream.body!.getReader();
  //   const decoder = new TextDecoder('utf-8');

  //   let done = false;
  //   while (!done) {
  //     const { done: streamDone, value } = await reader.read();
  //     done = streamDone;

  //     if (value) {
  //       const chunk = decoder.decode(value, { stream: true });
  //       // console.log('Received chunk:', chunk);
  //       yield chunk as DataStreamString;

  //       // 处理每个事件
  //       // const events = chunk.split('\n\n');
  //       // events.forEach((event) => {
  //       //   if (event) {
  //       //     console.log('Event:', event);
  //       //   }
  //       // });
  //     }
  //   }

  /**
   * AI 生图，通常不要直接调用，用 Attachment.generateImages，有 log 和 usage 记录
   *
   * @param props
   * @returns
   */
  public static async generateImages(
    props: AIGenerateImageProps,
    // prompt: string,
    // size: `${number}x${number}` | undefined = undefined,
    // n: number | undefined = undefined,
  ) {
    const { prompt, size, n } = props;
    const imageModelConfig = PresetImageModelsServerConfig[props.imageModel];
    const provider = AIModelPicker.parseCustomAISDKProvider(imageModelConfig);

    // Check if the provider supports image generation
    if (!('image' in provider) || typeof provider.image !== 'function') {
      throw new Error(
        `Provider type '${imageModelConfig.type}' does not support image generation. Please use a provider that supports image generation (e.g., OpenAI, Azure AI).`,
      );
    }

    const result = await generateImage({
      model: provider.image(imageModelConfig.modelId), // imageModelConfig.modelId // openai.image('dall-e-3'),
      prompt,
      size: size as `${number}x${number}` | undefined,
      n,
    });
    const { images } = result;
    console.log('Generated images:', images, props);
    return images;
  }

  public static async imageToText(imageUrl: string): Promise<string> {
    const provider = AIModelPicker.getAIProviderByOptions({ model: 'openai/gpt-4o' });
    // url to base64
    const response = await fetch(imageUrl);
    const buffer = await response.arrayBuffer();
    const base64 = Buffer.from(buffer).toString('base64');
    const mimeType = response.headers.get('content-type') || 'image/png';
    const imageData = `data:${mimeType};base64,${base64}`;
    const { text } = await generateText({
      model: provider('gpt-4o'),
      messages: [
        {
          role: 'user',
          content: [
            {
              type: 'text',
              text: `请提取并返回图片中的所有文本内容，要求：
1. 保持原有的格式和布局
2. 保留所有换行和空格
3. 按照从上到下、从左到右的顺序排列
4. 不要添加任何解释或描述
5. 如果有表格，请使用适当的格式保持表格结构
6. 只返回文本内容，不要返回其他描述`,
            },
            {
              type: 'image',
              image: imageData,
            },
          ],
        },
      ],
      system:
        'You are an OCR expert. Extract all text from images while preserving the original formatting, layout, and structure exactly as it appears in the image.',
      maxTokens: 1000,
      temperature: 0, // 设置为0以获得更一致的结果
    });
    return text;
  }

  public static async fileToTextStream(prompt: { messages: CoreMessage[]; system: string }, options: AIModelOptions) {
    const model = await AIModelPicker.getLanguageModel(options);
    const { messages, system } = prompt;
    return streamText({
      model,
      system,
      messages,
      onError: (error) => {
        console.error('🚀 ~ fileToTextStream ~ error:', error);
        throw error;
      },
    });
  }

  public static async streamObject<OBJECT>(
    prompt: IPromptWithSchema<OBJECT>,
    options: Omit<AIModelOptions, 'onChunk' | 'onStepFinish'>,
  ) {
    const result = streamObject<OBJECT>({
      model: await AIModelPicker.getLanguageModel(options),
      prompt: prompt.prompt,
      system: prompt.system,
      messages: prompt.messages ? await convertAIMessageBOArrayToChatMessages(prompt.messages) : undefined,
      schema: prompt.schema,
    });
    return result;
  }

  /**
   *  调去 AI LLM， streaming 式返回，同时获取AI Message BO和 Usage
   *
   * @param prompt
   * @param options
   * @param dataStreamWriter
   * @param returnPrompts 返回消息体里，用户下一个可以说话的 prompts 建议
   * @returns
   */
  public static async streamChat(
    // 强制使用 messages，确保 chat history 正确被使用
    prompt: IStreamChatPrompt,
    options: AIModelOptions,
    msgOptions: {
      // 默认 的AISDK，stream，是会卡在 await steps 的
      // 是否直接消耗整个 stream？  默认都自动从后台开始消费，
      // 若 disableAutoStream = true，则消耗则依赖于客户端，客户端 刷新就会被打断，消息无保存，避免消耗更多 tokens
      // 若 autoStream，则浏览器刷新后，直接取回 stream 继续
      disableAutoStream?: boolean;

      // 返回 prompt tips？控制从 DataStream.data 返回
      returnPrompts?: string[];
    },
  ): Promise<IStreamChatReturn> {
    let skillsetTools = prompt.skillsets
      ? await AISkillsetServerRegistry.parseAISDKToolsets(prompt.skillsets, {
          dataStreamWriter: options.dataStreamWriter,
          user: prompt.user,
          space: prompt.space,
        })
      : undefined;

    if (prompt.tools) {
      // 再度合并, skillsets + tools
      skillsetTools = skillsetTools
        ? await AISkillsetServerRegistry.mergeToolSet([prompt.tools, skillsetTools])
        : prompt.tools;
    }

    // reassign
    // eslint-disable-next-line no-param-reassign
    prompt.tools = skillsetTools;

    if (options.dataStreamWriter) {
      if (prompt.skillsets) {
        options.dataStreamWriter.writeData({ type: 'tools', tools: Object.keys(skillsetTools || {}) });
        options.dataStreamWriter.writeMessageAnnotation({
          type: 'skillsets',
          skillsets: prompt.skillsets,
        } as JSONValue);
      }
    }
    const { result: streamResult, prompt: retPrompt, options: retOptions } = await this.streamText(prompt, options);

    if (msgOptions.disableAutoStream !== true) {
      for await (const _chunk of streamResult.fullStream) {
        //
      }
    }
    const resp = await streamResult.response;
    const responseMessages = resp.messages;

    const newMessages = appendResponseMessages({ messages: toAISDKMessages(prompt.messages), responseMessages });
    // 这里未必是一定新建了 message，有可能是修改了最后一个 message，比如，tool result，并不会新建 msg，而是改掉上一个 tool call 的内容
    const newMessage = newMessages[newMessages.length - 1];

    // 将skillsets 添加到 annotations 里
    const annotations = newMessage.annotations || [];
    if (prompt.skillsets) {
      annotations.push({ type: 'skillsets', skillsets: prompt.skillsets } as JSONValue);
      newMessage.annotations = annotations;
    }

    const usage = await this.parseAICreditCost(options.model, await streamResult.usage);
    return {
      message: toAIMessageBO(newMessage as AISDKMessage),
      usage,
      result: streamResult,
      prompt: retPrompt,
      options: retOptions,
    };
  }

  /**
   * 不带 usage cost 统计、不带 skillsets parsing，如是聊天，请使用 streamChat
   *
   * @param prompt
   * @param options
   * @returns
   */
  public static async streamText(prompt: IStreamTextPrompt, options: AIModelOptions): Promise<IStreamTextReturn> {
    if (!options.dataStreamWriter) {
      console.warn(
        `[WARN]Warning, AISO.streamByAISDK's dataStreamWriter is undefined, ensure it is only under unit test mode.
[WARN]Don't make undefined datastream writer on production environment (by Kelly)`,
      );
    }
    const model = await AIModelPicker.getLanguageModel(options);

    if (options.dataStreamWriter) {
      options.dataStreamWriter.writeData({ model: model.modelId, type: 'model' });
    }
    const result = await streamText({
      model,
      onFinish: (data) => {
        if (options.onFinish) options.onFinish(data);
      },
      onChunk: (event) => {
        // if (isInUnitTest()) {
        //   console.log(`[AISO.streamText on Unit Test] onChunk: `, event);
        // }
        if (options.onChunk) {
          options.onChunk(event);
        }
      },
      onError: (error) => {
        if (options.onError) {
          options.onError(error);
        }
      },
      onStepFinish: (stepResult) => {
        // if (isInUnitTest()) {
        //   console.log(`[AISO.streamText on Unit Test] onStepFinish: `, stepResult);
        // }
        if (options.onStepFinish) {
          options.onStepFinish(stepResult);
        }
      },
      prompt: prompt.prompt,
      system: prompt.system,
      messages: prompt.messages ? await convertAIMessageBOArrayToChatMessages(prompt.messages) : undefined,
      tools: prompt.tools,
      maxSteps: prompt.maxSteps,
      // ...params,
      // ...prompt,
    });

    if (options.dataStreamWriter) result.mergeIntoDataStream(options.dataStreamWriter);

    // if (isInUnitTest()) {
    //   console.warn('[WARN] AISO.streamText is running in Unit Test.', prompt, options, model);
    // }

    return { result, prompt, options };
  }

  /**
   *
   * 光速执行AI，可自由选择模型 (LangChain版)
   *
   * @param prompt
   * @param jsonMode 必定返回JSON
   * @returns
   */
  public static async invokeByLangchain(prompt: BaseLanguageModelInput, options: AIModelOptions): Promise<string> {
    const chatModel = this.getLangChainModelFromOptions(options);

    try {
      const aiMsg = await chatModel.invoke(prompt);
      return aiMsg.content as string;
    } catch (error) {
      Logger.error('AI invoke error', error);
      throw new Error('AI error, please try again later or contact the administrator');
    }
  }

  public static async streamByLangchain(
    prompt: BaseLanguageModelInput,
    options: AIModelOptions,
  ): Promise<IterableReadableStream<AIMessageChunk>> {
    const chatModel = this.getLangChainModelFromOptions(options);
    const stream = chatModel.stream(prompt);
    return stream;
  }

  /**
   * 音频到文字
   *
   * @param audioPath
   * @returns
   */
  public static async audio2Text(audioPath: string | Blob): Promise<string | null> {
    const loader = new OpenAIWhisperAudio(audioPath);
    const docs = await loader.load();
    if (docs.length > 0) {
      return docs[0].pageContent;
    }
    return null;
  }
}

import assert from 'assert';
import _ from 'lodash';
import { errors, ServerError } from '@bika/contents/config/server/error';
import { DatabaseSO } from '@bika/domains/database/server/database-so';
import { replaceVariablesInString } from '@bika/domains/shared/server';
import { DatabaseRecordModel } from '@bika/server-orm';
import { AIWriterResponse, IAIModelSelectBO } from '@bika/types/ai/bo';
import { iStringParse } from '@bika/types/i18n/bo';
import { BaseAIWriter } from './base-ai-writer';
import { AISO } from '../ai-so';

export class RecordCellFillingAIWriter extends BaseAIWriter {
  async write(userPrompt: string): Promise<AIWriterResponse> {
    const { model, prompt } = await this.getModelAndPrompt(userPrompt);

    const { result } = await AISO.streamText(
      {
        user: this._user,
        prompt,
      },
      {
        model,
      },
    );

    for await (const chunk of result.fullStream) {
      if (chunk.type === 'error') {
        throw chunk.error;
      }
    }

    const data = await result.text;
    const usage = await result.usage;

    return {
      data,
      usages: [await AISO.parseAICreditCost(model, usage)],
      success: true,
      message: 'success',
    };
  }

  private async getModelAndPrompt(userPrompt: string): Promise<{ model?: IAIModelSelectBO; prompt: string }> {
    assert(this._writerOptions.type === 'RECORD_CELL_FILLING', 'type must be RECORD_CELL_FILLING');

    const { recordId, databaseId, fieldId, field, cells } = this._writerOptions;
    const database = await DatabaseSO.init(databaseId);
    const getRecord = async () => {
      let record: Pick<DatabaseRecordModel, 'data' | 'values' | 'computed'> | undefined;
      if (cells) {
        record = {
          data: cells,
          values: {},
          computed: {},
        };
      } else if (recordId) {
        record = (await database.getRecord(recordId)).model;
      } else {
        record = (await database.getFirstRecord())?.model;
      }
      return record;
    };
    const record = await getRecord();
    if (!record) {
      throw new ServerError(errors.database.ai_writer_no_record_found);
    }
    const fields = database.getFields();
    const fieldMap = _.keyBy(fields, 'id');
    const fieldBO = fieldId ? fieldMap[fieldId]?.toBO() : field;
    if (!fieldBO) {
      throw new ServerError(errors.database.field_not_found, {
        key: fieldId,
        databaseName: database.getName(),
      });
    }
    assert(fieldBO.type === 'AI_TEXT');
    const fieldPrompt = replaceVariablesInString(fieldBO.property.prompt || '', (fldId: string) => {
      const replaceField = fieldMap[fldId];
      if (!replaceField) {
        return '';
      }
      const data = record.values?.[fldId] || record.data?.[fldId] || record.computed?.[fldId] || '';
      if (data) {
        return data;
      }
      return iStringParse(replaceField.name, this._reqContext.locale);
    }).replace(/<%=|%>/g, '');
    let model: IAIModelSelectBO | undefined;
    // use type to determine if it's a old property configuration
    if ('type' in fieldBO.property && fieldBO.property.type) {
      model = { kind: 'auto' };
    } else {
      model = fieldBO.property.model;
    }
    return {
      model,
      prompt: fieldPrompt + userPrompt,
    };
  }
}

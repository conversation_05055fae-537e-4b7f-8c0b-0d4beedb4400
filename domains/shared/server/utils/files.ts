import fs from 'node:fs';
import path from 'node:path';
import os from 'os';
import axios from 'axios';
import { generateNanoID } from 'basenext/utils/nano-id';
import mime from 'mime-types';

/**
 * 从axios响应头中获取指定字段的值，处理大小写不敏感问题
 * @param headers axios响应头对象
 * @param fieldName 字段名
 * @returns 字段值或null
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
function getHeaderValue(headers: Record<string, any>, fieldName: string): string | null {
  const lowerFieldName = fieldName.toLowerCase();
  return headers[fieldName] || headers[lowerFieldName] || headers[fieldName.toUpperCase()];
}

/**
 * 下载成blob
 * @param url 下载地址
 * @param ext 文件后缀
 * @returns Blob 二进制
 */
export async function httpGetObjectBlob(url: string, ext: string): Promise<Blob> {
  const getResponse = await axios({
    url,
    method: 'GET',
    responseType: 'arraybuffer', // blob只能用于浏览器，这里arraybuffer用于nodejs
  });
  const blob = new Blob([getResponse.data], { type: mime.lookup(ext) as string });
  const filePath = path.join(os.tmpdir(), 'blobtest.m4a');
  fs.writeFileSync(filePath, getResponse.data, 'binary');
  return blob;
}

/**
 * 资源地址下载到本地 /tmp 目录里
 *
 * @param url
 */
export async function downloadUrlToTmpFile(url: string): Promise<string> {
  const response = await axios({
    url,
    method: 'GET',
    responseType: 'stream',
  });

  const contentType = getHeaderValue(response.headers, 'content-type');
  if (!contentType) {
    throw new Error('Content type not found');
  }

  // Determine the file extension based on the content type
  const extension = mime.extension(contentType);
  if (!extension) {
    throw new Error('Unsupported content type');
  }
  // Generate a unique file name
  const fileName = `downloaded.${extension}`;
  const dirPath = path.join(os.tmpdir(), generateNanoID(''));
  const localFilePath = path.join(dirPath, fileName);
  await fs.promises.mkdir(dirPath, { recursive: true });

  const writer = fs.createWriteStream(localFilePath);

  response.data.pipe(writer);

  return new Promise((resolve, reject) => {
    writer.on('finish', () => resolve(localFilePath));
    writer.on('error', reject);
  });
}

/**
 * Buffer下载到本地
 */
export async function bufferToFile(buffer: Buffer, ext: string): Promise<string> {
  const dirPath = path.join(os.tmpdir(), generateNanoID(''));
  const localFilePath = path.join(dirPath, `downloaded.${ext}`);
  await fs.promises.mkdir(dirPath, { recursive: true });
  fs.writeFileSync(localFilePath, new Uint8Array(buffer));
  return localFilePath;
}

/**
 * 从URL中获取mimeType的方法
 * 优先从URL参数中获取，如果没有则通过HTTP HEAD请求获取Content-Type，最后根据文件扩展名推断
 * @param url 文件URL
 * @returns Promise<string> mimeType
 */
export async function getMimeTypeFromUrl(url: string): Promise<string> {
  try {
    const urlObj = new URL(url);

    // 1. 首先尝试从URL参数中获取mimeType
    const mimeTypeFromParams = urlObj.searchParams.get('mimeType');
    if (mimeTypeFromParams) {
      return mimeTypeFromParams;
    }

    // 2. 根据文件扩展名使用mime-types库推断mimeType
    const pathname = urlObj.pathname;
    const extension = pathname.split('.').pop()?.toLowerCase();

    if (extension) {
      const mimeType = mime.lookup(extension);
      if (mimeType) {
        return mimeType;
      }
    }

    // 3. 如果扩展名推断失败，尝试通过HTTP HEAD请求获取Content-Type
    try {
      const response = await axios({
        url,
        method: 'HEAD',
        timeout: 5000, // 添加超时设置，避免长时间等待
      });

      if (response.status === 200) {
        const contentType = getHeaderValue(response.headers, 'content-type');
        if (contentType) {
          // 提取主要的mime type，去掉charset等参数
          return contentType.split(';')[0].trim();
        }
      }
    } catch (axiosError) {
      console.warn('Failed to fetch Content-Type from URL:', axiosError);
    }

    // 4. 默认返回通用二进制类型
    return 'application/octet-stream';
  } catch (error) {
    console.error('Error getting mimeType from URL:', error);
    return 'application/octet-stream';
  }
}

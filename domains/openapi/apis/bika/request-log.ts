import { generateNanoID } from 'basenext/utils/nano-id';
import { Context, Next } from 'hono';
import { db } from '@bika/server-orm';
import { parseAttributesFromRequest } from '@bika/server-orm/utils';
import { SpaceSO } from '../../../space/server';

const generateRequestId = () => generateNanoID('req');

/**
 * extract /v2/spaces/spc0bH6OsKFku7mmXWJq0Rnr/resources/databases/datIETWuI47wpCoUeywktKrN/records
 */
const extractPathParams = (path: string): { spaceId?: string; resourceId?: string } => {
  const segments = path.split('/');
  const spacesLocation = segments.findIndex((s) => s === 'spaces');
  if (spacesLocation !== -1) {
    const res = { spaceId: segments[spacesLocation + 1] };
    const resourcesLocation = segments.findIndex((s) => s === 'resources');
    if (resourcesLocation !== -1) {
      const resourceId = segments[resourcesLocation + 2];
      return {
        ...res,
        resourceId,
      };
    }
    return res;
  }
  return {};
};

/**
 * 请求日志中间件
 */
export const requestLogMiddleware = async (c: Context, next: Next) => {
  const { spaceId, resourceId } = extractPathParams(c.req.path);

  if (!spaceId) {
    await next();
    return;
  }

  // 校验是否超量请求
  const space = await SpaceSO.init(spaceId);
  const entitlement = await space.getEntitlement();
  // console.log(`space ${spaceId}: ${c.req.method} ${c.req.url}`);
  await entitlement.checkUsageExceed({ feature: 'API_REQUEST' });

  const requestId = generateRequestId();
  c.header('X-Request-Id', requestId);

  const userId = c.var.context.session.userId;
  const userToken = c.var.token.token;

  const sessionAttributes = parseAttributesFromRequest(c.req.raw.headers);

  // 读取请求体（克隆后读取避免阻塞）
  const clonedReq = c.req.raw.clone();
  const requestBody = await clonedReq.text();

  const baseLog = {
    id: requestId,
    endpoint: c.req.path,
    method: c.req.method,
    request: JSON.stringify(requestBody),
    space_id: spaceId,
    resource_id: resourceId,
    user_id: userId,
    user_token: userToken,
    client_ip: sessionAttributes.ip,
  };

  if (clonedReq.method === 'GET') {
    const queryParams = new URLSearchParams(clonedReq.url.split('?')[1]);
    Object.assign(baseLog, { query: JSON.stringify(Object.fromEntries(queryParams.entries())) });
  }

  try {
    await next();
    const statusCode = c.res.status;
    const cloneRes = c.res.clone();
    const responseBody = await cloneRes.text();
    db.log.write({
      kind: 'OPENAPI_REQUEST_LOG',
      ...baseLog,
      response: JSON.stringify(responseBody),
      status_code: statusCode,
      success: statusCode >= 200 && statusCode < 300,
    });
  } catch (error) {
    // 错误日志
    db.log.write({
      kind: 'OPENAPI_REQUEST_LOG',
      ...baseLog,
      response: JSON.stringify(error),
      status_code: 500,
      success: false,
    });
    throw error;
  }
};

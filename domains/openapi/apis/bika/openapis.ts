import { OpenAPIHono } from '@hono/zod-openapi';
import { HTTPException } from 'hono/http-exception';
import { errors, ServerError } from '@bika/contents/config/server/error';
import { requestErrorHandler } from '@bika/domains/shared/server/utils/logger/sentry';
import { ResponseVOBuilder } from '@bika/types/openapi/vo';
import { initAutomationRoutes } from './automation';
import v1Route from './database/v1';
import v2Route from './database/v2';
import { initNodeRoutes } from './node';
import { initOutgoingWebhookRoutes } from './outgoing-webhooks';
import { rateLimiterMiddleware } from './rate-limiter';
import { initSpaceRoutes } from './space';
import { initSystemRoutes } from './system';
import type { ContextVariable } from './types';
import { initUnitRoutes } from './unit';
import { initUserRoutes } from './user';
import { authorizeMiddleware } from '../middlewares';
import { requestLogMiddleware } from './request-log';

export const app = new OpenAPIHono<{ Variables: ContextVariable }>({
  strict: false,
  defaultHook: (result, c) => {
    // Zod 参数验证错误统一处理
    if (!result.success) {
      const issueErrors = result.error.issues;
      // const errorMap = issueErrors.reduce((acc: Record<string, string>, issue) => {
      //   // 合并字段路径（如：address.street -> "street"）
      //   const key = issue.path.join('.');
      //   acc[key] = issue.message;
      //   return acc;
      // }, {});
      const formattedErrors = issueErrors.map((issue) => issue.message).join(', ');
      // const formatErrors = result.error.format();
      console.error('Zod Validation Error:', formattedErrors);
      const res = ResponseVOBuilder.error(formattedErrors, 400);
      return c.json(res, 400);
    }
    return result;
  },
});

app.doc('/openapi.json', {
  openapi: '3.0.0',
  info: {
    version: process.env.VERSION || 'UNKNOW VERSION',
    title: 'Bika.ai Official OpenAPI Documentation',
    'x-logo': {
      url: '/assets/icons/logo/bika-logo-text.png',
      altText: 'Bika LOGO',
      href: '/',
    },
  },
  // Url prefix
  servers: [
    {
      url: '/api/openapi/bika',
    },
  ],
});

// 统一异常处理
app.onError((err, c) => {
  requestErrorHandler(err, c);

  //   console.log(err instanceof ServerError);
  if (err instanceof ServerError) {
    // 业务错误
    const res = ResponseVOBuilder.error(err.getMessage(), err.code);
    if (err.code === errors.billing.usage_exceed_limit.code || err.code === errors.billing.feature_locked.code) {
      return c.json(res, 403); // 使用超限错误返回 403
    }
    return c.json(res, 500);
  }
  // HTTPException 异常处理
  if (err instanceof HTTPException) {
    const res = ResponseVOBuilder.error(err.message, err.status);
    return c.json(res, err.status);
  }
  // 非业务错误
  const unknownServerError = ResponseVOBuilder.error(err.message, 500);
  return c.json(unknownServerError, 500);
});

initSystemRoutes(app);

// 上面无鉴权

app.use(authorizeMiddleware);
app.use(rateLimiterMiddleware);
app.use(requestLogMiddleware);

// 下面有鉴权
initAutomationRoutes(app);
initSpaceRoutes(app);
initNodeRoutes(app);
initUnitRoutes(app);
v1Route.initResourceDatabaseRoutes(app);
v1Route.initResourceDatabaseRecordsRoutes(app);
v2Route.initDatabaseRecordsRoutes(app);
initOutgoingWebhookRoutes(app);
initUserRoutes(app);

export default app;

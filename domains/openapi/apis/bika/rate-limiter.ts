import { Context, Next } from 'hono';
import { RateLimiterRedis } from 'rate-limiter-flexible';
import { SpaceSO } from '@bika/domains/space/server/space-so';
import { db } from '@bika/server-orm';
import { trpc, TRPCError } from '@bika/server-orm/trpc';
import { CONST_PREFIX_SPACE, CONST_RESOURCE_IDS } from '@bika/types/database/vo';

/**
 * Create a rate limiter
 * @param points - The number of points to consume
 * @param duration - The duration of the rate limit, seconds
 * @returns The rate limiter
 */
const apiRateLimiter = async (points: number, duration: number) => {
  // ensure redis connected
  await db.redis.connection;
  const limiter = new RateLimiterRedis({
    storeClient: db.redis.redisOriginClient,
    keyPrefix: 'rate-limiter:',
    useRedisPackage: true,
    points,
    duration,
  });
  return limiter;
};

const getSpaceIdAndNodeIdFromPath = (path: string) => {
  const pathParts = path.split('/');
  const spaceId = pathParts.find((part) => part.startsWith(CONST_PREFIX_SPACE));
  const nodeId = pathParts.find((part) => {
    for (const prefix of CONST_RESOURCE_IDS) {
      if (part.startsWith(prefix)) {
        return part;
      }
    }
    return undefined;
  });
  return { spaceId, nodeId };
};

const getPointAndConsumeKeyBySpace = async (c: Context) => {
  const { spaceId, nodeId } = getSpaceIdAndNodeIdFromPath(c.req.path);
  // get space subscription for api limit
  const userId = c.var.context.session.userId;
  const key = nodeId ? `${nodeId}:${userId}` : userId;
  // get space subscription for api limit
  if (spaceId) {
    const space = await SpaceSO.init(spaceId);
    const entitlement = await space.getEntitlement();
    // if no limit, skip
    if (!entitlement.hasFeature('OPENAPI_RATE')) {
      return { point: undefined, key: undefined };
    }
    // use space subscription for api limit
    const point = entitlement.planFeature.getFixedNumberFeature('OPENAPI_RATE').value;
    return { point, key };
  }
  // default 5 points in every 1 second
  return { point: 5, key };
};

/**
 * 是否跳过调用速率限制
 */
const isSkipRateLimit = (): boolean =>
  // skip rate limit for environment variables config
  process.env.SKIP_RATE_LIMIT === 'true';

/**
 * Rate limiter middleware
 * if openapi, use space id and node id
 * @param option - The option
 * @param option.openapi - If true, use space id and node id
 * @param option.trpc - If true, use client ip
 * @param c - The context
 * @param next - The next middleware
 */
export const rateLimiterMiddleware = async (c: Context, next: Next) => {
  if (isSkipRateLimit()) {
    return next();
  }
  const { key, point } = await getPointAndConsumeKeyBySpace(c);
  // -1 not limit, 0 maybe something wrong
  if (!key || !point || point <= 0) {
    return next();
  }
  const limiter = await apiRateLimiter(point, 1);
  let limited = false;
  const res = await limiter.consume(key).catch((err) => {
    if (err instanceof Error) {
      // Some Redis error
      console.error(`rate limiter error:`, err);
    } else {
      // can't consume
      // If there is no error, rateLimiterRedis promise rejected with number of ms before next request allowed
      limited = true;
    }
    return err;
  });
  if (limited) {
    return c.json(
      {
        code: 429,
        message: 'Rate limit exceeded',
        success: false,
        data: {
          remainingPoints: res.remainingPoints,
          resetTime: new Date(Date.now() + res.msBeforeNext || 1000).toISOString(),
          limit: point,
        },
      },
      429,
    );
  }
  return next();
};

/**
 * Rate limiter middleware for trpc, use in specific router
 * @param opts - The options
 * @param opts.ctx - The context
 * @param opts.next - The next middleware
 */
export const rateLimiterMiddlewareForTrpc = trpc.middleware(async (opts) => {
  const { ctx, next } = opts;
  const headers = ctx.req.headers;
  // 替换 .和: 为 _
  const ip = headers.get('x-forwarded-for')?.split(',')[0].replace(/\./g, '_').replace(/:/g, '_');
  const key = ip || ctx.session?.id;
  if (!key) {
    return next({ ctx });
  }
  const limiter = await apiRateLimiter(5, 1);
  let limited = false;
  await limiter.consume(key).catch((err) => {
    // rate limit exceeded
    limited = true;
    return err;
  });
  if (limited) {
    throw new TRPCError({
      code: 'TOO_MANY_REQUESTS',
      message: 'Rate limit exceeded',
      cause: 'Rate limit exceeded',
    });
  }
  return next({ ctx });
});

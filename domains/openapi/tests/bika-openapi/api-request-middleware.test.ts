import { describe, expect, test, vi } from 'vitest';
import { MockContext, waitForMatchToBeMet } from '@bika/domains/__tests__/mock';
import { DatabaseSO } from '@bika/domains/database/server/database-so';
import { ApiCreateRecordsReqV2 } from '@bika/types/openapi/dto';
import { ApiCreateRecordsRes } from '@bika/types/openapi/vo';
import { UsageType } from '@bika/types/pricing/bo';
import { BikaOpenAPIs } from '../../apis/bika';

describe('API Request Middleware', async () => {
  const { space, user, rootFolder } = await MockContext.initUserContext();
  const token = await user.developer.createToken();

  // 创建记录
  const createRecordsRequest = async (
    databaseId: string,
    body: ApiCreateRecordsReqV2,
  ): Promise<ApiCreateRecordsRes> => {
    const response = await <PERSON>ikaOpenAPIs.request(`/v2/spaces/${space.id}/resources/databases/${databaseId}/records`, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${token.model.token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });
    expect(response.status).toBe(200);
    const responseData = await response.json();
    // console.log('Create Records Response:', responseData);
    return responseData.data as ApiCreateRecordsRes;
  };

  // 请求数限制测试
  test('Api Request Middleware Test', async () => {
    vi.stubEnv('SKIP_RATE_LIMIT', 'true'); // Skip rate limit for testing

    // 创建表
    const databaseNode = await rootFolder.createChildSimple(user, {
      name: 'database',
      resourceType: 'DATABASE',
      fields: [
        {
          name: 'single_text',
          type: 'SINGLE_TEXT',
        },
      ],
    });
    const database = await databaseNode.toResourceSO<DatabaseSO>();

    // 创建记录
    for (let i = 0; i < 5; i++) {
      const toCreateRecords: ApiCreateRecordsReqV2 = {
        records: [
          {
            fields: {
              single_text: `Test Text ${i}`,
            },
          },
        ],
      };
      const createdRecords = await createRecordsRequest(database.id, toCreateRecords);
      expect(createdRecords.records.length).toBe(1);
    }

    // 检查已使用的调用量应该是5次
    const entitlement = await space.getEntitlement();
    const usage = entitlement.getUsage();
    await waitForMatchToBeMet(
      async () => {
        const usedUsage = await usage.getUsedUsageValue('API_REQUEST');
        return usedUsage === 5;
      },
      5000,
      1000,
    );

    // 先模拟替换usage对象
    vi.spyOn(entitlement, 'getUsage').mockImplementation(() => usage);

    // mock 最大次数为5
    vi.spyOn(usage, 'getMaxUsageValue').mockImplementation((type: UsageType) => {
      if (type === 'API_REQUEST') {
        return 5;
      }
      return undefined;
    });
    const maxValue = usage.getMaxUsageValue('API_REQUEST');
    expect(maxValue).toBe(5);

    const { exceed } = await entitlement.isUsageExceed({ feature: 'API_REQUEST' });
    expect(exceed).toBe(true);

    await expect(() => entitlement.checkUsageExceed({ feature: 'API_REQUEST' })).rejects.toThrowError();
  });
});

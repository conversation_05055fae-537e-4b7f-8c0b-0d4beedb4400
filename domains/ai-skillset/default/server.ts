import assert from 'assert';
import { z } from 'zod';
import { AiPageNodeBO } from '@bika/types/ai/bo';
import { Slides, FileArtifactFileTypeSchema } from '@bika/types/ai/vo';
import type { DocumentBO } from '@bika/types/document/bo';
import { Folder, NodeResource } from '@bika/types/node/bo';
import { AIArtifactSO, AIArtifactDTO } from '../../ai-artifacts/ai-artifact-so';
import { NodeSO } from '../../node/server/node-so';
import { ToolSetHandler } from '../types';

// default 的，通常不需要 服务端 toolset
const tools: ToolSetHandler = async (props) => {
  //
  // const image = await BikaImageToolSetHandler(props);
  // console.log('todo');
  assert(true);

  return {
    // 请求澄清，当用户的需求不明确时，使用此工具请求澄清，智能生成 UI 界面，让用户点击鼠标做选择
    // disambiguation: {
    //   description:
    //     'Request clarification when user requirements are unclear, use this tool to request clarification and intelligently generate UI interface (select or form) for user to make selections by clicking',
    //   parameters: z.object({}),
    //   execute: async () => {},
    // },

    //  加载附件
    load_attachment: {
      description:
        'Load and analyze various file types (text, image, audio, video, pdf, excel, word, powerpoint) to extract information and answer user questions. This tool can process any supported file format and provide detailed analysis based on user intent.',
      parameters: z.object({
        fileType: FileArtifactFileTypeSchema.describe(
          'The type/category of the file to be analyzed (text, image, audio, video, pdf, excel, word, powerpoint)',
        ),
        attachmentUrl: z.string().describe('The URL or path of the file to be analyzed and processed'),
        userIntent: z
          .string()
          .describe(
            'The specific questions or requirements the user has about the file (e.g., "summarize the content", "extract key data points", "find specific information", "analyze trends", "compare data")',
          ),
      }),
      execute: async (params, { toolCallId }) => {
        console.log('🚀 ~ load_attachment executing:', params);
        const { attachmentUrl, userIntent, fileType } = params;
        if (!FileArtifactFileTypeSchema.safeParse(fileType).success) {
          throw new Error(`File type is not supported: ${fileType}`);
        }

        // 创建 artifact 用于 server-artifact 机制
        const artifact = await AIArtifactSO.create(props.user, {
          type: 'file',
          toolCallId,
          prompt: {
            system: `You are a comprehensive file analysis assistant capable of processing multiple file formats.

**Your capabilities:**
- Analyze text files, images, audio, video, PDFs, Excel spreadsheets, Word documents, and PowerPoint presentations
- Extract relevant information based on user's specific questions and requirements
- Provide detailed, accurate, and contextual responses
- Use natural, conversational language while maintaining professionalism

**Your approach:**
- First understand the user's intent and specific questions
- Thoroughly analyze the file content to find relevant information
- Provide comprehensive answers that directly address user needs
- Include relevant details, data, or insights from the file
- If the file contains structured data (like spreadsheets), organize and present it clearly

**Important:** Focus on answering the user's specific questions rather than just describing the file. Be helpful, direct, and provide actionable insights when possible.`,
            prompt: `Analyze this file based on the user's questions:


**User Intent:** ${userIntent}

**Your task:**
1. Carefully read and understand the user's specific questions or requirements
2. Thoroughly analyze the file content to extract relevant information
3. Provide a comprehensive response that directly addresses the user's needs
4. If the file contains data, tables, or structured information, present it in an organized manner
5. Include specific details, numbers, or insights that are relevant to the user's questions
6. Use clear, professional language while being conversational

**Response format:**
- Start with a brief overview if the user's question is general
- Provide specific answers to their questions
- Include relevant data, quotes, or examples from the file
- If applicable, suggest insights or recommendations based on the content

Please provide the most helpful and relevant information based on what the user is looking for.`,
          },
          initData: {
            filePath: attachmentUrl,
            fileType,
            content: '',
          },
        });
        // todo 不支持的类型需要抛出异常

        // 流式写入 artifact 数据
        const artifactVO = await artifact.getValue(props.user, {
          dataStreamWriter: props.dataStreamWriter,
        });
        return {
          artifactId: artifactVO.id,
          data: artifactVO.data,
        };
      },
    },
    // [ImageSkillsetName.image_to_text]: {
    //   description: 'Analyze and describe the content of an image, or extract text from it using OCR',
    //   parameters: z.object({
    //     imageUrl: z.string().describe('The URL of the image to analyze or extract text from'),
    //   }),
    //   execute: async (params, { toolCallId }) => {
    //     try {
    //       const { imageUrl } = params;
    //       const text = await AISO.imageToText(imageUrl);

    //       // 创建 artifact 用于 server-artifact 机制
    //       const artifact = await AIArtifactSO.create(props.user, {
    //         type: 'image-text',
    //         toolCallId,
    //         initData: { text, imageUrl },
    //       });

    //       // 流式写入 artifact 数据
    //       const artifactVO = await artifact.getValue(props.user, {
    //         dataStreamWriter: props.dataStreamWriter,
    //       });

    //       // 返回 artifactId 供前端使用
    //       return { artifactId: artifactVO.id, data: artifactVO.data };
    //     } catch (error) {
    //       const errorMessage = error instanceof Error ? error.message : String(error);
    //       throw new Error(`Failed to process image: ${errorMessage}`);
    //     }
    //   },
    // },

    read_node_content: {
      description: 'Get bika node content by nodeId. Only supports nodes with IDs starting with doc, aip, or fold.',
      parameters: z.object({
        nodeId: z.string().describe(`The node id must start with doc, aip, or fold`),
      }),
      execute: async (params, { toolCallId }) => {
        const { nodeId } = params;
        const user = props.user;
        const node = await NodeSO.init(nodeId);
        const bo = await node.toBO();
        const getArtifactCreateDTO = (): AIArtifactDTO => {
          let type: AIArtifactDTO['type'] | undefined;
          let initData: string | { html: string } | { slides: Slides } | NodeResource[] | undefined;
          if (bo.resourceType === 'DOCUMENT') {
            type = 'text';
            initData = (bo as DocumentBO).markdown;
          }
          if (bo.resourceType === 'PAGE') {
            const { data } = bo as AiPageNodeBO;
            if (data?.kind === 'SINGLE_HTML' && data.content) {
              type = 'html';
              initData = {
                html: data.content || '',
              };
            }
            if (data?.kind === 'SLIDES' && data.contents) {
              type = 'slides';
              initData = {
                slides: (data.contents || []) as unknown as Slides,
              };
            }
          }
          if (bo.resourceType === 'FOLDER') {
            type = 'node-resources';
            initData = (bo as Folder).children || [];
          }
          if (!type) {
            throw new Error(`Node resource type is not supported: ${bo.resourceType}`);
          }
          return {
            type,
            initData,
            state: 'SUCCESS',
            toolCallId,
            prompt: {
              system:
                'You are a helpful assistant that analyzes node resources and provides comprehensive summaries based on the artifact data.',
              prompt: `Analyze the following node resource and provide a detailed summary:\n\nResource Type: ${bo.resourceType}\nNode ID: ${nodeId}\n\nPlease provide a comprehensive analysis and summary of this resource based on the artifact data.`,
            },
          } as AIArtifactDTO;
        };
        const artifact = await AIArtifactSO.create(user, getArtifactCreateDTO());
        return {
          artifactId: artifact.id,
          error: artifact.error,
          data: artifact.data,
        };
      },
    },
  };
};
export default tools;

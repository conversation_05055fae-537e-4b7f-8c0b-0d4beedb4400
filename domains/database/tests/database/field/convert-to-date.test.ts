import { describe, expect, test } from 'vitest';
import { MockContext } from '@bika/domains/__tests__/mock';
import { DatabaseSO } from '../../../server/database-so';
import { allFields } from '../field-util';

/**
 * Test Case Collection: 其他类型转换为日期字段
 */
describe.skip('any field convert to date', () => {
  /**
   * Test Case: 其他类型 ---> 日期字段
   */
  test('other field convert to datetime', async () => {
    const { user, member, rootFolder } = await MockContext.initUserContext();

    // 创建全字段的测试表
    const databaseNode = await rootFolder.createChildSimple(user, {
      name: 'database',
      resourceType: 'DATABASE',
      fields: allFields,
    });
    const database = await databaseNode.toResourceSO<DatabaseSO>();

    // 创建记录
    const date = new Date();
    const record1 = await database.createRecord(user, member, {
      // 秒级时间戳(2025-04-12T07:13:04.000Z)
      single_text: '1744441984',
      long_text: '1744441984',
      url: 'https://www.bika.ai', // 不能转
      email: '<EMAIL>', // 不能转
      phone: '46241245', // 不能转
      // 数字类
      // 秒级时间戳(2025-04-22T15:13:04.000Z)
      number: 1745334784,
      currency: 1745334784.23, // 不能转
      percent: 1745334784.1234, // 不能转
      rating: 4.5, // 不能转
      // 日期类
      date_range: `${date.toISOString()}/${date.toISOString()}`, // 取第一个
    });
    const record2 = await database.createRecord(user, member, {
      // 文本类
      // 毫秒级时间戳
      single_text: '1744441984000',
      long_text: '1744441984000',
      url: 'https://www.bika.ai', // 不能转
      email: '<EMAIL>', // 不能转
      phone: '46241245', // 不能转
      // 数字类
      // 毫秒级时间戳
      number: 1745334784,
      currency: 1745334784.23, // 不能转
      percent: 1745334784.1234, // 不能转
      rating: 4.5, // 不能转
      // 日期类
      date_range: `${date.toISOString()}/${date.toISOString()}`, // 取第一个
    });

    const record3 = await database.createRecord(user, member, {
      // 少于10位的字符串
      single_text: '174533478',
      long_text: '174533478',
      url: 'https://www.bika.cn',
      email: 'email test',
      phone: '0755-46241245',
      // 数字类
      // 少于10位的数字
      number: 17453347837172162,
      currency: 174533478.78,
      percent: 174533478.5678,
      rating: 3,
      // 日期类
      date_range: `${date.toISOString()}/${date.toISOString()}`, // 取第一个
    });

    const record4 = await database.createRecord(user, member, {
      // 10位的字符串
      single_text: '17453347821331231',
      long_text: '17453347821331231',
      url: 'https://www.bika.cn',
      email: 'email test',
      phone: '0755-46241245',
      // 数字类
      // 少于10位的数字
      number: 1745334781283912,
      currency: 174533478.78,
      percent: 174533478.5678,
      rating: 3,
      // 日期类
      date_range: `${date.toISOString()}/${date.toISOString()}`, // 取第一个
    });

    // 文本类字段转换为日期
    const singleTextField = database.getFieldByFieldKey('single_text');
    const longTextField = database.getFieldByFieldKey('long_text');
    const urlField = database.getFieldByFieldKey('url');
    const emailField = database.getFieldByFieldKey('email');
    const phoneField = database.getFieldByFieldKey('phone');

    // 单行文本 -> 日期 = (有限支持)
    await singleTextField.update(user, {
      name: 'single_text to date',
      type: 'DATETIME',
      property: {
        dateFormat: 'YYYY-MM-DD',
        includeTime: true,
        timeFormat: 'HH:mm',
      },
    });

    await database.reloadFields();
    let row1 = await database.getRecord(record1.id);
    expect(row1.getCellData(singleTextField.id)).toEqual('2025-04-12T07:13:04.000Z');
    expect(row1.getCellValue(singleTextField.id)).toEqual('2025-04-12 07:13');
    let row2 = await database.getRecord(record2.id);
    expect(row2.getCellData(singleTextField.id)).toEqual('2025-04-12T07:13:04.000Z');
    expect(row2.getCellValue(singleTextField.id)).toEqual('2025-04-12 07:13');
    let row3 = await database.getRecord(record3.id);
    expect(row3.getCellData(singleTextField.id)).toBeUndefined();
    expect(row3.getCellValue(singleTextField.id)).toBeNull();
    let row4 = await database.getRecord(record4.id);
    expect(row4.getCellData(singleTextField.id)).toBeUndefined();
    expect(row4.getCellValue(singleTextField.id)).toBeNull();

    // 长文本 -> 日期 = (有限支持)
    await longTextField.update(user, {
      name: 'long_text to date',
      type: 'DATETIME',
      property: {
        dateFormat: 'YYYY-MM-DD',
        includeTime: true,
        timeFormat: 'HH:mm',
      },
    });
    await database.reloadFields();
    row1 = await database.getRecord(record1.id);
    expect(row1.getCellData(longTextField.id)).toEqual('2025-04-12T07:13:04.000Z');
    expect(row1.getCellValue(longTextField.id)).toEqual('2025-04-12 07:13');
    row2 = await database.getRecord(record2.id);
    expect(row2.getCellData(longTextField.id)).toEqual('2025-04-12T07:13:04.000Z');
    expect(row2.getCellValue(longTextField.id)).toEqual('2025-04-12 07:13');
    row3 = await database.getRecord(record3.id);
    expect(row3.getCellData(longTextField.id)).toBeUndefined();
    expect(row3.getCellValue(longTextField.id)).toBeNull();
    row4 = await database.getRecord(record4.id);
    expect(row4.getCellData(longTextField.id)).toBeUndefined();
    expect(row4.getCellValue(longTextField.id)).toBeNull();

    // URL -> 日期 = (不支持)
    await urlField.update(user, {
      name: 'url to date',
      type: 'DATETIME',
      property: {
        dateFormat: 'YYYY-MM-DD',
        includeTime: true,
        timeFormat: 'HH:mm',
      },
    });
    await database.reloadFields();
    row1 = await database.getRecord(record1.id);
    expect(row1.getCellData(urlField.id)).toBeUndefined();
    expect(row1.getCellValue(urlField.id)).toBeNull();
    row2 = await database.getRecord(record2.id);
    expect(row2.getCellData(urlField.id)).toBeUndefined();
    expect(row2.getCellValue(urlField.id)).toBeNull();
    row3 = await database.getRecord(record3.id);
    expect(row3.getCellData(urlField.id)).toBeUndefined();
    expect(row3.getCellValue(urlField.id)).toBeNull();
    row4 = await database.getRecord(record4.id);
    expect(row4.getCellData(urlField.id)).toBeUndefined();
    expect(row4.getCellValue(urlField.id)).toBeNull();

    // Email -> 日期 = (不支持)
    await emailField.update(user, {
      name: 'email to date',
      type: 'DATETIME',
      property: {
        dateFormat: 'YYYY-MM-DD',
        includeTime: true,
        timeFormat: 'HH:mm',
      },
    });
    await database.reloadFields();
    row1 = await database.getRecord(record1.id);
    expect(row1.getCellData(emailField.id)).toBeUndefined();
    expect(row1.getCellValue(emailField.id)).toBeNull();
    row2 = await database.getRecord(record2.id);
    expect(row2.getCellData(emailField.id)).toBeUndefined();
    expect(row2.getCellValue(emailField.id)).toBeNull();
    row3 = await database.getRecord(record3.id);
    expect(row3.getCellData(emailField.id)).toBeUndefined();
    expect(row3.getCellValue(emailField.id)).toBeNull();
    row4 = await database.getRecord(record4.id);
    expect(row4.getCellData(emailField.id)).toBeUndefined();
    expect(row4.getCellValue(emailField.id)).toBeNull();

    // Phone -> 日期 = (不支持)
    await phoneField.update(user, {
      name: 'phone to date',
      type: 'DATETIME',
      property: {
        dateFormat: 'YYYY-MM-DD',
        includeTime: true,
        timeFormat: 'HH:mm',
      },
    });
    await database.reloadFields();
    row1 = await database.getRecord(record1.id);
    expect(row1.getCellData(phoneField.id)).toBeUndefined();
    expect(row1.getCellValue(phoneField.id)).toBeNull();
    row2 = await database.getRecord(record2.id);
    expect(row2.getCellData(phoneField.id)).toBeUndefined();
    expect(row2.getCellValue(phoneField.id)).toBeNull();
    row3 = await database.getRecord(record3.id);
    expect(row3.getCellData(phoneField.id)).not.toBeUndefined();
    expect(row3.getCellValue(phoneField.id)).not.toBeUndefined();
    row4 = await database.getRecord(record4.id);
    expect(row4.getCellData(phoneField.id)).not.toBeUndefined();
    expect(row4.getCellValue(phoneField.id)).not.toBeUndefined();

    // 数字类字段转换为日期
    const numberField = database.getFieldByFieldKey('number');
    const currencyField = database.getFieldByFieldKey('currency');
    const percentField = database.getFieldByFieldKey('percent');
    const ratingField = database.getFieldByFieldKey('rating');
    const autoNumberField = database.getFieldByFieldKey('auto_number');

    // 数字 -> 日期 (有限支持)
    await numberField.update(user, {
      name: 'number to date',
      type: 'DATETIME',
      property: {
        dateFormat: 'YYYY-MM-DD',
        includeTime: true,
        timeFormat: 'HH:mm',
      },
    });
    await database.reloadFields();
    row1 = await database.getRecord(record1.id);
    expect(row1.getCellData(numberField.id)).toEqual('2025-04-22T15:13:04.000Z');
    expect(row1.getCellValue(numberField.id)).toEqual('2025-04-22 15:13');
    row2 = await database.getRecord(record2.id);
    expect(row2.getCellData(numberField.id)).toEqual('2025-04-22T15:13:04.000Z');
    expect(row2.getCellValue(numberField.id)).toEqual('2025-04-22 15:13');
    row3 = await database.getRecord(record3.id);
    expect(row3.getCellData(numberField.id)).toBeUndefined();
    expect(row3.getCellValue(numberField.id)).toBeNull();
    row4 = await database.getRecord(record4.id);
    expect(row4.getCellData(numberField.id)).toBeUndefined();
    expect(row4.getCellValue(numberField.id)).toBeNull();

    // 货币 -> 日期 (不支持)
    await currencyField.update(user, {
      name: 'currency to date',
      type: 'DATETIME',
      property: {
        dateFormat: 'YYYY-MM-DD',
        includeTime: true,
        timeFormat: 'HH:mm',
      },
    });
    await database.reloadFields();
    row1 = await database.getRecord(record1.id);
    expect(row1.getCellData(currencyField.id)).toBeUndefined();
    expect(row1.getCellValue(currencyField.id)).toBeNull();
    row2 = await database.getRecord(record2.id);
    expect(row2.getCellData(currencyField.id)).toBeUndefined();
    expect(row2.getCellValue(currencyField.id)).toBeNull();
    row3 = await database.getRecord(record3.id);
    expect(row3.getCellData(currencyField.id)).toBeUndefined();
    expect(row3.getCellValue(currencyField.id)).toBeNull();
    row4 = await database.getRecord(record4.id);
    expect(row4.getCellData(currencyField.id)).toBeUndefined();
    expect(row4.getCellValue(currencyField.id)).toBeNull();

    // 百分比 -> 日期(不支持)
    await percentField.update(user, {
      name: 'percent to date',
      type: 'DATETIME',
      property: {
        dateFormat: 'YYYY-MM-DD',
        includeTime: true,
        timeFormat: 'HH:mm',
      },
    });
    await database.reloadFields();
    row1 = await database.getRecord(record1.id);
    expect(row1.getCellData(percentField.id)).toBeUndefined();
    expect(row1.getCellValue(percentField.id)).toBeNull();
    row2 = await database.getRecord(record2.id);
    expect(row2.getCellData(percentField.id)).toBeUndefined();
    expect(row2.getCellValue(percentField.id)).toBeNull();
    row3 = await database.getRecord(record3.id);
    expect(row3.getCellData(percentField.id)).toBeUndefined();
    expect(row3.getCellValue(percentField.id)).toBeNull();
    row4 = await database.getRecord(record4.id);
    expect(row4.getCellData(percentField.id)).toBeUndefined();
    expect(row4.getCellValue(percentField.id)).toBeNull();

    // 评分 -> 日期
    await ratingField.update(user, {
      name: 'rating to date',
      type: 'DATETIME',
      property: {
        dateFormat: 'YYYY-MM-DD',
        includeTime: true,
        timeFormat: 'HH:mm',
      },
    });
    await database.reloadFields();
    row1 = await database.getRecord(record1.id);
    expect(row1.getCellData(ratingField.id)).toBeUndefined();
    expect(row1.getCellValue(ratingField.id)).toBeNull();
    row2 = await database.getRecord(record2.id);
    expect(row2.getCellData(ratingField.id)).toBeUndefined();
    expect(row2.getCellValue(ratingField.id)).toBeNull();
    row3 = await database.getRecord(record3.id);
    expect(row3.getCellData(ratingField.id)).toBeUndefined();
    expect(row3.getCellValue(ratingField.id)).toBeNull();
    row4 = await database.getRecord(record4.id);
    expect(row4.getCellData(ratingField.id)).toBeUndefined();
    expect(row4.getCellValue(ratingField.id)).toBeNull();

    // 自动编号 -> 日期
    await autoNumberField.update(user, {
      name: 'auto_number to date',
      type: 'DATETIME',
      property: {
        dateFormat: 'YYYY-MM-DD',
        includeTime: true,
        timeFormat: 'HH:mm',
      },
    });
    await database.reloadFields();
    row1 = await database.getRecord(record1.id);
    expect(row1.getCellData(autoNumberField.id)).toBeUndefined();
    expect(row1.getCellValue(autoNumberField.id)).toBeNull();
    row2 = await database.getRecord(record2.id);
    expect(row2.getCellData(autoNumberField.id)).toBeUndefined();
    expect(row2.getCellValue(autoNumberField.id)).toBeNull();
    row3 = await database.getRecord(record3.id);
    expect(row3.getCellData(autoNumberField.id)).toBeUndefined();
    expect(row3.getCellValue(autoNumberField.id)).toBeNull();
    row4 = await database.getRecord(record4.id);
    expect(row4.getCellData(autoNumberField.id)).toBeUndefined();
    expect(row4.getCellValue(autoNumberField.id)).toBeNull();

    // 日期类字段转换为数字
    const createdTimeField = database.getFieldByFieldKey('created_time');
    const modifiedTimeField = database.getFieldByFieldKey('modified_time');

    // 创建时间 -> 日期
    await createdTimeField.update(user, {
      name: 'created_time to date',
      type: 'DATETIME',
      property: {
        dateFormat: 'YYYY-MM-DD',
        includeTime: true,
        timeFormat: 'HH:mm',
      },
    });

    await database.reloadFields();
    row1 = await database.getRecord(record1.id);
    expect(row1.getCellData(createdTimeField.id)).toEqual(row1.createdAt.toISOString());
    row2 = await database.getRecord(record2.id);
    expect(row2.getCellData(createdTimeField.id)).toEqual(row2.createdAt.toISOString());
    row3 = await database.getRecord(record3.id);
    expect(row3.getCellData(createdTimeField.id)).toEqual(row3.createdAt.toISOString());
    row4 = await database.getRecord(record4.id);
    expect(row4.getCellData(createdTimeField.id)).toEqual(row4.createdAt.toISOString());

    // 修改时间 -> 日期
    await modifiedTimeField.update(user, {
      name: 'modified_time to date',
      type: 'DATETIME',
      property: {
        dateFormat: 'YYYY-MM-DD',
        includeTime: true,
        timeFormat: 'HH:mm',
      },
    });
    await database.reloadFields();
    row1 = await database.getRecord(record1.id);
    // 判断为字符串即可
    expect(row1.getCellData(modifiedTimeField.id)).toBeTypeOf('string');
    row2 = await database.getRecord(record2.id);
    expect(row2.getCellData(modifiedTimeField.id)).toBeTypeOf('string');
  });
});

'use client';

import { DragDropContext, type DropResult, Droppable, type DroppableProvided } from '@hello-pangea/dnd';
import { useUpdateEffect } from 'ahooks';
import { nanoid } from 'nanoid';
import type React from 'react';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useLocale } from '@bika/contents/i18n/context';
import { utils } from '@bika/domains/shared/client';
import type { AttachmentVO } from '@bika/types/attachment/vo';
import { useGlobalContext } from '@bika/types/website/context';
import { useAttachmentUpload } from '@bika/ui/components/image-crop-upload/index';
import { triggerPreviewAttachment } from '@bika/ui/preview-attachment/preivew-attachment';
import { AttachmentItem } from './attachment-item';
import { type AttachmentVOExtends, type IAttachments, UploadStatus } from './interface';
import { UploadAttachmentItem } from './upload-attachment-item';

interface IAttachmentCoreProps {
  value?: AttachmentVOExtends[];
  onChange: (value: AttachmentVO[]) => void;
  disabled?: boolean;
  spaceId: string;
}

export const AttachmentCore: React.FC<IAttachmentCoreProps> = ({ value, onChange, disabled, spaceId }) => {
  const inputRef = useRef<HTMLInputElement | null>(null);
  const [savedAttachments, setSavedAttachments] = useState<AttachmentVOExtends[]>(value || []);
  const [uploadingAttachments, setUploadingAttachments] = useState<IAttachments>([]);
  const { upload: doUploadFile } = useAttachmentUpload();
  const [isDropOver, setIsDropOver] = useState(false);
  const { t } = useLocale();

  const pasteRef = useRef<HTMLDivElement>(null);

  const uploadItem = useCallback(
    ({ file, fileId }: { file: File; fileId: string }) => {
      doUploadFile({
        // spaceId,
        file,
        filePrefix: 'database',
        axiosConfig: {
          onUploadProgress: ({ loaded, total }) => {
            setUploadingAttachments((pre) =>
              pre.map((item) => {
                if (item.id === fileId) {
                  if (item.status === UploadStatus.FAIL) {
                    // https://github.com/vikadata/bika/issues/9114
                    // 按照这个 Issue 里记录的，有一定概率，附件在上传成功前，create 会提前触发（我暂时无法复现）
                    // 因此，另一个接口失败后，这里更新进度的方法会覆盖失败的状态，导致无法重试
                    // 因此这里检查到上传的状态已经失败后，则停止更新上传进度
                    return item;
                  }
                  return {
                    ...item,
                    status: UploadStatus.UPLOADING,
                    progress: Math.floor((loaded / (total as number)) * 100),
                  };
                }
                return item;
              }),
            );
          },
        },
      })
        .then((res) => {
          setUploadingAttachments((pre) => pre.filter((item) => item.id !== fileId));

          setSavedAttachments((pre) => [...pre, { ...res, name: file.name, originFile: file }]);
        })
        .catch((_e) => {
          setUploadingAttachments((pre) =>
            pre.map((item) => {
              if (item.id === fileId) {
                return {
                  ...item,
                  status: UploadStatus.FAIL,
                };
              }
              return item;
            }),
          );
        });
    },
    [doUploadFile, setUploadingAttachments, setSavedAttachments],
  );

  const uploadAttachment = useCallback(
    (files: FileList) => {
      const pendingFIles = Array.from(files).map((file) => ({
        file,
        id: nanoid(),
        status: UploadStatus.PENDING,
        progress: 0,
      }));

      setUploadingAttachments((pre) => [...pre, ...pendingFIles] as IAttachments);

      for (const fileObj of pendingFIles) {
        uploadItem({ file: fileObj.file, fileId: fileObj.id });
      }
    },
    [uploadItem, setUploadingAttachments],
  );

  useEffect(() => {
    const handlePaste = (e: ClipboardEvent) => {
      if (document.activeElement !== pasteRef.current) return;
      if (!e.clipboardData) return;
      uploadAttachment(e.clipboardData.files);
    };

    document.addEventListener('paste', handlePaste);

    return () => {
      document.removeEventListener('paste', handlePaste);
    };
  }, [uploadAttachment]);

  useUpdateEffect(() => {
    // console.log('savedAttachments', savedAttachments);
    // const value = savedAttachments.map((item) => omit(item, ['links', 'originFile']));
    onChange(savedAttachments);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [JSON.stringify(savedAttachments)]);

  useEffect(() => {
    // 未保存记录的修改就直接退出，清空待保存的数据队列
    setUploadingAttachments([]);
  }, [disabled]);

  const handleDrop = (e: React.DragEvent) => {
    setIsDropOver(false);
    e.preventDefault();
    uploadAttachment(e.dataTransfer.files);
  };

  const handleDragOver = (e: React.DragEvent) => {
    setIsDropOver(true);
    e.preventDefault();
  };

  const globalContext = useGlobalContext();

  const filePublicUrl = globalContext.servers.storagePublicUrl ?? '';

  const handlePreviewAttachment = (index: number) => {
    // Map savedAttachments to kkfile format
    const attachmentsForPreview = savedAttachments.map((attachment) => {
      const fileUrl = `${filePublicUrl}/${attachment.path}`;
      return {
        name: attachment.name || 'attachment',
        contentType: attachment.mimeType,
        url: attachment.links?.previewUrl || attachment.links?.downloadUrl || fileUrl,
        variant: 'kkfile' as const,
      };
    });

    triggerPreviewAttachment({ index, attachments: attachmentsForPreview, t });
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    pasteRef.current?.blur();
    const { files } = e.target;
    if (!files) return;
    uploadAttachment(files);
    e.target.value = '';
  };

  const onDragEnd = (result: DropResult) => {
    const sourceIndex = result.source.index;
    const targetIndex = result.destination?.index;

    const sourceItem = savedAttachments[sourceIndex];

    setSavedAttachments((exitFiles) => {
      const newFiles = [...exitFiles];

      if (Number.isInteger(targetIndex) && targetIndex !== undefined) {
        newFiles.splice(sourceIndex, 1);
        newFiles.splice(targetIndex, 0, sourceItem);
      }

      return newFiles;
    });
  };

  const handleDeleteItem = (id: string) => {
    setUploadingAttachments((exitFiles) => exitFiles.filter((file) => file.id !== id));
  };

  const handleRetryUpload = (fileId: string) => {
    const retryFile = uploadingAttachments.find((item) => item.id === fileId);

    if (!retryFile) return;
    setUploadingAttachments((prevFiles) =>
      prevFiles.map((file) =>
        file.id === fileId
          ? {
              ...file,
              status: UploadStatus.PENDING,
            }
          : file,
      ),
    );

    uploadItem({ file: retryFile.file, fileId: retryFile.id });
  };

  return (
    <div className={'h-full'}>
      <input defaultValue="" multiple hidden type="file" ref={inputRef} onChange={handleChange} />
      {!disabled && (
        <div
          className={utils.cn(
            'border-[1px] border-[--border-default] border-dashed w-full h-[64px] flex justify-center items-center space-x-1 rounded focus:border-[--brand] text-[--text-secondary] outline-none px-1',
            isDropOver && 'border-[--brand]',
          )}
          // onPaste={handlePaste}
          tabIndex={1}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={() => setIsDropOver(false)}
          ref={pasteRef}
        >
          <p onClick={() => inputRef.current?.click()} className={'text-[--brand] cursor-pointer'}>
            {t.record.add_local_file}
          </p>
          &nbsp;{t.auth.or}
          <p>{t.record.paste_or_drop_file_upload}</p>
        </div>
      )}

      <div className={'h-[calc(100%-112px)] overflow-y-auto mt-2 max-h-[400px]'}>
        <div className={'mb-2'}>
          <DragDropContext onDragEnd={onDragEnd}>
            <Droppable droppableId="droppable">
              {(provided: DroppableProvided) => (
                <div {...provided.droppableProps} ref={provided.innerRef} className={'space-y-2'}>
                  {savedAttachments?.map((fileItem, index) => (
                    <AttachmentItem
                      {...fileItem}
                      key={fileItem.id}
                      index={index}
                      setSaveFiles={setSavedAttachments}
                      disabled={disabled}
                      handlePreviewAttachment={handlePreviewAttachment}
                    />
                  ))}
                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          </DragDropContext>
        </div>
        <div className={'space-y-2'}>
          {uploadingAttachments?.map((fileItem) => (
            <UploadAttachmentItem
              {...fileItem}
              key={fileItem.id}
              handleDeleteItem={handleDeleteItem}
              disabled={disabled}
              handleRetryUpload={handleRetryUpload}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

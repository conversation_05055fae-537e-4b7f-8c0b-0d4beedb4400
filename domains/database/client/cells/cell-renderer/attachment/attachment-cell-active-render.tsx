'use client';

import classNames from 'classnames';
import _ from 'lodash';
// import Image from 'next/image';
import React, { useState } from 'react';
import { useLocale } from '@bika/contents/i18n';
import { useGlobalContext } from '@bika/types/website/context';
import { Popover, PopoverContent, PopoverTrigger } from '@bika/ui/components/popover/index';
import type { CustomCellRendererProps } from '@bika/ui/database/types';
import AddOutlined from '@bika/ui/icons/components/add_outlined';
import { Box } from '@bika/ui/layouts';
import type { AttachmentVOExtends } from '@bika/ui/preview-attachment/interface';
import { triggerPreviewAttachment } from '@bika/ui/preview-attachment/preivew-attachment';
import { useSpaceId } from '../../../../../space/client/context';
import { AttachmentCore } from '../../cell-editor/attachment/attachment-core';
import { getAttachmentDisplayPath } from '../../cell-editor/attachment/utils/get-attachment-display-path';

const { omit } = _;

const ATTACHMENT_MODAL_ID = 'ATTACHMENT-MODAL';

interface AttachmentCellRenderProps
  extends Pick<CustomCellRendererProps<any, AttachmentVOExtends[]>, 'value' | 'data' | 'colDef'> {
  editable?: boolean;
  className?: string;
}

// TODO 移除 coldef 的依赖, 从 value 中获取
export const AttachmentCellActiveRender: React.FC<AttachmentCellRenderProps> = ({
  // valueFormatted,
  value: _values,
  data,
  colDef,
  className,
  // editable = true,
  ...props
}) => {
  const editable = colDef?.editable;
  const spaceId = useSpaceId();
  const [isCellEditorOpen, setIsCellEditorOpen] = useState(false);
  const [savedFiles, setSaveFiles] = useState<AttachmentVOExtends[]>(_values ?? []);

  const { t } = useLocale();

  const globalContext = useGlobalContext();

  const filePublicUrl = globalContext.servers.storagePublicUrl ?? '';

  const handlePreviewAttachment = (index: number) => {
    // Map savedAttachments to kkfile format
    const attachmentsForPreview = (_values ?? []).map((attachment) => {
      const fileUrl = `${filePublicUrl}/${attachment.path}`;
      return {
        name: attachment.name || 'attachment',
        contentType: attachment.mimeType,
        url: attachment.links?.previewUrl || attachment.links?.downloadUrl || fileUrl,
        variant: 'kkfile' as const,
      };
    });

    triggerPreviewAttachment({ index, attachments: attachmentsForPreview, t });
  };

  const onChange = (values: AttachmentVOExtends[]) => {
    setSaveFiles(values);
  };

  return (
    <Box
      maxWidth={'100%'}
      display={'flex'}
      className={classNames('select-renderer', className)}
      sx={{
        paddingTop: 'var(--bika-ag-cell-padding-top)',
        height: 'fit-content',
        overflow: 'auto',
        minHeight: 'var(--bika-ag-cell-row-height-minus-padding-bottom)',
        width: '100%',
        maxWidth: '100%',
        maxHeight:
          'max(var(--bika-ag-cell-active-container-max-height), var(--bika-ag-cell-row-height-minus-padding-bottom))',
        paddingLeft: 'var(--bika-ag-cell-padding-horizontal)',
        paddingRight: 'var(--bika-ag-cell-padding-horizontal)',
        verticalAlign: 'middle',
      }}
    >
      <Popover
        placement={'bottom-start'}
        modal={true}
        zIndex={998}
        open={isCellEditorOpen}
        onOpenChange={(open) => {
          setIsCellEditorOpen(open);
          if (!open) {
            const filterValues = savedFiles.map((item) => omit(item, 'links', 'originFile'));

            if (!colDef?.field) {
              return;
            }
            // @ts-ignore
            props.node.setDataValue(colDef?.field ?? '', filterValues);
          }
        }}
      >
        <PopoverTrigger asChild>
          <Box
            className={'flex items-center flex-row gap-x-1 overflow-x-hidden flex flex-wrap gap-y-[4px]'}
            sx={{
              minHeight: 'var(--bika-ag-cell-item-height)',
              alignItems: 'center',
              // paddingTop: 'var(--bika-ag-cell-padding-top)',
              // paddingBottom: 'var(--bika-ag-cell-padding-bottom)',
            }}
          >
            {editable && (
              <AddOutlined
                className={'flex-shrink-0 mr-2 cursor-pointer'}
                size={16}
                onClick={() => setIsCellEditorOpen(!isCellEditorOpen)}
                color="var(--text-secondary)"
              />
            )}
            {savedFiles?.map((item, index) => (
              <div
                key={item.id}
                className={'relative '}
                style={{
                  minWidth: 'var(--bika-ag-cell-item-height)',
                  width: 'var(--bika-ag-cell-item-height)',
                  height: 'var(--bika-ag-cell-item-height)',
                }}
              >
                <img
                  alt=""
                  // fill={true}
                  onClick={(e) => {
                    e.stopPropagation();
                    handlePreviewAttachment(index);
                  }}
                  src={getAttachmentDisplayPath(item)}
                  // objectFit="cover"
                  className={'border-[1px] border-[--border-default]  h-[22px] w-[22px]'}
                />
              </div>
            ))}
          </Box>
        </PopoverTrigger>
        <PopoverContent className="Popover">
          <Box
            className={
              'w-[480px] bg-[var(--bg-popup)] p-4 rounded h-[400px] border border-[var(--border-default)] shadow-[0_0_10px_0_var(--border-default)]'
            }
            sx={{
              overflow: 'auto',
              position: 'relative',
            }}
          >
            <AttachmentCore value={savedFiles} onChange={onChange} spaceId={spaceId} />
          </Box>
        </PopoverContent>
      </Popover>
    </Box>
  );
};

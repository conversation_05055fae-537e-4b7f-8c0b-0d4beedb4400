import assert from 'assert';
import fs from 'fs';
import { CoreMessage, TextPart, ImagePart, FilePart } from 'ai';
import { PresetLanguageModelServerConfig } from '@bika/contents/config/server/ai/ai-model-config';
import { AISO } from '@bika/domains/ai/server/ai-so';
import { downloadUrlToTmpFile, getMimeTypeFromUrl } from '@bika/domains/shared/server/utils/files';
import type { PresetLanguageAIModelDef } from '@bika/types/ai/bo';
import { ArtifactVO, FileArtifactData } from '@bika/types/ai/vo';
import { IStreamResult } from '../../ai/server/types';
import type { UserSO } from '../../user/server/user-so';
import { type AIArtifactSO } from '../ai-artifact-so';
import { IArtifactHandler } from '../interface';

export class FileArtifactHandler implements IArtifactHandler {
  private _model: PresetLanguageAIModelDef = 'openai/gpt-4.1';

  private _artifactSO: AIArtifactSO;

  public constructor(artifactSO: AIArtifactSO) {
    this._artifactSO = artifactSO;
  }

  async streamResult(_user: UserSO): Promise<IStreamResult | undefined> {
    const data = this._artifactSO.data as FileArtifactData;
    const { filePath, fileType } = data;

    const getFileContent = async (): Promise<TextPart | ImagePart | FilePart> => {
      if (fileType === 'image') {
        return { type: 'image', image: new URL(filePath) };
      }
      const tmpFilePath = await downloadUrlToTmpFile(filePath);
      console.log('🚀 ~ tmpFilePath:', tmpFilePath);
      const fileBuffer = fs.readFileSync(tmpFilePath);

      const mimeType = await getMimeTypeFromUrl(filePath);
      return { type: 'file', data: fileBuffer, mimeType };
    };

    const { prompt, system } = this._artifactSO.prompt;
    assert(prompt, 'FileArtifactHandler:prompt is required');
    assert(system, 'FileArtifactHandler:system is required');

    const fileContent = await getFileContent();

    const messages: CoreMessage[] = [
      {
        role: 'user',
        content: [{ text: prompt!, type: 'text' }, fileContent],
      },
    ];
    const modelConfig = PresetLanguageModelServerConfig[this._model].fallbacks?.[0];
    assert(modelConfig, `FileArtifactHandler:modelConfig fallback is required, ${this._model}`);

    const result = await AISO.fileToTextStream(
      { messages, system },
      {
        model: {
          kind: 'custom',
          custom: {
            type: 'manual',
            modelId: modelConfig.modelId,
            provider: {
              type: 'OPENAI',
              apiKey: modelConfig.apiKey!,
              baseUrl: modelConfig.baseUrl!,
              organizationId: modelConfig.organizationId,
            },
          },
        },
      },
    );

    return {
      type: 'text',
      textResult: result,
    };
  }

  async getValue(_user: UserSO, streamResult: IStreamResult | undefined): Promise<ArtifactVO> {
    assert(streamResult, 'streamResult should not be undefined');
    assert(streamResult.type === 'text', 'streamResult should be text');

    // Get the filePath from initData (the original file URL)
    const data = this._artifactSO.data as FileArtifactData;
    const { filePath, fileType } = data;

    // Get the AI-generated content interpretation
    const content = await streamResult.textResult.text;

    const vo: ArtifactVO = {
      type: 'file',
      id: this._artifactSO.id,
      data: {
        filePath,
        fileType,
        content,
      },
      usage: await AISO.parseAICreditCost(this._model, await streamResult.textResult.usage),
    };

    return vo;
  }
}

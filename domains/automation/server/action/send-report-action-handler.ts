/* eslint-disable no-param-reassign */
import { isNotBlank } from 'basenext/utils/string';
import { BObuildIdConverter } from '@bika/domains/node/server/bo-builder/bo-build-id-converter';
import { ReportSO } from '@bika/domains/report/server/report-so';
import { ReportTemplateSO } from '@bika/domains/report/server/report-template-so';
import { replaceVariablesInString } from '@bika/domains/shared/server';
import { SpaceSO } from '@bika/domains/space/server';
import { RecipientParser } from '@bika/domains/system/server/recipient-parser';
import { MemberSO } from '@bika/domains/unit/server/member-so';
import { ActionOutput, Action, SendReportAction, SendReportActionSchema } from '@bika/types/automation/bo';
import { AbstractActionBuilderHandler } from './abstract-action-builder-handler';
import { IActionFillInParam, IActionRunInput, IActionRunContext } from './types';

export class SendReportActionHandler<T extends SendReportAction> extends AbstractActionBuilderHandler<T> {
  override convertInput(input: T['input'], converter: BObuildIdConverter): T['input'] {
    const relationFunc = converter.getRelationFunc();
    const to = RecipientParser.convertTo(input.to, converter);
    switch (input.type) {
      case 'MARKDOWN': {
        const { subject, markdown, html } = input;
        return {
          ...input,
          to,
          subject: replaceVariablesInString(subject, relationFunc),
          markdown: markdown ? replaceVariablesInString(markdown, relationFunc) : undefined,
          html: html ? replaceVariablesInString(html, relationFunc) : undefined,
        };
      }
      case 'AI_PROMPT': {
        const { subject, prompt } = input;
        return {
          ...input,
          to,
          subject: replaceVariablesInString(subject, relationFunc),
          prompt: replaceVariablesInString(prompt, relationFunc),
        };
      }
      default:
        break;
    }
    return { ...input, to };
  }

  override validateBO(action: Action): boolean {
    const { success } = SendReportActionSchema.safeParse(action);
    if (!success) {
      return false;
    }
    if (action.input.to.length === 0) {
      return false;
    }
    switch (action.input.type) {
      case 'MARKDOWN': {
        const { subject, markdown } = action.input;
        return isNotBlank(subject) || isNotBlank(markdown);
      }
      case 'AI_PROMPT': {
        const { subject, prompt } = action.input;
        return isNotBlank(subject) || isNotBlank(prompt);
      }
      case 'REPORT_TEMPLATE': {
        const { reportTemplateId } = action.input;
        return isNotBlank(reportTemplateId);
      }
      default:
        break;
    }
    return false;
  }

  override async fillInBO(action: SendReportAction, param: IActionFillInParam): Promise<SendReportAction> {
    const to = await RecipientParser.fillInTo(action.input.to, param.templateNodeId);
    return { ...action, input: { ...action.input, to } };
  }

  override fetchOutputMaybeFake(_input: IActionRunInput): ActionOutput {
    return {};
  }

  override doTest(): ActionOutput {
    return {};
  }

  override async fetchRunOutput(input: IActionRunInput, context: IActionRunContext): Promise<ActionOutput> {
    const { userId, spaceId, action: bo, templateNodeId } = input;
    const action = SendReportActionSchema.parse(bo);
    const recipientParser = new RecipientParser(spaceId);
    const space = await recipientParser.getSpace();
    const memberSOs = await recipientParser.parseToMembers(action.input.to, {
      userId,
      templateNodeId,
      props: { ...context },
    });
    if (memberSOs.length === 0) {
      return {};
    }
    await this.sendReports(space, memberSOs, action, context);
    return {};
  }

  /**
   * 通过Automation Action里进行报告发送
   * @param memberSOs
   * @param action
   * @param context
   * @returns
   */
  private async sendReports(
    space: SpaceSO,
    memberSOs: MemberSO[],
    action: SendReportAction,
    context: IActionRunContext,
  ) {
    const { input } = action;
    const entitlement = await space.getEntitlement();
    for (let i = 0; i < memberSOs.length; i += 1) {
      const memberSO = memberSOs[i];
      // 传入Send Report的Props
      const props = {
        ...context,
        _to: await memberSO.toVO(), // 自己
      };
      switch (input.type) {
        case 'MARKDOWN':
          {
            const { subject, markdown, html } = input;
            await entitlement.checkUsageExceed({ feature: 'REPORTS' });
            await ReportSO.sendReport(memberSO.id, subject, markdown!, { props, reportType: 'MARKDOWN', html });
          }
          break;
        case 'AI_PROMPT':
          {
            const { subject, prompt } = input;
            await entitlement.checkUsageExceed({ feature: 'REPORTS' });
            await ReportSO.sendReport(memberSO.id, subject, prompt, { props });
          }
          break;
        case 'REPORT_TEMPLATE':
          {
            const { reportTemplateId } = input;
            await entitlement.checkUsageExceed({ feature: 'REPORTS' });
            const reportTemplate = await ReportTemplateSO.init(reportTemplateId!);
            await reportTemplate.sendReport(memberSO.id, props);
          }
          break;
        default:
          throw new Error('Unknown report type');
      }
    }
  }
}

import { generateNanoID } from 'basenext/utils/nano-id';
import { errors, ServerError } from '@bika/contents/config/server/error';
import { ReminderSO } from '@bika/domains/reminder/server/reminder-so';
import { SpaceSO } from '@bika/domains/space/server';
import { RecipientParser } from '@bika/domains/system/server/recipient-parser';
import { RecipientSO } from '@bika/domains/system/server/recipients-so';
import { SchedulerSO } from '@bika/domains/system/server/scheduler-so';
import { MissionRelationType, SchedulerModel } from '@bika/domains/system/server/types';
import { MemberSO } from '@bika/domains/unit/server/member-so';
import { UnitFactory } from '@bika/domains/unit/server/unit-factory';
import { MissionModel, PrismaPromise, RecipientModel, ReminderModel, db, mongoose } from '@bika/server-orm';
import { Mission, MissionResolve, MissionState, MissionStatus, MissionType } from '@bika/types/mission/bo';
import { MissionVO, TodoVO, MissionQuerySelectorType, MissionRenderOpts } from '@bika/types/mission/vo';
import { Reminder } from '@bika/types/reminder/bo';
import { DueDateSO, iString, iStringParse, LocaleType } from '@bika/types/system';
import { To } from '@bika/types/unit/bo';
import { MissionHandlerFactory } from './handlers/mission-handler-factory';
import { IMissionCreatedOperations, IMissionCreateParam } from './types';
import { EventSO } from '../../event/server/event/event-so';
import { TalkSO } from '../../talk/server/talk-so';

/**
 * 任务
 */
export class MissionSO {
  private _model: MissionModel;

  private _recipients?: RecipientSO[];

  private constructor(model: MissionModel, recipients?: RecipientSO[]) {
    this._model = model;
    this._recipients = recipients;
  }

  async getRecipients(): Promise<RecipientSO[]> {
    if (this._recipients) {
      return this._recipients;
    }
    this._recipients = await RecipientSO.find('MISSION', this.id);
    return this._recipients;
  }

  async getInitiator() {
    if (!this.model.createdBy) {
      return undefined;
    }
    const initiator = await UnitFactory.findMember(this.model.createdBy, this.spaceId);
    return initiator ? initiator.toVO() : undefined;
  }

  get model(): MissionModel {
    return this._model;
  }

  get id(): string {
    return this.model.id;
  }

  get spaceId() {
    return this.model.spaceId;
  }

  get templateId(): string | undefined {
    return this.model.templateId ?? undefined;
  }

  get type(): MissionType {
    return this.model.type as MissionType;
  }

  get name(): iString {
    return this.model.name as iString;
  }

  getName(locale: LocaleType = 'en'): string {
    return iStringParse(this.name, locale);
  }

  get description(): iString | undefined {
    return this.model.description as iString;
  }

  getDescription(locale: LocaleType = 'en'): string {
    return this.description ? iStringParse(this.description, locale) : '';
  }

  get status(): MissionStatus {
    return this.model.status as MissionStatus;
  }

  async getSpace(): Promise<SpaceSO> {
    return SpaceSO.init(this.spaceId);
  }

  async checkBeforeCreateMissions(numOfMission: number = 1): Promise<void> {
    const space = await this.getSpace();
    const entitlement = await space.getEntitlement();
    await entitlement.checkUsageExceed({ feature: 'MISSIONS', value: numOfMission });
  }

  static async init(missionId: string): Promise<MissionSO> {
    const missionSO = await this.initMaybeNull(missionId);
    if (!missionSO) {
      throw new ServerError(errors.common.not_found);
    }
    return missionSO;
  }

  static async initMaybeNull(missionId: string): Promise<MissionSO | null> {
    const missionPO = await db.mongo.mission.findOne({
      id: missionId,
    });
    return missionPO && MissionSO.initWithModel(missionPO);
  }

  static initWithModel(po: MissionModel, recipients?: RecipientSO[]): MissionSO {
    return new MissionSO(po, recipients);
  }

  async getParentMission(): Promise<MissionSO | undefined> {
    if (!this.model.parentMissionId) {
      return undefined;
    }
    const parentMission = await MissionSO.initMaybeNull(this.model.parentMissionId);
    return parentMission || undefined;
  }

  async getSubMissions(): Promise<MissionSO[]> {
    if (this.type !== 'SEQUENCE') {
      return [];
    }
    const missions = await db.mongo.mission.find({ parentMissionId: this.id }).sort({ _id: -1 });
    return missions.map((po) => MissionSO.initWithModel(po));
  }

  async isInvalid(): Promise<boolean> {
    if (this.status === 'INVALID') {
      return true;
    }
    // 已处理（完成/取消/预期/..）的任务，无需再判断是否失效
    if (this.status !== 'PENDING') {
      return false;
    }
    // 待处理的任务，判断相当的资源（如表格/记录）是否已不存在，如果不存在，任务失效
    const missionHandler = MissionHandlerFactory.getMissionHandler(this.type);
    return missionHandler.isResourceNotExists(this.spaceId, this.model.bo);
  }

  async update(data: Partial<MissionModel>): Promise<void> {
    await db.mongo.mission.updateOne({ id: this.id }, data);
    Object.assign(this.model, data);
  }

  async delete(member: MemberSO) {
    if (this.model.status !== 'PENDING') {
      throw new ServerError(errors.mission.expect_pending);
    }
    await this.checkMemberInMission(member);

    const missionIds = [this.id];
    // 总任务删除，子任务也删除
    const subMissions = await this.getSubMissions();
    subMissions.forEach((i) => missionIds.push(i.id));
    await db.mongo.transaction(async (session) => {
      await db.mongo.mission.deleteMany({ id: { $in: missionIds } }, { session });
      await db.mongo.recipient('MISSION').deleteMany({ relationId: { $in: missionIds } }, { session });
      // 子任务被删除，总任务自动失效
      if (this.model.parentMissionId) {
        await db.mongo.mission.updateOne(
          { id: this.model.parentMissionId },
          { $set: { status: 'INVALID' } },
          { session },
        );
        await db.mongo
          .recipient('MISSION')
          .updateMany(
            { relationId: this.model.parentMissionId },
            { $set: { 'state.missionStatus': 'INVALID' } },
            { session },
          );
      }
    });
    // delete reminders
    ReminderSO.deleteByMissionId(this.model.parentMissionId ? [...missionIds, this.model.parentMissionId] : missionIds);
  }

  /**
   * 拒绝、取消这个Mission 🙅🏻‍♀️
   */
  async reject(member: MemberSO): Promise<boolean> {
    if (!this.model.bo.canReject) {
      throw new ServerError(errors.mission.expect_can_reject_is_true);
    }
    if (this.model.status !== 'PENDING') {
      throw new ServerError(errors.mission.expect_pending);
    }
    await this.checkMemberInMission(member);

    const missionIds = [this.id];
    // 子任务被拒绝/取消，总任务也被拒绝
    if (this.model.parentMissionId) {
      missionIds.push(this.model.parentMissionId);
    }
    // 总任务被拒绝，子任务当已完成处理（参考企微撤回审批逻辑）
    const subMissions = await this.getSubMissions();
    const pendingSubMissionIds = subMissions.filter((i) => i.model.status === 'PENDING').map((i) => i.id);
    const date = new Date();
    await db.mongo.transaction(async (session) => {
      // 如果是共享任务或有总任务，更新其他人的任务状态
      if (this.model.bo.assignType !== 'DEDICATED' || missionIds.length > 1) {
        await db.mongo.recipient('MISSION').updateMany(
          {
            $and: [
              { $or: [{ relationId: { $in: missionIds } }] },
              { $nor: [{ relationId: this.id, recipientId: member.id }] },
            ],
          },
          {
            $set: { updatedAt: date, 'state.missionStatus': 'REJECTED' },
          },
          { session },
        );
      }
      if (pendingSubMissionIds.length > 0) {
        await db.mongo.mission.updateMany(
          { id: { $in: pendingSubMissionIds } },
          { $set: { completedAt: date, status: 'COMPLETED' } },
          { session },
        );
        await db.mongo.recipient('MISSION').updateMany(
          { relationId: { $in: pendingSubMissionIds } },
          {
            $set: { updatedAt: date, 'state.missionStatus': 'COMPLETED' },
          },
          { session },
        );
      }
      await db.mongo.mission.updateMany(
        { id: { $in: missionIds } },
        { $set: { rejectedAt: date, status: 'REJECTED' } },
        { session },
      );
      // 更新操作者的任务状态
      await db.mongo.recipient('MISSION').updateOne(
        { relationId: this.id, recipientId: member.id },
        {
          $set: { updatedAt: date, state: { missionStatus: 'REJECTED', recipientStatus: 'REJECTED' } },
        },
        { session },
      );
    });
    this.model.rejectedAt = date;
    this.model.status = 'REJECTED';

    // delete reminders
    ReminderSO.deleteByMissionId([...missionIds, ...pendingSubMissionIds]);
    return true;
  }

  /**
   * 手动标记 彻底完成 (all complete)
   *
   * （通常是完成系统事件后完成，大部分Mission是不支持直接调completeAll的）
   *
   * @returns
   */
  async completeManually(member: MemberSO) {
    const supportManualCompleteType = ['REDIRECT_SPACE_NODE', 'REVIEW_RECORD', 'READ_TEMPLATE_README', 'READ_MARKDOWN'];
    if (!supportManualCompleteType.includes(this.model.type) && !this.model.bo.canCompleteManually) {
      throw new ServerError(errors.mission.manually_complete_not_supported);
    }
    if (this.model.status !== 'PENDING') {
      throw new ServerError(errors.mission.expect_pending);
    }
    // sequence mission 不能手动完成
    if (this.type === 'SEQUENCE') {
      throw new ServerError(errors.mission.manually_complete_not_supported_for_sequence);
    }
    await this.checkMemberInMission(member);
    await this.complete(member);
  }

  /**
   * 校验成员是否是任务的执行者
   */
  private async checkMemberInMission(member: MemberSO) {
    const recipients = await this.getRecipients();
    const exist = recipients.some((asi) => asi.existMember(member));
    if (!exist) {
      throw new ServerError(errors.mission.recipients_not_match);
    }
  }

  async complete(member?: MemberSO, state?: MissionState) {
    const date = new Date();
    const updateQuery: mongoose.UpdateQuery<MissionModel> | undefined = state && { 'bo.state': state };
    const missionUpdateQuery = { ...updateQuery, completedAt: date, status: 'COMPLETED' };
    const recipientDAO = db.mongo.recipient('MISSION');
    await recipientDAO.init();
    await db.mongo.transaction(async (session) => {
      // 如果是共享任务，更新其他人的任务状态
      if (this.model.bo.assignType !== 'DEDICATED') {
        const recipientQuery = member
          ? { relationId: this.id, recipientId: { $ne: member.id } }
          : { relationId: this.id };
        await recipientDAO.updateMany(
          recipientQuery,
          {
            $set: { updatedAt: date, 'state.missionStatus': 'COMPLETED' },
          },
          { session },
        );
      }
      await db.mongo.mission.updateOne({ id: this.id }, { $set: missionUpdateQuery }, { session });
      if (member) {
        await recipientDAO.updateOne(
          { relationId: this.id, recipientId: member.id },
          {
            $set: { updatedAt: date, state: { missionStatus: 'COMPLETED', recipientStatus: 'COMPLETED' } },
          },
          { session },
        );
      }
    });
    if (state) {
      this.model.bo = { ...this.model.bo, state };
    }
    this.model.completedAt = date;
    this.model.status = 'COMPLETED';

    EventSO.mission.onMissionCompleted(this, member);
  }

  /**
   * auto due
   */
  async runSchedulers() {
    const { id: missionId, status, dueAt } = this.model;
    const missionIds = [missionId];
    if (status === 'PENDING' && dueAt) {
      // 子任务过期，总任务也过期
      if (this.model.parentMissionId) {
        missionIds.push(this.model.parentMissionId);
      }
      // 总任务过期，子任务也过期
      const subMissions = await this.getSubMissions();
      subMissions.filter((i) => i.model.status === 'PENDING').forEach((i) => missionIds.push(i.id));
      await db.mongo.transaction(async (session) => {
        await db.mongo.mission.updateMany({ id: { $in: missionIds } }, { $set: { status: 'DUE' } }, { session });
        await db.mongo.recipient('MISSION').updateMany(
          { relationId: { $in: missionIds } },
          {
            $set: { 'state.missionStatus': 'DUE' },
          },
          { session },
        );
      });
    }
    // delete reminders
    ReminderSO.deleteByMissionId(missionIds);
  }

  async createNextSubMission(createMissionParam: IMissionCreateParam): Promise<MissionSO[]> {
    const { spaceId } = createMissionParam;
    const memberIds = (await this.getRecipients())
      .filter((rcp) => rcp.recipientType === 'MEMBER')
      .map((rcp) => rcp.recipientId);
    const members = memberIds.length > 0 ? await UnitFactory.getMembersOnSpace(spaceId, memberIds) : [];
    const operation = await MissionSO.createSubMissionOperation(members, createMissionParam, this.id);
    await this.checkBeforeCreateMissions();
    return MissionSO.doCreateMission([operation]);
  }

  /**
   * create mission
   * 根据mission模板创建时候,如果mission包含databaseTemplateId,则需要传入templateNodeId
   * @param createMissionParam spaceId: space id, templateNodeId: template node id, missionTemplate: mission template, props: template render props
   */
  static async createMission(createMissionParam: IMissionCreateParam): Promise<MissionSO[]> {
    const { user, spaceId, templateNodeId, missionTemplate, props } = createMissionParam;

    // 空间权益
    const space = await SpaceSO.init(spaceId);
    const entitlement = await space.getEntitlement();

    // 任务分配的成员
    const recipientParser = new RecipientParser(spaceId);
    const to = missionTemplate.to as To[];
    const memberSOs = await recipientParser.parseToMembers(to, {
      userId: user?.id,
      templateNodeId,
      props,
    });

    const missionAssignType = missionTemplate.assignType || 'SHARE'; // 默认是share
    if (missionAssignType === 'DEDICATED') {
      // 每人独享分别做任务
      const operations: IMissionCreatedOperations[] = [];
      for (const memberSO of memberSOs) {
        const missionProps = {
          ...props,
          _to: await memberSO.toVO(), // 接收的成员
        };
        const ops = await this.createMissionOperations([memberSO], {
          ...createMissionParam,
          props: missionProps,
        });
        operations.push(...ops);
      }
      await entitlement.checkUsageExceed({ feature: 'MISSIONS', value: operations.length });
      return this.doCreateMission(operations);
    }
    if (missionAssignType === 'SHARE') {
      // 多人共享做任务
      const members = await Promise.all(memberSOs.map((i) => i.toVO()));
      const missionProps = {
        ...props,
        _to: members.length === 1 ? members[0] : undefined,
        _tos: members,
      };
      const operations = await this.createMissionOperations(memberSOs, {
        ...createMissionParam,
        props: missionProps,
      });
      await entitlement.checkUsageExceed({ feature: 'MISSIONS', value: operations.length });
      return this.doCreateMission(operations);
    }
    throw new Error(`Unknown assign type: ${missionAssignType}`);
  }

  static async getMissionsBySpaceId(
    spaceId: string,
    options?: {
      type?: MissionType;
      status?: MissionStatus;
      templateId?: string;
    },
  ): Promise<MissionSO[]> {
    const { templateId, type } = options || {};

    // 默认加载未完成的任务
    const status = options?.status || 'PENDING';
    const query: mongoose.FilterQuery<MissionModel> = {
      spaceId,
      type: type ?? { $exists: true },
      status,
    };
    if (templateId) {
      query.templateId = templateId;
    }
    const missionPOs: MissionModel[] = await db.mongo.mission.find(query);

    const ret = [];
    for (const po of missionPOs) {
      const recipients = await RecipientSO.find('MISSION', po.id);
      const missionSO = MissionSO.initWithModel(po, recipients);
      ret.push(missionSO);
    }
    return ret;
  }

  /**
   * 获取指定Member ID的Missions
   *
   * @param memberId
   */
  static async getMemberMissions(
    member: MemberSO,
    options?: {
      type?: MissionType;
      queryFilterType?: MissionQuerySelectorType;
    },
  ): Promise<MissionSO[]> {
    const { id, userId, spaceId } = member;
    const { type, queryFilterType } = options || {};
    const { filter, sorter, skip, limit, missionStatus } = this.getQuerySelector(queryFilterType || 'RECENT', userId);
    // 我创建的任务，直接查找mission表。ps: 任务的创建者不一定是任务的执行者
    if (queryFilterType !== 'MY_CREATED') {
      const recipientQuery: mongoose.FilterQuery<MissionModel> = {
        relationType: 'MISSION',
        recipientType: 'MEMBER',
        recipientId: id,
      };
      if (missionStatus) {
        // 定义了，就要找
        recipientQuery['state.missionStatus'] = missionStatus;
      }
      const missionRecipients = await db.mongo.recipient('MISSION').find(recipientQuery);
      if (missionRecipients.length === 0) {
        return [];
      }
      const memberMissionsIds = missionRecipients.map((rcp) => rcp.relationId);
      filter.id = { $in: memberMissionsIds };
    }
    // Query missions
    const missionPOs = await db.mongo.mission
      .find({
        spaceId,
        type: type ?? { $exists: true },
        ...filter,
      })
      .sort(sorter)
      .skip(skip)
      .limit(limit);
    if (missionPOs.length === 0) {
      return [];
    }

    const missions = [];
    for (const po of missionPOs) {
      const recipients = await RecipientSO.find('MISSION', po.id);
      const missionSO = MissionSO.initWithModel(po, recipients);
      missions.push(missionSO);
    }
    return missions;
  }

  private static getQuerySelector(
    queryType: MissionQuerySelectorType,
    meUserId: string,
  ): {
    filter: { [key: string]: unknown };
    sorter: { [key: string]: mongoose.SortOrder };
    skip: number;
    limit: number;
    missionStatus?: MissionStatus;
  } {
    switch (queryType) {
      case 'PENDING':
      case 'COMPLETED':
      case 'REJECTED':
      case 'DUE':
        return {
          skip: 0,
          limit: 20,
          filter: {
            status: queryType,
          },
          // 未完成的任务，按照创建时间倒序；其他状态，按照更新时间倒序
          sorter: queryType === 'PENDING' ? { _id: -1 } : { updatedAt: -1 },
          missionStatus: queryType,
        };
      case 'ALL':
        return {
          skip: 0,
          limit: 20,
          filter: {},
          sorter: {
            completedAt: 1, // 1 for ascending, -1 for descending
            createdAt: -1, // 1 for ascending, -1 for descending
          },
        };
      case 'RECENT':
        return {
          // 排序，未完成的在前面优先
          filter: {},
          sorter: {
            completedAt: 1, // 1 for ascending, -1 for descending
            createdAt: -1, // 1 for ascending, -1 for descending
          },
          skip: 0,
          limit: 20,
        };
      case 'MY_CREATED':
        return {
          filter: {
            createdBy: meUserId,
          },
          sorter: {
            createdAt: -1, // 1 for ascending, -1 for descending
          },
          skip: 0,
          limit: 20,
        };
      default:
        throw new Error(`Unknown query type: ${queryType}`);
    }
  }

  private static async doCreateMission(operations: IMissionCreatedOperations[]): Promise<MissionSO[]> {
    if (operations.length === 0) {
      return [];
    }
    // build mission sos
    const missionSOs: MissionSO[] = operations.map(({ missionModel, missionRecipientModels }) => {
      const missionRecipients = missionRecipientModels.map((model) => new RecipientSO(model));
      return new MissionSO(missionModel, missionRecipients);
    });
    const missionModels = operations.map((operation) => operation.missionModel);
    // summary operations
    const { missionRecipientModels, reminderModels, reminderRecipientModels, schedulerOperations } = operations.reduce(
      (result, obj) => {
        Object.keys(obj).forEach((key) => {
          const field = obj[key as keyof IMissionCreatedOperations];
          if (!Array.isArray(field)) {
            return;
          }
          // eslint-disable-next-line no-param-reassign
          result[key] = (result[key] || []).concat(field);
        });
        return result;
      },
      {} as Omit<IMissionCreatedOperations, 'missionModel'> & {
        [key: string]: unknown[];
      },
    );
    // 初始化，避免自动分表机制导致冲突错误
    await db.mongo.recipient('MISSION').init();
    await db.mongo.recipient('REMINDER').init();

    await db.mongo.transaction(async (session) => {
      if (schedulerOperations.length > 0) {
        await db.prisma.$transaction(schedulerOperations);
      }
      await db.mongo.mission.insertMany(missionModels, { session });
      await db.mongo.recipient('MISSION').insertMany(missionRecipientModels, { session });
      if (reminderModels.length > 0) {
        await db.mongo.reminder.insertMany(reminderModels, { session });
        await db.mongo.recipient('REMINDER').insertMany(reminderRecipientModels, { session });
      }
    });
    missionSOs.map((missionSO) => EventSO.mission.onMissionCreate(missionSO));
    return missionSOs;
  }

  private static async createMissionOperations(
    members: MemberSO[],
    createMissionParam: IMissionCreateParam,
  ): Promise<IMissionCreatedOperations[]> {
    const { missionTemplate } = createMissionParam;
    // Sequence任务，可以没有指定执行者，但是子任务必须有
    if (members.length === 0 && missionTemplate.type !== 'SEQUENCE') {
      return [];
    }
    // 创建总任务
    const missionOperation = await this.createMissionOperation(members, createMissionParam);
    if (missionTemplate.type !== 'SEQUENCE') {
      return [missionOperation];
    }

    // Sequence任务，创建首个子任务
    // 不全部创建原因：1、后续的子任务可能依赖于第一个任务输出；2、前置子任务完成，后续子任务的相关人才能看到，全部创建需要过滤
    if (missionTemplate.missions && missionTemplate.missions.length === 0) {
      throw new Error('Sequence mission must have sub-missions');
    }
    // 子任务不能再嵌套Sequence任务
    const exist = missionTemplate.missions.some((subMission) => subMission.type === 'SEQUENCE');
    if (exist) {
      throw new Error('Sequence mission can not have sub-mission type of SEQUENCE');
    }
    // 子任务分配类型，只能共享，独享会创建多个任务，不符合预期（Sequence每个步骤应只对应一个子任务）
    const existDedicated = missionTemplate.missions.some((subMission) => subMission.assignType === 'DEDICATED');
    if (existDedicated) {
      throw new Error('Sequence mission sub-mission assign type must be SHARE');
    }

    const subOperation = await this.createSubMissionOperation(
      members,
      {
        ...createMissionParam,
        missionTemplate: missionTemplate.missions[0] as Mission,
      },
      missionOperation.missionModel.id,
    );
    return [missionOperation, subOperation];
  }

  private static async createSubMissionOperation(
    parentMissionMembers: MemberSO[],
    createMissionParam: IMissionCreateParam,
    parentMissionId: string,
  ): Promise<IMissionCreatedOperations> {
    const { user, spaceId, templateNodeId, missionTemplate, props } = createMissionParam;

    // 任务分配的成员
    const recipientParser = new RecipientParser(spaceId);
    const to = missionTemplate.to as To[];
    let subMissionMembers = await recipientParser.parseToMembers(to, {
      userId: user?.id,
      templateNodeId,
      props,
    });
    if (subMissionMembers.length === 0) {
      // 子任务没有指定人员，沿用总任务的人员
      subMissionMembers = parentMissionMembers;
    }

    const members = await Promise.all(subMissionMembers.map((i) => i.toVO()));
    const missionProps = {
      ...props,
      _to: members.length === 1 ? members[0] : undefined,
      _tos: members,
    };

    for (const member of subMissionMembers) {
      TalkSO.upsertByExpertKey('mission', member);
    }

    return this.createMissionOperation(
      subMissionMembers,
      {
        ...createMissionParam,
        props: missionProps,
      },
      parentMissionId,
    );
  }

  private static async createMissionOperation(
    members: MemberSO[],
    createParam: IMissionCreateParam,
    parentMissionId?: string,
  ): Promise<IMissionCreatedOperations> {
    const { user, timezone, missionTemplate, templateNodeId, props, spaceId } = createParam;
    const missionHandler = MissionHandlerFactory.getMissionHandler(missionTemplate.type);
    const mission = await missionHandler.fillInBO(missionTemplate, { spaceId, templateNodeId, props });

    const dueDate =
      mission.dueDate &&
      DueDateSO.parseDynamicDueDate({
        dueDate: mission.dueDate,
        userTimezone: user ? user.timeZone : timezone,
      });
    const { start, end: dueAt } = dueDate || {};

    const missionModel = this.buildModel(
      {
        creatorUserId: user?.id,
        spaceId,
        parentMissionId,
        startAt: start,
        dueAt,
      },
      mission,
    );

    const schedulerOperations: PrismaPromise<SchedulerModel>[] = [];
    // 创建DueDate Scheduler
    if (dueAt && dueAt > new Date()) {
      const schedulerOperation = SchedulerSO.createSchedulerOperation({
        userId: user?.id,
        relationType: MissionRelationType,
        relationId: missionModel.id,
        runTime: dueAt,
      });
      schedulerOperations.push(schedulerOperation);
    }

    // Mission Recipient
    const missionRecipientModels: RecipientModel[] = [];
    for (const member of members) {
      missionRecipientModels.push(
        RecipientSO.buildModel('MISSION', missionModel.id, 'MEMBER', member.id, {
          missionStatus: 'PENDING',
          recipientStatus: 'PENDING',
        }),
      );
    }

    // Reminders
    const reminderModels: ReminderModel[] = [];
    const reminderRecipientModels: RecipientModel[] = [];
    const theMission = mission as Mission;
    if (theMission.reminders && theMission.reminders.length > 0) {
      for (const reminder of theMission.reminders) {
        const theReminder = reminder as Reminder;
        const { reminderModel, recipientModels, schedulerOperation } =
          await ReminderSO.createReminderOperationWithTemplate(
            {
              user,
              timezone: user ? user.timeZone : timezone,
              property: { missionId: missionModel.id },
              props,
            },
            theReminder,
            members.map((member) => member.id),
          );
        reminderModels.push(reminderModel);
        reminderRecipientModels.push(...recipientModels);
        if (schedulerOperation) {
          schedulerOperations.push(schedulerOperation);
        }
      }
    }

    return {
      missionModel,
      missionRecipientModels,
      reminderModels,
      reminderRecipientModels,
      schedulerOperations,
    };
  }

  /**
   * 根据Mission BO对象创建模型
   * @param createParam create parameter
   * @param mission mission bo
   */
  private static buildModel(
    createParam: {
      creatorUserId?: string;
      spaceId: string;
      parentMissionId?: string;
      startAt?: Date;
      dueAt?: Date;
    },
    mission: Mission,
  ): MissionModel {
    const { creatorUserId, spaceId, parentMissionId, startAt, dueAt } = createParam;
    const missionId = generateNanoID('msn');
    // 限制包含延迟动作，因为afterActions未保存运行历史，无法在延迟结束后拿到运行上下文
    if (mission.afterActions && mission.afterActions.length > 0) {
      const exist = mission.afterActions.some((action) => action?.actionType.toString() === 'DELAY');
      if (exist) {
        throw new ServerError(errors.mission.expect_no_delay_action);
      }
    }
    return {
      id: missionId,
      templateId: mission.templateId,
      name: mission.name,
      type: mission.type,
      description: mission.description,
      bo: mission,
      spaceId,
      parentMissionId,
      startAt,
      dueAt,
      status: 'PENDING',
      createdBy: creatorUserId,
      updatedBy: creatorUserId,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  }

  /**
   * 处理(resolve) Member的空间站内所有的相同类型的missions
   *
   * 是“半完成" (step resolve)，不是全完成 (all complete)，
   * 是否彻底完成(all complete)，由mission handler决定（如：创建多条记录，完成条件是达到数量）
   *
   * 模式跟AI Intent Resolve和AI Intent Complete非常类似
   *
   * @param memberSO
   * @param resolve
   * @param filterFunc
   */
  static async resolve(memberSO: MemberSO, resolve: MissionResolve) {
    // 找到这个人的mission
    const recipients = await db.mongo.recipient('MISSION').find({
      relationType: 'MISSION',
      recipientType: 'MEMBER',
      recipientId: memberSO.id,
      'state.missionStatus': 'PENDING',
    });
    if (recipients.length === 0) {
      return;
    }
    const memberMissionsIds = recipients.map((rec) => rec.relationId);
    const missionHandler = MissionHandlerFactory.getMissionHandler(resolve.type);
    const findMissionQuery = missionHandler.findRelateMissionQuery(resolve);
    // 然后找到对应的任务
    const missions = await db.mongo.mission.find({
      id: { $in: memberMissionsIds },
      type: resolve.type,
      spaceId: memberSO.spaceId,
      ...findMissionQuery,
    });
    if (missions.length === 0) {
      return;
    }

    // resolve state
    async function toResolveState(model: MissionModel) {
      const { state, isCompleted, isSpecialUpdated } = missionHandler.resolveState(model.bo, resolve);
      if (isCompleted) {
        const missionSO = MissionSO.initWithModel(model);
        await missionSO.complete(memberSO, state);
      }
      if (isSpecialUpdated && state) {
        // Mission未完成，但需要特殊更新数据，执行Update SQL
        await db.mongo.mission.updateOne({ id: model.id }, { 'bo.state': state });
      }
    }
    // 批量处理
    await Promise.all(missions.map((mission) => toResolveState(mission)));
  }

  async toVO(opts?: MissionRenderOpts): Promise<MissionVO> {
    const { bo } = this.model;
    const missionHandler = MissionHandlerFactory.getMissionHandler(this.type);
    const [initiator, recipients, subMissions, voDescription, mission, invalid] = await Promise.all([
      this.getInitiator(),
      this.getRecipients(),
      this.getSubMissions(),
      missionHandler.getVODescription(bo),
      missionHandler.fillInFromState(bo),
      opts?.validateIfInvalid && (await this.isInvalid()),
    ]);
    // sub missions
    const missions: MissionVO[] | undefined =
      subMissions.length > 0
        ? await Promise.all(subMissions.map((i) => i.toVO({ ...opts, noLoadParent: true })))
        : undefined;
    // parent mission
    let parentMission: MissionVO | undefined;
    if (!missions && !opts?.noLoadParent) {
      const parentMissionSO = await this.getParentMission();
      parentMission = parentMissionSO && (await parentMissionSO.toVO(opts));
    }
    return {
      id: this.id,
      spaceId: this.spaceId,
      name: this.getName(opts?.locale),
      description: voDescription ? iStringParse(voDescription, opts?.locale) : '',
      templateId: this.templateId,
      assignees: await Promise.all(recipients.map((rcp) => rcp.toVO())),
      completedAt: this.model.completedAt?.toISOString(),
      rejectAt: this.model.rejectedAt?.toISOString(),
      type: this.type,
      bo: mission,
      invalid,
      initiator,
      createAt: this.model.createdAt?.toISOString(),
      updatedAt: this.model.updatedAt?.toISOString(),
      dueAt: this.model.dueAt?.toISOString(),
      status: this.status,
      missions,
      parentMission,
    };
  }

  async toTodoVO(opts?: MissionRenderOpts): Promise<TodoVO> {
    const missionVO = await this.toVO(opts);
    const todoVO: TodoVO = {
      ...missionVO,
      todoType: 'MISSION',
    };
    return todoVO;

    // const recipients = await this.getRecipients();
    // const recipientVOs = await Promise.all(recipients.map((rcp) => rcp.toVO()));
    // return {
    //   id: this.id,
    //   name: this.getName(opts?.locale),
    //   description: this.getDescription(opts?.locale),
    //   assignees: recipientVOs,
    //   createAt: this.model.createdAt?.toISOString(),
    //   dueAt: this.model.dueAt?.toISOString(),
    //   type: 'MISSION',
    //   status: this.status,
    // };
  }
}

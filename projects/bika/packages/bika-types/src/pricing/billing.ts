import { z } from 'zod';

export const ISOCurrencies = ['USD', 'CNY', 'TWD', 'JPY', 'HKD', 'MOP', 'SGD', 'CAD', 'EUR', 'AUD', 'KRW'] as const;
export const ISOCurrencySchema = z.enum(ISOCurrencies);
export type ISOCurrency = z.infer<typeof ISOCurrencySchema>;
export const USD = z.literal(ISOCurrencySchema.enum.USD).describe('USD - US Dollar 1');
export const CNY = z.literal(ISOCurrencySchema.enum.CNY).describe('CNY - Chinese Yuan, 7.24');
export const TWD = z.literal(ISOCurrencySchema.enum.TWD).describe('TWD - New Taiwan Dollar, 32.54');
export const JPY = z.literal(ISOCurrencySchema.enum.JPY).describe('JPY - Japanese Yen, 154.62');
export const HKD = z.literal(ISOCurrencySchema.enum.HKD).describe('HKD - Hong Kong Dollar, 7.83');
export const MOP = z.literal(ISOCurrencySchema.enum.MOP).describe('MOP - Macau Pataca, 8.05');
export const SGD = z.literal(ISOCurrencySchema.enum.SGD).describe('SGD - Singapore Dollar, 1.36');
export const CAD = z.literal(ISOCurrencySchema.enum.CAD).describe('CAD - Canadian Dollar, 1.38');
export const EUR = z.literal(ISOCurrencySchema.enum.EUR).describe('EUR - Euro, 0.94');
export const AUD = z.literal(ISOCurrencySchema.enum.AUD).describe('AUD - Australian Dollar, 1.56');
export const KRW = z.literal(ISOCurrencySchema.enum.KRW).describe('KRW - South Korean Won, 1374.50');

// Billing plan
export const SpacePlanTypes = [
  'FREE',
  'STARTER',
  'PLUS',
  'PRO',
  'TEAM',
  'BUSINESS',
  'ENTERPRISE',
  'ENTERPRISE_PRIVATE_CLOUD',
  'ENTERPRISE_SELF_HOSTED',
  'COMMUNITY',
] as const;
export const SpacePlanTypeSchema = z.enum(SpacePlanTypes);
export type SpacePlanType = z.infer<typeof SpacePlanTypeSchema>;

/**
 * Specification, used to define the functional limitations of the spaces
 */
export const BillingSpecSchema = z.enum([
  'FREE',
  'PLUS',
  'PRO',
  'TEAM',
  'BUSINESS',
  'ENTERPRISE',

  // Oncely
  'PLUS_5_SEAT',
  'PRO_15_SEAT',
  'TEAM_100_SEAT',

  'PLUS_PACK',
  'PRO_PACK',
  'TEAM_PACK',
  'BUSINESS_PACK',
  'ENTERPRISE_PACK',
  'ENTERPRISE_SELF_HOSTED',
  'COIN',

  // Appsumo
  'PLUS_TIER1_APPSUMO',
  'PRO_TIER2_APPSUMO',
  'TEAM_TIER3_APPSUMO',
  'BUSINESS_TIER4_APPSUMO',

  // Bika Official Lifetime Plans, used for transferring AItable users with appsumo tier to Bika
  'PLUS_AITABLE_APPSUMO_TIER1',
  'PRO_AITABLE_APPSUMO_TIER2',
  'TEAM_AITABLE_APPSUMO_TIER3',
  'BUSINESS_AITABLE_APPSUMO_TIER4',
]);
export type BillingSpec = z.infer<typeof BillingSpecSchema>;

// usage metrics
export const StripeUsageTypeSchema = z.union([
  z.literal('seat'),
  z.literal('guest'),
  z.literal('record'),
  z.literal('api-request'),
  z.literal('ai-request'),
  z.literal('ai-request-advanced'),
  z.literal('mission'),
  z.literal('automation'),
  z.literal('sms'),
  z.literal('email'),
  z.literal('report'),
  z.literal('attachment-size'),
  z.literal('coin'),
]);
export type StripeUsageType = z.infer<typeof StripeUsageTypeSchema>;

export const BillingPriceSchema = z.record(ISOCurrencySchema, z.number()); // divided by 100
export type BillingPrice = z.infer<typeof BillingPriceSchema>;

// Billing cycle frequency
export const BillingRecurringIntervalSchema = z.union([z.literal('month'), z.literal('year'), z.literal('once')]);
export type BillingRecurringInterval = z.infer<typeof BillingRecurringIntervalSchema>;

export const StripeSubscriptionPriceSchema = z.record(
  BillingRecurringIntervalSchema,
  BillingPriceSchema, // divided by 100
);
export type StripeSubscriptionPrice = z.infer<typeof StripeSubscriptionPriceSchema>;

export const AIModelPriceSchema = z.object({
  inputCredit: z.number(), // input credit per 1000 tokens
  outputCredit: z.number(), // output credit per 1000 tokens
});

export type AIModelPrice = z.infer<typeof AIModelPriceSchema>;

// Subscription platform
export const BillingPlatforms = ['BIKA', 'STRIPE', 'APPSUMO', 'ONCELY', 'IOS', 'DING', 'WECOM', 'LARK'] as const;
export const BillingPlatformSchema = z.enum(BillingPlatforms);
export type BillingPlatform = z.infer<typeof BillingPlatformSchema>;

const BillingSkuBaseSchema = z.object({
  id: z.string(),
  version: z.number(),
  name: z.string(),
  description: z.string(),
  platform: BillingPlatformSchema,
  prices: BillingPriceSchema,
  meter: StripeUsageTypeSchema,
  included: z.number(),
  billingUnit: z.number(),
  interval: BillingRecurringIntervalSchema,
});

export const BillingSpacePlanSkuSchema = BillingSkuBaseSchema.extend({
  skuType: z.literal('SPACE_PLAN'),
  // plan, used to display which space station level
  plan: SpacePlanTypeSchema,
  // spec, used to actually limit the usage of the space station
  spec: BillingSpecSchema,
  // interval, how often to auto-renew (only stripe or ios subscriptions are auto-renewed, others are once, just set the expiration date)
});

export type BillingSpacePlanSku = z.infer<typeof BillingSpacePlanSkuSchema>;

const BillingPayAsYouGoSkuSchema = BillingSkuBaseSchema.extend({
  skuType: z.literal('PAY_AS_YOU_GO'),
  plan: SpacePlanTypeSchema, // Different plans, different pay-as-you-go prices
  interval: z.literal('month'),
});

export type BillingPayAsYouGoSku = z.infer<typeof BillingPayAsYouGoSkuSchema>;

const BillingTopUpCoinsSkuSchema = BillingSkuBaseSchema.extend({
  skuType: z.literal('COIN_CURRENCY'),
  amount: z.number(),
  interval: z.literal('once'),
});

const BillingTemplateSkuSchema = BillingSkuBaseSchema.extend({
  skuType: z.literal('TEMPLATE'),
  templateId: z.string(),
  version: z.string(),
});

export const BillingSKUConfigSchema = z.discriminatedUnion('skuType', [
  BillingSpacePlanSkuSchema,
  BillingPayAsYouGoSkuSchema,
  BillingTopUpCoinsSkuSchema,
  BillingTemplateSkuSchema,
]);
export type BillingSKUConfig = z.infer<typeof BillingSKUConfigSchema>;
export const BillingSKUConfigMapSchema = z.record(BillingSKUConfigSchema);
export type BillingSKUConfigMap = z.infer<typeof BillingSKUConfigMapSchema>;
export type BillingSKUType = BillingSKUConfig['skuType'];

// Subscription type SKU
export const SubscriptionSkuSchema = z.union([BillingSpacePlanSkuSchema, BillingPayAsYouGoSkuSchema]);
export type SubscriptionSkuConfig = z.infer<typeof SubscriptionSkuSchema>;

/**
 * Convert SKU to corresponding stripe product id
 * @param sku sku config
 * @returns stripe product id
 */
export function toStripeProductId(sku: BillingSKUConfig): string {
  const skuType = sku.skuType;
  if (skuType === 'PAY_AS_YOU_GO' || skuType === 'SPACE_PLAN') {
    return `${sku.version}-${skuType}-${sku.plan}`.toLowerCase().replace(/_/g, '-');
  }
  if (skuType === 'COIN_CURRENCY') {
    return `${sku.version}-${skuType}-${sku.amount}`.toLowerCase().replace(/_/g, '-');
  }
  throw new Error(`Unknown skuType: ${skuType}`);
}

/**
 * Add-on features
 */
export const AddonSchema = z.object({
  templateId: z.string(),
});

const BillingPlanFeatureBooleanValueSchema = z.enum(['YES', 'NO', 'COMING_SOON']);
export type BillingPlanFeatureBooleanValue = z.infer<typeof BillingPlanFeatureBooleanValueSchema>;

/**
 * feature.xlsx table
 */
export const BillingPlanFeatureConfigSchema = z.object({
  spec: z.string(),
  seats: z.number(),
  guests: z.number(),
  recordsPerSpace: z.number(),
  recordsPerDatabase: z.number(),
  storage: z.number().describe('GB'),
  resources: z.number().describe('How many node resources?'),
  browserNotifications: BillingPlanFeatureBooleanValueSchema,
  mobileNotifications: BillingPlanFeatureBooleanValueSchema,
  missions: z.number(),
  reports: z.number(),
  sms: z.number(),
  managedEmails: z.number(),
  smtpEmails: z.number(),
  automationRuns: z.number(),
  automationRunsHistory: z.number().describe('How many days to keep history?'),

  automationIntegrations: BillingPlanFeatureBooleanValueSchema,
  advancedAutomationIntegrations: BillingPlanFeatureBooleanValueSchema,

  creditsPerSeat: z.number(),
  // advancedAI: z.number(),
  bringYourOwnAIKey: BillingPlanFeatureBooleanValueSchema,
  resourcesPermission: z.number(),
  publishShare: BillingPlanFeatureBooleanValueSchema,
  viewResource: BillingPlanFeatureBooleanValueSchema,

  removeLogos: BillingPlanFeatureBooleanValueSchema,

  spaceIntegrations: z.number(),
  apiRequest: z.number().describe('How many API requests per month?'),
  syncer: BillingPlanFeatureBooleanValueSchema,

  openapiRate: z.number().describe('How many API requests run in per second?'),

  // Security
  subAdmins: BillingPlanFeatureBooleanValueSchema,
  subDomain: BillingPlanFeatureBooleanValueSchema,
  customDomain: BillingPlanFeatureBooleanValueSchema,

  restrictDomains: BillingPlanFeatureBooleanValueSchema,
  userSessionsLogs: BillingPlanFeatureBooleanValueSchema,
  spaceSessionsLogs: z.number().describe('How many days to keep logs?'),
  spaceAuditLogs: z.number().describe('How many days to keep logs?'),

  exportExcel: BillingPlanFeatureBooleanValueSchema,
  exportBika: BillingPlanFeatureBooleanValueSchema,

  // Template
  publishTemplate: BillingPlanFeatureBooleanValueSchema,
  sellTemplate: BillingPlanFeatureBooleanValueSchema,
  privateTemplate: BillingPlanFeatureBooleanValueSchema,

  // Support
  community: BillingPlanFeatureBooleanValueSchema,
  helpCenter: BillingPlanFeatureBooleanValueSchema,
  webinar: BillingPlanFeatureBooleanValueSchema,
  emailSupport: BillingPlanFeatureBooleanValueSchema,
  imSupport: BillingPlanFeatureBooleanValueSchema,
  professionalServices: BillingPlanFeatureBooleanValueSchema,
  selfHosted: BillingPlanFeatureBooleanValueSchema,
});

export type BillingPlanFeatureConfig = z.infer<typeof BillingPlanFeatureConfigSchema>;
export type BillingPlanFeatureKey = keyof Omit<BillingPlanFeatureConfig, 'spec'>;

export const BillingPlanFeatureConfigMapSchema = z.record(BillingPlanFeatureConfigSchema);
export type BillingPlanFeatureConfigMap = z.infer<typeof BillingPlanFeatureConfigMapSchema>;

// Usage calculation type specifications
export const BillingUsagePlanFeatures = [
  // Numeric types
  'SEATS',
  'GUESTS',
  'STORAGES',
  'RECORDS_PER_SPACE',
  'RECORDS_PER_DATABASE',
  'RESOURCES',
  'MISSIONS',
  'REPORTS',
  'MANAGED_EMAILS',
  'AUTOMATION_RUNS',

  'CREDITS_PER_SEAT',
  'SPACE_INTEGRATIONS',
  'API_REQUEST',

  'SMTP_EMAILS', // deprecated
  'TOTAL_SMS_NOTIFICATIONS', // deprecated
  'RESOURCE_PERMISSION',
] as const;
export const BillingUsagePlanFeatureSchema = z.enum(BillingUsagePlanFeatures);
export type BillingUsagePlanFeature = z.infer<typeof BillingUsagePlanFeatureSchema>;
export const BillingUsagePlanFeatureMapSchema = z.record(BillingUsagePlanFeatureSchema, z.number());
export type BillingUsagePlanFeatureMap = z.infer<typeof BillingUsagePlanFeatureMapSchema>;
// Features with date range type (unit: days)
export const BillingDatePlanFeatures = ['AUTOMATION_RUN_HISTORY', 'SPACE_SESSIONS_LOGS', 'SPACE_AUDIT_LOGS'] as const;
export const BillingDatePlanFeatureSchema = z.enum(BillingDatePlanFeatures);
export type BillingDatePlanFeature = z.infer<typeof BillingDatePlanFeatureSchema>;
export const BillingDatePlanFeatureMapSchema = z.record(BillingDatePlanFeatureSchema, z.number());
export type BillingDatePlanFeatureMap = z.infer<typeof BillingDatePlanFeatureMapSchema>;
// Features with fix number type (unit: depend feature)
export const BillingFixNumberPlanFeatures = [
  'OPENAPI_RATE', // openapi request frequency unit: times/1 second
] as const;
export const BillingFixNumberPlanFeatureSchema = z.enum(BillingFixNumberPlanFeatures);
export type BillingFixNumberPlanFeature = z.infer<typeof BillingFixNumberPlanFeatureSchema>;
export const BillingFixNumberPlanFeatureMapSchema = z.record(BillingFixNumberPlanFeatureSchema, z.number());
export type BillingFixNumberPlanFeatureMap = z.infer<typeof BillingFixNumberPlanFeatureMapSchema>;

// Features with support type, boolean type
export const BillingBooleanPlanFeatures = [
  'VIEW_RESOURCE',
  'SUB_ADMINS',
  'USER_SESSIONS_LOGS',
  'EMAIL_SUPPORT',
  'IM_SUPPORT',
  'PROFESSIONAL_SERVICES',

  'AUTOMATION_INTEGRATIONS',
  'ADVANCED_AUTOMATION_INTEGRATIONS',
  'BRING_YOUR_OWN_AI_KEY',
  'REMOVE_LOGOS',
  'DATA_SYNCER',
  'SUB_DOMAIN',
  'CUSTOM_DOMAIN',
  'EXPORT_EXCEL_CSV',
  'EXPORT_BIKA',
  'SELL_TEMPLATE',
  'PRIVATE_TEMPLATE',
  'SELF_HOSTED',

  'BROWSER_NOTIFICATIONS',
  'MOBILE_NOTIFICATIONS',
  'RESTRICT_DOMAINS',
  'PUBLIC_SHARE',
  'PUBLISH_TEMPLATE',
  'COMMUNITY',
  'HELP_CENTER',
  'WEBINAR',
] as const;
export const BillingBooleanPlanFeatureSchema = z.enum(BillingBooleanPlanFeatures);
export type BillingBooleanPlanFeature = z.infer<typeof BillingBooleanPlanFeatureSchema>;
export const BillingBooleanPlanFeatureMapSchema = z.record(BillingBooleanPlanFeatureSchema, z.boolean());
export type BillingBooleanPlanFeatureMap = z.infer<typeof BillingBooleanPlanFeatureMapSchema>;

// All specifications merged together
export const BillingPlanFeatures = [
  ...BillingUsagePlanFeatures,
  ...BillingDatePlanFeatures,
  ...BillingBooleanPlanFeatures,
  ...BillingFixNumberPlanFeatures,
] as const;
export const BillingPlanFeatureSchema = z.enum(BillingPlanFeatures);
export type BillingPlanFeature = z.infer<typeof BillingPlanFeatureSchema>;

// Combine the two types of specifications into key-value pairs
export const BillingPlanFeatureMapSchema = BillingUsagePlanFeatureMapSchema.and(BillingBooleanPlanFeatureMapSchema)
  .and(BillingDatePlanFeatureMapSchema)
  .and(BillingFixNumberPlanFeatureMapSchema);
export type BillingPlanFeatureMap = z.infer<typeof BillingPlanFeatureMapSchema>;

// Payment status
export const PaymentStatuses = ['PENDING', 'SUCCESS', 'EXPIRED', 'FAILED', 'CANCEL'] as const;
export const PaymentStatusSchema = z.enum(PaymentStatuses);
export type PaymentStatus = z.infer<typeof PaymentStatusSchema>;
export const BillingPlanFeatureMapper: Record<BillingPlanFeatureKey, BillingPlanFeature> = {
  seats: 'SEATS',
  guests: 'GUESTS',
  recordsPerSpace: 'RECORDS_PER_SPACE',
  recordsPerDatabase: 'RECORDS_PER_DATABASE',
  storage: 'STORAGES',
  resources: 'RESOURCES',
  browserNotifications: 'BROWSER_NOTIFICATIONS',
  mobileNotifications: 'MOBILE_NOTIFICATIONS',
  missions: 'MISSIONS',
  reports: 'REPORTS',
  sms: 'TOTAL_SMS_NOTIFICATIONS',
  managedEmails: 'MANAGED_EMAILS',
  smtpEmails: 'SMTP_EMAILS',
  automationRuns: 'AUTOMATION_RUNS',
  automationRunsHistory: 'AUTOMATION_RUN_HISTORY',

  automationIntegrations: 'AUTOMATION_INTEGRATIONS',
  advancedAutomationIntegrations: 'ADVANCED_AUTOMATION_INTEGRATIONS',

  creditsPerSeat: 'CREDITS_PER_SEAT',
  bringYourOwnAIKey: 'BRING_YOUR_OWN_AI_KEY',
  resourcesPermission: 'RESOURCE_PERMISSION',
  publishShare: 'PUBLIC_SHARE',
  viewResource: 'VIEW_RESOURCE',

  removeLogos: 'REMOVE_LOGOS',
  spaceIntegrations: 'SPACE_INTEGRATIONS',
  apiRequest: 'API_REQUEST',
  syncer: 'DATA_SYNCER',
  openapiRate: 'OPENAPI_RATE',

  // Security
  subAdmins: 'SUB_ADMINS',
  subDomain: 'SUB_DOMAIN',
  customDomain: 'CUSTOM_DOMAIN',

  restrictDomains: 'RESTRICT_DOMAINS',
  userSessionsLogs: 'USER_SESSIONS_LOGS',
  spaceSessionsLogs: 'SPACE_SESSIONS_LOGS',
  spaceAuditLogs: 'SPACE_AUDIT_LOGS',
  exportExcel: 'EXPORT_EXCEL_CSV',
  exportBika: 'EXPORT_BIKA',

  // Template
  publishTemplate: 'PUBLISH_TEMPLATE',
  sellTemplate: 'SELL_TEMPLATE',
  privateTemplate: 'PRIVATE_TEMPLATE',

  // Support
  community: 'COMMUNITY',
  helpCenter: 'HELP_CENTER',
  webinar: 'WEBINAR',
  emailSupport: 'EMAIL_SUPPORT',
  imSupport: 'IM_SUPPORT',
  professionalServices: 'PROFESSIONAL_SERVICES',
  selfHosted: 'SELF_HOSTED',
};

// Mapping between BillingPlanFeature and BillingPlanFeatureKey
export const BillingPlanFeatureKeyMapper: Record<BillingPlanFeature, BillingPlanFeatureKey> = {
  SEATS: 'seats',
  GUESTS: 'guests',
  RECORDS_PER_SPACE: 'recordsPerSpace',
  RECORDS_PER_DATABASE: 'recordsPerDatabase',
  STORAGES: 'storage',
  RESOURCES: 'resources',
  BROWSER_NOTIFICATIONS: 'browserNotifications',
  MOBILE_NOTIFICATIONS: 'mobileNotifications',
  MISSIONS: 'missions',
  REPORTS: 'reports',
  TOTAL_SMS_NOTIFICATIONS: 'sms',
  MANAGED_EMAILS: 'managedEmails',
  SMTP_EMAILS: 'smtpEmails',
  AUTOMATION_RUNS: 'automationRuns',
  AUTOMATION_RUN_HISTORY: 'automationRunsHistory',

  AUTOMATION_INTEGRATIONS: 'automationIntegrations',
  ADVANCED_AUTOMATION_INTEGRATIONS: 'advancedAutomationIntegrations',

  CREDITS_PER_SEAT: 'creditsPerSeat',
  // ADVANCED_AI: 'advancedAI',
  BRING_YOUR_OWN_AI_KEY: 'bringYourOwnAIKey',
  RESOURCE_PERMISSION: 'resourcesPermission',
  PUBLIC_SHARE: 'publishShare',
  VIEW_RESOURCE: 'viewResource',

  REMOVE_LOGOS: 'removeLogos',
  SPACE_INTEGRATIONS: 'spaceIntegrations',
  API_REQUEST: 'apiRequest',
  DATA_SYNCER: 'syncer',
  OPENAPI_RATE: 'openapiRate',

  // Security
  SUB_ADMINS: 'subAdmins',
  SUB_DOMAIN: 'subDomain',
  CUSTOM_DOMAIN: 'customDomain',

  RESTRICT_DOMAINS: 'restrictDomains',
  USER_SESSIONS_LOGS: 'userSessionsLogs',
  SPACE_SESSIONS_LOGS: 'spaceSessionsLogs',
  SPACE_AUDIT_LOGS: 'spaceAuditLogs',
  EXPORT_EXCEL_CSV: 'exportExcel',
  EXPORT_BIKA: 'exportBika',

  // Template
  PUBLISH_TEMPLATE: 'publishTemplate',
  SELL_TEMPLATE: 'sellTemplate',
  PRIVATE_TEMPLATE: 'privateTemplate',

  // Support
  COMMUNITY: 'community',
  HELP_CENTER: 'helpCenter',
  WEBINAR: 'webinar',
  EMAIL_SUPPORT: 'emailSupport',
  IM_SUPPORT: 'imSupport',
  PROFESSIONAL_SERVICES: 'professionalServices',
  SELF_HOSTED: 'selfHosted',
};

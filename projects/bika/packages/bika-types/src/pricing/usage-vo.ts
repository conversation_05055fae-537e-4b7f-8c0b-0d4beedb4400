import { z } from 'zod';
import { BillingPlanFeatureSchema, BillingUsagePlanFeatureSchema, SpacePlanTypeSchema } from './billing';
import { UsageTypeSchema } from './usage';

/**
 * Billing Usage VO
 */
export const UsageVOSchema = z.object({
  type: UsageTypeSchema,
  current: z.number(),
  max: z.number(),
});

export type UsageVO = z.infer<typeof UsageVOSchema>;

export const FeatureLockedErrorDataSchema = z.object({
  plan: SpacePlanTypeSchema,
  feature: BillingPlanFeatureSchema,
});
export type FeatureLockedErrorData = z.infer<typeof FeatureLockedErrorDataSchema>;

export const UsageExceedLimitErrorDataSchema = z.object({
  plan: SpacePlanTypeSchema,
  feature: BillingUsagePlanFeatureSchema,
  max: z.number(),
  current: z.number(),
});

export type UsageExceedLimitErrorData = z.infer<typeof UsageExceedLimitErrorDataSchema>;

import { z } from 'zod';
import { BillingUsagePlanFeatureSchema } from './billing';

const UsageTypeEnum = BillingUsagePlanFeatureSchema.enum;

// Usage log types (only record metered types), displayed in order
export const UsageTypes = [
  // If defined, it must be tracked; do not define if not developed
  UsageTypeEnum.SEATS,
  UsageTypeEnum.GUESTS,
  UsageTypeEnum.STORAGES,
  UsageTypeEnum.RESOURCES,
  UsageTypeEnum.RECORDS_PER_SPACE,
  UsageTypeEnum.MISSIONS,
  UsageTypeEnum.REPORTS,
  UsageTypeEnum.MANAGED_EMAILS,
  UsageTypeEnum.AUTOMATION_RUNS,
  UsageTypeEnum.SPACE_INTEGRATIONS,
  UsageTypeEnum.API_REQUEST,
  UsageTypeEnum.RESOURCE_PERMISSION,
] as const;
export const UsageTypeSchema = z.enum(UsageTypes);
export type UsageType = z.infer<typeof UsageTypeSchema>;

export const BillingUsageLogSchema = z.object({
  type: UsageTypeSchema,
  value: z.number(),
  databaseId: z.string().optional(),
  attachmentId: z.string().optional(),
});
export type BillingUsageLog = z.infer<typeof BillingUsageLogSchema>;

export const UsageStatPeriods = ['DAY', 'MONTH', 'YEAR'] as const;
export const UsageStatPeriodSchema = z.enum(UsageStatPeriods);
export type UsageStatPeriod = z.infer<typeof UsageStatPeriodSchema>;

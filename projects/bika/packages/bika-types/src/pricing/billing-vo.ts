import { z } from 'zod';
import {
  SpacePlanTypeSchema,
  BillingRecurringIntervalSchema,
  BillingPlanFeatureMapSchema,
  BillingPlatformSchema,
  BillingUsagePlanFeatureSchema,
  BillingDatePlanFeatureSchema,
  BillingBooleanPlanFeatureSchema,
  PaymentStatusSchema,
  BillingFixNumberPlanFeatureSchema,
} from './billing';

export const SubscriptionVOSchema = z.object({
  id: z.string().optional(),
  skuId: z.string().optional(),
  platform: BillingPlatformSchema,
  plan: SpacePlanTypeSchema,
  planName: z.string(),
  interval: BillingRecurringIntervalSchema.optional(),
  expireAt: z.string().optional(), // undefined for infinite
  cancelAtPeriodEnd: z.boolean().optional(),
  features: BillingPlanFeatureMapSchema,
});

export type SubscriptionVO = z.infer<typeof SubscriptionVOSchema>;
// View object for creating a payment session
export const CheckoutVOSchema = z.object({
  // System payment ID
  paymentId: z.string(),
  // Initial payment session URL, empty when updating subscription
  checkoutSessionUrl: z.string().optional(),
});
export type CheckoutVO = z.infer<typeof CheckoutVOSchema>;

export const PaymentVOSchema = z.object({
  paymentId: z.string(),
  subscriptionId: z.string().nullable(),
  status: PaymentStatusSchema,
});
export type PaymentVO = z.infer<typeof PaymentVOSchema>;

export const UsageFeatureVOSchema = z.object({
  feature: BillingUsagePlanFeatureSchema,
  current: z.number(),
  max: z.number(),
});
export type UsageFeatureVO = z.infer<typeof UsageFeatureVOSchema>;

export const SupportedFeatureVOSchema = z.object({
  feature: BillingBooleanPlanFeatureSchema,
  active: z.boolean(),
});
export type SupportedFeatureVO = z.infer<typeof SupportedFeatureVOSchema>;

export const DateFeatureVOSchema = z.object({
  feature: BillingDatePlanFeatureSchema,
  value: z.number(),
});
export type DateFeatureVO = z.infer<typeof DateFeatureVOSchema>;

export const FixedNumberFeatureVOSchema = z.object({
  feature: BillingFixNumberPlanFeatureSchema,
  value: z.number(),
});
export type FixedNumberFeatureVO = z.infer<typeof FixedNumberFeatureVOSchema>;

// Entitlement features
export const EntitlementFeatureVOSchema = z.discriminatedUnion('feature', [
  UsageFeatureVOSchema,
  SupportedFeatureVOSchema,
  DateFeatureVOSchema,
  FixedNumberFeatureVOSchema,
]);
export type EntitlementFeatureVO = z.infer<typeof EntitlementFeatureVOSchema>;

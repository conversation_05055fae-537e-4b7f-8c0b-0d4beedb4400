import {
  FeatureLockedErrorData,
  FeatureLockedErrorDataSchema,
  UsageExceedLimitErrorData,
  UsageExceedLimitErrorDataSchema,
} from '../pricing/usage-vo';

/**
 * Validate and transform data to usage exceed limit data structure
 * @param errorData unknown
 * @returns UsageExceedLimitErrorData | null
 */
export const transformUsageLimitErrorData = (errorData: unknown): UsageExceedLimitErrorData | null => {
  const errorDataParse = UsageExceedLimitErrorDataSchema.safeParse(errorData);
  return errorDataParse.success ? errorDataParse.data : null;
};

export const transformFeatureLockedErrorData = (errorData: unknown): FeatureLockedErrorData | null => {
  const errorDataParse = FeatureLockedErrorDataSchema.safeParse(errorData);
  return errorDataParse.success ? errorDataParse.data : null;
};

const dict = {
  about: {
    about: 'About',
    address: '488 The Bridle Walk, Toronto, ON L6C 2Y4 Canada',
    cn_license: '备案号：粤ICP备********号-4 公安备案号：**************',
    copyright: 'Copyright © 2025 Bika.ai',
    license: 'License',
    permission: 'Application permission description',
    privacy_policy: 'Privacy policy',
    rate_us: 'Rate us',
    release_notes: 'Release notes',
    safety: 'Safety white pape',
  },
  account: {
    account_binding: 'Account binding',
    account_binding_and_security: 'Account Binding & Security',
    account_binding_description:
      'In order to better receive alerts and handle tasks, please choose one of the following methods to log in to your Bika account.',
    account_binding_error: 'Account binding error',
    account_binding_error_description: 'Account binding failed. Please try again later.',
    account_binding_error_type: 'The current {type} has been bound to another account',
    account_binding_success: 'Account binding success',
    account_info: 'Account info',
    advanced_features: 'Advanced features',
    bind_now: 'Bind now',
    bound: 'Bound',
    delete_account: 'Delete account',
    delete_account_description: 'Are you sure you want to delete your account? This action cannot be undone.',
    destroy_account: 'Destroy account',
    social_account_binding: 'Social account binding',
    social_account_binding_description: 'Bind social account to unlock more features',
    subscribe_now: 'Subscribe now',
    use_advanced_features: 'Use advanced features',
    use_advanced_features_description: 'Open advanced features to improve team efficiency',
  },
  action: {
    accept: 'Accept',
    accepted: 'Accepted',
    add: 'Add',
    added: 'Added',
    adding: 'Adding...',
    again: 'Again',
    apply: 'Apply',
    auth: 'Auth',
    back: 'Back',
    cancel: 'Cancel',
    choose: 'Choose',
    click_here: 'Click here',
    close: 'Close',
    coming_soon: 'Coming Soon',
    coming_soon_description: 'This feature is coming soon.',
    comment: 'Comment',
    commented: 'Commented',
    complete: 'Complete',
    completed: 'Completed',
    confirm: 'Confirm',
    connect: 'Connect',
    connected: 'Connected',
    contact_community: 'Contact community',
    create: 'Create',
    create_data: 'New Data',
    create_folder: 'New folder',
    create_resource: 'New resource',
    created: 'Created',
    current: 'Current',
    decline: 'Decline',
    declined: 'Declined',
    delete: 'Delete',
    delete_resource: 'Delete resource',
    deleted: 'Deleted',
    detail: 'Detail',
    due: 'Due',
    duplicate: 'Duplicate',
    edit: 'Edit',
    edit_resource: 'Edit resource',
    edited: 'Edited',
    email_sent: 'Email Sent',
    failed: 'Failed',
    get: 'Get',
    import: 'Import',
    install: 'Install',
    installed: 'Installed',
    loading: 'Loading...',
    manual_complete: 'Manual Complete',
    more: 'More',
    move: 'Move',
    next: 'Next',
    no: 'No',
    not_supported: 'Not supported',
    not_supported_description: 'This feature is not supported yet.',
    ok: 'OK',
    only_on_web: 'Web only feature',
    only_on_web_description: 'Please use the web version to access this feature.',
    only_on_web_editor: 'Editor',
    only_on_web_editor_description: 'Please use the web editor to access this feature.',
    overdue: 'Overdue',
    process: 'Process',
    processing: 'Processing',
    reauthorize_integration: 'Reauthorize',
    redirect: 'Redirect',
    remove: 'Remove',
    removed: 'Removed',
    rename: 'Rename',
    save: 'Save',
    saved: 'Saved',
    search: 'Search',
    search_placeholder: 'Search...',
    search_result: 'Search Result',
    search_result_description: 'Search result for "{keyword}"',
    search_result_not_found: 'Search result not found',
    search_result_not_found_description: 'No result found for "{keyword}"',
    searching: 'Searching...',
    select: 'Select',
    select_all: 'Select all',
    selected: 'Selected',
    send: 'Send',
    submit: 'Submit',
    submitted: 'Submitted',
    submitter: 'Submitter',
    transfer: 'Transfer',
    transferred: 'Transferred',
    uninstall: 'Uninstall',
    uninstalled: 'Uninstalled',
    unselect_all: 'Unselect All',
    view: 'View',
    viewed: 'Viewed',
    yes: 'Yes',
  },
  ag_grid: {
    AreaColumnCombo: 'Area & Column',
    addCurrentSelectionToFilter: 'Add current selection to filter',
    addToLabels: 'Add ${variable} to labels',
    addToValues: 'Add ${variable} to values',
    advancedFilterAnd: 'AND',
    advancedFilterApply: 'Apply',
    advancedFilterBlank: 'is blank',
    advancedFilterBuilder: 'Builder',
    advancedFilterBuilderAddButtonTooltip: 'Add filter or group',
    advancedFilterBuilderAddCondition: 'Add filter',
    advancedFilterBuilderAddJoin: 'Add group',
    advancedFilterBuilderApply: 'Apply',
    advancedFilterBuilderCancel: 'Cancel',
    advancedFilterBuilderEnterValue: 'Enter a value...',
    advancedFilterBuilderMoveDownButtonTooltip: 'Move down',
    advancedFilterBuilderMoveUpButtonTooltip: 'Move up',
    advancedFilterBuilderRemoveButtonTooltip: 'Remove',
    advancedFilterBuilderSelectColumn: 'Select a column',
    advancedFilterBuilderSelectOption: 'Select an option',
    advancedFilterBuilderTitle: 'Advanced filter',
    advancedFilterBuilderValidationAlreadyApplied: 'Current filter already applied.',
    advancedFilterBuilderValidationEnterValue: 'Must enter a value.',
    advancedFilterBuilderValidationIncomplete: 'Not all conditions are complete.',
    advancedFilterBuilderValidationSelectColumn: 'Must select a column.',
    advancedFilterBuilderValidationSelectOption: 'Must select an option.',
    advancedFilterContains: 'contains',
    advancedFilterEndsWith: 'ends with',
    advancedFilterEquals: '=',
    advancedFilterFalse: 'is false',
    advancedFilterGreaterThan: '>',
    advancedFilterGreaterThanOrEqual: '>=',
    advancedFilterLessThan: '<',
    advancedFilterLessThanOrEqual: '<=',
    advancedFilterNotBlank: 'is not blank',
    advancedFilterNotContains: 'does not contain',
    advancedFilterNotEqual: '!=',
    advancedFilterOr: 'OR',
    advancedFilterStartsWith: 'begins with',
    advancedFilterTextEquals: 'equals',
    advancedFilterTextNotEqual: 'does not equal',
    advancedFilterTrue: 'is true',
    advancedFilterValidationExtraEndBracket: 'Too many end brackets',
    advancedFilterValidationInvalidColumn: 'Column not found',
    advancedFilterValidationInvalidDate: 'Value is not a valid date',
    advancedFilterValidationInvalidJoinOperator: 'Join operator not found',
    advancedFilterValidationInvalidOption: 'Option not found',
    advancedFilterValidationJoinOperatorMismatch: 'Join operators within a condition must be the same',
    advancedFilterValidationMessage: 'Expression has an error. ${variable} - ${variable}.',
    advancedFilterValidationMessageAtEnd: 'Expression has an error. ${variable} at end of expression.',
    advancedFilterValidationMissingColumn: 'Column is missing',
    advancedFilterValidationMissingCondition: 'Condition is missing',
    advancedFilterValidationMissingEndBracket: 'Missing end bracket',
    advancedFilterValidationMissingOption: 'Option is missing',
    advancedFilterValidationMissingQuote: 'Value is missing an end quote',
    advancedFilterValidationMissingValue: 'Value is missing',
    advancedFilterValidationNotANumber: 'Value is not a number',
    advancedSettings: 'Advanced settings',
    after: 'After',
    aggregate: 'Aggregate',
    andCondition: 'AND',
    animation: 'Animation',
    applyFilter: 'Apply',
    april: 'April',
    area: 'Area',
    areaChart: 'Area',
    areaColumnComboTooltip: 'Area & Column',
    areaGroup: 'Area',
    ariaAdvancedFilterBuilderColumn: 'Column',
    ariaAdvancedFilterBuilderFilterItem: 'Filter condition',
    ariaAdvancedFilterBuilderGroupItem: 'Filter group',
    ariaAdvancedFilterBuilderItem: '${variable}. Level ${variable}. Press ENTER to edit',
    ariaAdvancedFilterBuilderItemValidation: '${variable}. Level ${variable}. ${variable} Press ENTER to edit',
    ariaAdvancedFilterBuilderJoinOperator: 'Join operator',
    ariaAdvancedFilterBuilderList: 'Advanced filter builder list',
    ariaAdvancedFilterBuilderOption: 'Option',
    ariaAdvancedFilterBuilderValueP: 'Value',
    ariaAdvancedFilterInput: 'Advanced filter input',
    ariaChartMenuClose: 'Close chart edit menu',
    ariaChartSelected: 'Selected',
    ariaChecked: 'checked',
    ariaColumn: 'Column',
    ariaColumnFiltered: 'Column filtered',
    ariaColumnGroup: 'Column group',
    ariaColumnPanelList: 'Column list',
    ariaColumnSelectAll: 'Toggle all columns visibility',
    ariaDateFilterInput: 'Date filter input',
    ariaDefaultListName: 'List',
    ariaDropZoneColumnComponentAggFuncSeparator: ' of ',
    ariaDropZoneColumnComponentDescription: 'Press DELETE to remove',
    ariaDropZoneColumnComponentSortAscending: 'ascending',
    ariaDropZoneColumnComponentSortDescending: 'descending',
    ariaDropZoneColumnGroupItemDescription: 'Press ENTER to sort',
    ariaDropZoneColumnValueItemDescription: 'Press ENTER to change the aggregation type',
    ariaFilterColumn: 'Press CTRL ENTER to open filter',
    ariaFilterColumnsInput: 'Filter columns input',
    ariaFilterFromValue: 'Filter from value',
    ariaFilterInput: 'Filter input',
    ariaFilterList: 'Filter list',
    ariaFilterMenuOpen: 'Open filter menu',
    ariaFilterPanelList: 'Filter list',
    ariaFilterToValue: 'Filter to value',
    ariaFilterValue: 'Filter value',
    ariaFilteringOperator: 'Filtering operator',
    ariaHidden: 'hidden',
    ariaIndeterminate: 'indeterminate',
    ariaInputEditor: 'Input editor',
    ariaLabelAdvancedFilterAutocomplete: 'Advanced filter autocomplete',
    ariaLabelAdvancedFilterBuilderAddField: 'Advanced filter builder add field',
    ariaLabelAdvancedFilterBuilderColumnSelectField: 'Advanced filter builder column select field',
    ariaLabelAdvancedFilterBuilderJoinSelectField: 'Advanced filter builder join operator select field',
    ariaLabelAdvancedFilterBuilderOptionSelectField: 'Advanced filter builder option select field',
    ariaLabelAggregationFunction: 'Aggregation Function',
    ariaLabelCellEditor: 'Cell editor',
    ariaLabelColumnFilter: 'Column filter',
    ariaLabelColumnMenu: 'Column menu',
    ariaLabelContextMenu: 'Context menu',
    ariaLabelDialog: 'Dialog',
    ariaLabelRichSelectDeleteSelection: 'Press DELETE to deselect item',
    ariaLabelRichSelectDeselectAllItems: 'Press DELETE to deselect all items',
    ariaLabelRichSelectField: 'Rich Select Field',
    ariaLabelRichSelectToggleSelection: 'Press SPACE to toggle selection',
    ariaLabelSelectField: 'Select Field',
    ariaLabelSubMenu: 'SubMenu',
    ariaLabelTooltip: 'Tooltip',
    ariaMenuColumn: 'Press ALT DOWN to open column menu',
    ariaPageSizeSelectorLabel: 'Page Size',
    ariaPivotDropZonePanelLabel: 'Column Labels',
    ariaRowDeselect: 'Press SPACE to deselect this row',
    ariaRowGroupDropZonePanelLabel: 'Row Groups',
    ariaRowSelect: 'Press SPACE to select this row',
    ariaRowSelectAll: 'Press Space to toggle all rows selection',
    ariaRowSelectionDisabled: 'Row Selection is disabled for this row',
    ariaRowToggleSelection: 'Press Space to toggle row selection',
    ariaSearch: 'Search',
    ariaSearchFilterValues: 'Search filter values',
    ariaSkeletonCellLoading: 'Row data is loading',
    ariaSkeletonCellLoadingFailed: 'Row failed to load',
    ariaSortableColumn: 'Press ENTER to sort',
    ariaToggleCellValue: 'Press SPACE to toggle cell value',
    ariaToggleVisibility: 'Press SPACE to toggle visibility',
    ariaUnchecked: 'unchecked',
    ariaValuesDropZonePanelLabel: 'Values',
    ariaVisible: 'visible',
    asc_option: 'Ascending Order by Option',
    august: 'August',
    autoRotate: 'Auto Rotate',
    automatic: 'Automatic',
    autosizeAllColumns: 'Autosize All Columns',
    autosizeThisColumn: 'Autosize This Column',
    avg: 'Average',
    axis: 'Axis',
    axisType: 'Axis Type',
    background: 'Background',
    bar: 'Bar',
    barChart: 'Bar',
    barGroup: 'Bar',
    before: 'Before',
    blank: 'Blank',
    blanks: '(Blanks)',
    blur: 'Blur',
    bold: 'Bold',
    boldItalic: 'Bold Italic',
    bottom: 'Bottom',
    boxPlot: 'Box Plot',
    boxPlotTooltip: 'Box Plot',
    bubble: 'Bubble',
    bubbleTooltip: 'Bubble',
    callout: 'Callout',
    calloutLabels: 'Callout Labels',
    cancelFilter: 'Cancel',
    cap: 'Cap',
    capLengthRatio: 'Length Ratio',
    categories: 'Categories',
    category: 'Category',
    categoryAdd: 'Add a category',
    categoryValues: 'Category Values',
    chartAdvancedSettings: 'Advanced Settings',
    chartDownload: 'Download Chart',
    chartDownloadToolbarTooltip: 'Download Chart',
    chartEdit: 'Edit Chart',
    chartLink: 'Link to Grid',
    chartLinkToolbarTooltip: 'Linked to Grid',
    chartMenuToolbarTooltip: 'Menu',
    chartRange: 'Chart Range',
    chartSettingsToolbarTooltip: 'Menu',
    chartStyle: 'Chart Style',
    chartSubtitle: 'Subtitle',
    chartTitle: 'Chart Title',
    chartTitles: 'Titles',
    chartUnlink: 'Unlink from Grid',
    chartUnlinkToolbarTooltip: 'Unlinked from Grid',
    circle: 'Circle',
    clearFilter: 'Clear',
    collapseAll: 'Close All Row Groups',
    color: 'Color',
    column: 'Column',
    columnChart: 'Column',
    columnChooser: 'Choose Columns',
    columnFilter: 'Column Filter',
    columnGroup: 'Column',
    columnLineCombo: 'Column & Line',
    columnLineComboTooltip: 'Column & Line',
    columns: 'Columns',
    combinationChart: 'Combination',
    combinationGroup: 'Combination',
    connectorLine: 'Connector Line',
    contains: 'Contains',
    copy: 'Copy',
    copyWithGroupHeaders: 'Copy with Group Headers',
    copyWithHeaders: 'Copy With Headers',
    copy_row: 'Copy Row',
    count: 'Count',
    cross: 'Cross',
    crosshair: 'Crosshair',
    crosshairLabel: 'Label',
    crosshairSnap: 'Snap to Node',
    csvExport: 'CSV Export',
    ctrlC: 'Ctrl+C',
    ctrlV: 'Ctrl+V',
    ctrlX: 'Ctrl+X',
    customCombo: 'Custom Combination',
    customComboTooltip: 'Custom Combination',
    cut: 'Cut',
    data: 'Set Up',
    dateFilter: 'Date Filter',
    dateFormatOoo: 'yyyy-mm-dd',
    december: 'December',
    decimalSeparator: '.',
    defaultCategory: '(None)',
    desc_option: 'Descending Order by Option',
    diamond: 'Diamond',
    direction: 'Direction',
    donut: 'Donut',
    donutTooltip: 'Donut',
    duplicate_record: 'Duplicate Record',
    durationMillis: 'Duration (ms)',
    empty: 'Choose one',
    enabled: 'Enabled',
    endAngle: 'End Angle',
    endsWith: 'Ends with',
    equals: 'Equals',
    excelExport: 'Excel Export',
    expandAll: 'Expand All Row Groups',
    expand_record: 'Expand Record',
    export: 'Export',
    false: 'False',
    february: 'February',
    fillOpacity: 'Fill Opacity',
    filterOoo: 'Filter...',
    filteredRows: 'Filtered',
    filters: 'Filters',
    first: 'First',
    firstPage: 'First Page',
    fixed: 'Fixed',
    font: 'Font',
    footerTotal: 'Total',
    format: 'Customize',
    greaterThan: 'Greater than',
    greaterThanOrEqual: 'Greater than or equal to',
    gridLines: 'Grid Lines',
    group: 'Group',
    groupBy: 'Group by',
    groupFilterSelect: 'Select field:',
    groupPadding: 'Group Padding',
    groupedAreaTooltip: 'Area',
    groupedBar: 'Grouped',
    groupedBarFull: 'Grouped Bar',
    groupedBarTooltip: 'Grouped',
    groupedColumn: 'Grouped',
    groupedColumnFull: 'Grouped Column',
    groupedColumnTooltip: 'Grouped',
    groupedSeriesGroupType: 'Grouped',
    groups: 'Row Groups',
    heart: 'Heart',
    heatmap: 'Heatmap',
    heatmapTooltip: 'Heatmap',
    height: 'Height',
    hierarchicalChart: 'Hierarchical',
    hierarchicalGroup: 'Hierarchical',
    histogram: 'Histogram',
    histogramBinCount: 'Bin count',
    histogramChart: 'Histogram',
    histogramFrequency: 'Frequency',
    histogramTooltip: 'Histogram',
    horizontal: 'Horizontal',
    horizontalAxisTitle: 'Horizontal Axis Title',
    inRange: 'Between',
    inRangeEnd: 'To',
    inRangeStart: 'From',
    innerRadius: 'Inner Radius',
    inside: 'Inside',
    invalidColor: 'Color value is invalid',
    invalidDate: 'Invalid Date',
    invalidNumber: 'Invalid Number',
    italic: 'Italic',
    itemPaddingX: 'Item Padding X',
    itemPaddingY: 'Item Padding Y',
    itemSpacing: 'Item Spacing',
    january: 'January',
    july: 'July',
    june: 'June',
    labelPlacement: 'Placement',
    labelRotation: 'Rotation',
    labels: 'Labels',
    last: 'Last',
    lastPage: 'Last Page',
    layoutHorizontalSpacing: 'Horizontal Spacing',
    layoutVerticalSpacing: 'Vertical Spacing',
    left: 'Left',
    legend: 'Legend',
    legendEnabled: 'Enabled',
    length: 'Length',
    lessThan: 'Less than',
    lessThanOrEqual: 'Less than or equal to',
    line: 'Line',
    lineDash: 'Line Dash',
    lineDashOffset: 'Dash Offset',
    lineGroup: 'Line',
    lineTooltip: 'Line',
    lineWidth: 'Line Width',
    loadingError: 'ERR',
    loadingOoo: 'Loading...',
    lookup_unamed_record: 'Unnamed Record',
    march: 'March',
    markerPadding: 'Marker Padding',
    markerSize: 'Marker Size',
    markerStroke: 'Marker Stroke',
    markers: 'Markers',
    max: 'Max',
    maxSize: 'Maximum Size',
    may: 'May',
    min: 'Min',
    minSize: 'Minimum Size',
    miniChart: 'Mini-Chart',
    more: 'More',
    navigator: 'Navigator',
    nextPage: 'Next Page',
    nightingale: 'Nightingale',
    nightingaleTooltip: 'Nightingale',
    noAggregation: 'None',
    noDataToChart: 'No data available to be charted.',
    noMatches: 'No matches',
    noPin: 'No Pin',
    noRowsToShow: 'No Rows To Show',
    none: 'None',
    normal: 'Normal',
    normalizedArea: '100% Stacked',
    normalizedAreaFull: '100% Stacked Area',
    normalizedAreaTooltip: '100% Stacked',
    normalizedBar: '100% Stacked',
    normalizedBarFull: '100% Stacked Bar',
    normalizedBarTooltip: '100% Stacked',
    normalizedColumn: '100% Stacked',
    normalizedColumnFull: '100% Stacked Column',
    normalizedColumnTooltip: '100% Stacked',
    normalizedSeriesGroupType: '100% Stacked',
    notBlank: 'Not blank',
    notContains: 'Does not contain',
    notEqual: 'Does not equal',
    november: 'November',
    number: 'Number',
    numberFilter: 'Number Filter',
    october: 'October',
    of: 'of',
    offset: 'Offset',
    offsets: 'Offsets',
    orCondition: 'OR',
    orientation: 'Orientation',
    outside: 'Outside',
    padding: 'Padding',
    page: 'Page',
    pageLastRowUnknown: '?',
    pageSizeSelectorLabel: 'Page Size:',
    paired: 'Paired Mode',
    parallel: 'Parallel',
    paste: 'Paste',
    pasting_multiple_columns_is_not_supportted_currently: 'Pasting multiple columns is not currently supported',
    perpendicular: 'Perpendicular',
    pie: 'Pie',
    pieChart: 'Pie',
    pieGroup: 'Pie',
    pieTooltip: 'Pie',
    pinColumn: 'Pin Column',
    pinLeft: 'Pin Left',
    pinRight: 'Pin Right',
    pivotChart: 'Pivot Chart',
    pivotChartAndPivotMode: 'Pivot Chart & Pivot Mode',
    pivotChartRequiresPivotMode: 'Pivot Chart requires Pivot Mode enabled.',
    pivotChartTitle: 'Pivot Chart',
    pivotColumnGroupTotals: 'Total',
    pivotColumnsEmptyMessage: 'Drag here to set column labels',
    pivotMode: 'Pivot Mode',
    pivots: 'Column Labels',
    plus: 'Plus',
    polarAxis: 'Polar Axis',
    polarAxisTitle: 'Polar Axis Title',
    polarChart: 'Polar',
    polarGroup: 'Polar',
    polygon: 'Polygon',
    position: 'Position',
    positionRatio: 'Position Ratio',
    predefined: 'Predefined',
    preferredLength: 'Preferred Length',
    previousPage: 'Previous Page',
    radarArea: 'Radar Area',
    radarAreaTooltip: 'Radar Area',
    radarLine: 'Radar Line',
    radarLineTooltip: 'Radar Line',
    radialBar: 'Radial Bar',
    radialBarTooltip: 'Radial Bar',
    radialColumn: 'Radial Column',
    radialColumnTooltip: 'Radial Column',
    radiusAxis: 'Radius Axis',
    radiusAxisPosition: 'Position',
    rangeArea: 'Range Area',
    rangeAreaTooltip: 'Range Area',
    rangeBar: 'Range Bar',
    rangeBarTooltip: 'Range Bar',
    rangeChartTitle: 'Range Chart',
    removeFromLabels: 'Remove ${variable} from labels',
    removeFromValues: 'Remove ${variable} from values',
    resetColumns: 'Reset Columns',
    resetFilter: 'Reset',
    reverseDirection: 'Reverse Direction',
    right: 'Right',
    rowDragRow: 'row',
    rowDragRows: 'rows',
    rowGroupColumnsEmptyMessage: 'Drag here to set row groups',
    row_group: 'Group',
    scatter: 'Scatter',
    scatterGroup: 'X Y (Scatter)',
    scatterTooltip: 'Scatter',
    scrollingStep: 'Scrolling Step',
    scrollingZoom: 'Scrolling',
    searchOoo: 'Search...',
    secondaryAxis: 'Secondary Axis',
    sectorLabels: 'Sector Labels',
    see_more_detail: 'More',
    selectAll: '(Select All)',
    selectAllSearchResults: '(Select All Search Results)',
    selectedRows: 'Selected',
    selectingZoom: 'Selecting',
    september: 'September',
    series: 'Series',
    seriesAdd: 'Add a series',
    seriesChartType: 'Series Chart Type',
    seriesGroupType: 'Group Type',
    seriesItemLabels: 'Item Labels',
    seriesItemNegative: 'Negative',
    seriesItemPositive: 'Positive',
    seriesItemType: 'Item Type',
    seriesItems: 'Series Items',
    seriesLabels: 'Series Labels',
    seriesPadding: 'Series Padding',
    seriesType: 'Series Type',
    setFilter: 'Set Filter',
    settings: 'Chart',
    shadow: 'Shadow',
    shape: 'Shape',
    size: 'Size',
    sortAscending: 'Sort Ascending',
    sortDescending: 'Sort Descending',
    sortUnSort: 'Clear Sort',
    spacing: 'Spacing',
    specializedChart: 'Specialized',
    specializedGroup: 'Specialized',
    square: 'Square',
    stackedArea: 'Stacked',
    stackedAreaFull: 'Stacked Area',
    stackedAreaTooltip: 'Stacked',
    stackedBar: 'Stacked',
    stackedBarFull: 'Stacked Bar',
    stackedBarTooltip: 'Stacked',
    stackedColumn: 'Stacked',
    stackedColumnFull: 'Stacked Column',
    stackedColumnTooltip: 'Stacked',
    stackedSeriesGroupType: 'Stacked',
    startAngle: 'Start Angle',
    startsWith: 'Begins with',
    statisticalChart: 'Statistical',
    statisticalGroup: 'Statistical',
    strokeColor: 'Line Color',
    strokeOpacity: 'Line Opacity',
    strokeWidth: 'Stroke Width',
    sum: 'Sum',
    sunburst: 'Sunburst',
    sunburstTooltip: 'Sunburst',
    switchCategorySeries: 'Switch Category / Series',
    textFilter: 'Text Filter',
    thickness: 'Thickness',
    thousandSeparator: ',',
    ticks: 'Ticks',
    tile: 'Tile',
    time: 'Time',
    timeFormat: 'Time Format',
    timeFormatDashesYYYYMMDD: 'YYYY-MM-DD',
    timeFormatDotsDDMYY: 'DD.M.YY',
    timeFormatDotsMDDYY: 'M.DD.YY',
    timeFormatHHMMSS: 'HH:MM:SS',
    timeFormatHHMMSSAmPm: 'HH:MM:SS AM/PM',
    timeFormatSlashesDDMMYY: 'DD/MM/YY',
    timeFormatSlashesDDMMYYYY: 'DD/MM/YYYY',
    timeFormatSlashesMMDDYY: 'MM/DD/YY',
    timeFormatSlashesMMDDYYYY: 'MM/DD/YYYY',
    timeFormatSpacesDDMMMMYYYY: 'DD MMMM YYYY',
    title: 'Title',
    titlePlaceholder: 'Chart Title',
    to: 'to',
    tooltips: 'Tooltips',
    top: 'Top',
    totalAndFilteredRows: 'Rows',
    totalRows: 'Total Rows',
    treemap: 'Treemap',
    treemapTooltip: 'Treemap',
    triangle: 'Triangle',
    true: 'True',
    ungroupAll: 'Un-Group All',
    ungroupBy: 'Un-Group by',
    valueAggregation: 'Value Aggregation',
    valueColumnsEmptyMessage: 'Drag here to aggregate',
    values: 'Values',
    vertical: 'Vertical',
    verticalAxisTitle: 'Vertical Axis Title',
    waterfall: 'Waterfall',
    waterfallTooltip: 'Waterfall',
    weight: 'Weight',
    whisker: 'Whisker',
    width: 'Width',
    xAxis: 'Horizontal Axis',
    xOffset: 'X Offset',
    xType: 'X Type',
    xyChart: 'X Y (Scatter)',
    xyValues: 'X Y Values',
    yAxis: 'Vertical Axis',
    yOffset: 'Y Offset',
    zoom: 'Zoom',
  },
  agenda: {
    agenda: 'Agenda',
    description: 'Description',
    reminder_time: 'Reminder Time',
  },
  ai: {
    ai_image_generation: 'AI Image Generation',
    ai_translate_all: 'AI Translate All Languages',
    artifact_code: 'Code',
    artifact_preview: 'Preview',
    artifact_template_code: 'Template Code',
    artifact_workflow: 'Workflow',
    completed: 'Completed',
    generate: 'AI Generate',
    generate_image: 'Generate Image',
    generated_characters: 'Generated {characters} characters, completed {percent}%',
    generated_result: 'Generated Result',
    generating: 'Generating...',
    history: 'History',
    in_progress: 'In Progress',
    insert: 'Insert Content',
    launcher_esc: 'Close',
    launcher_open: 'Open',
    launcher_select: 'Select',
    launcher_tips_prefix: 'Use the',
    launcher_tips_suffix: 'to switch between filters',
    load_more: 'Load More',
    name: 'AI Writer',
    new_chat: 'New Chat',
    overwrite: 'Replace Content',
    pick_an_image: 'Pick an image:',
    press_enter: 'Press Enter to send, Shift+Enter to new line',
    reference: 'Reference {count} materials',
    regenerate: 'Regenerate',
    restore_conversation: 'Restore Conversation',
    share_conversation: 'Share Conversation',
    start_chat: 'Start Chat',
    thinking: 'Thinking...',
    thought: 'Thought for {seconds} seconds',
    type_message: 'Type a message',
    voice_hold_to_speak: 'Hold to Speak',
    voice_release_to_send: 'Release to send',
  },
  ai_consultant: {
    deep_think: 'Deep Think',
    description:
      'We are your AI business consultants. We can help you analyze your business, generate solution proposals and build automated AI systems for you',
    history: 'My history',
    quick: 'Standard',
    replay: 'Replay',
    title: 'AI Consultant Team',
  },
  api: {
    api: 'API',
    create_token: 'Create token',
    create_token_description: 'These tokens allow other apps to control your whole account. Be careful!',
    create_token_success: 'Create Token Success',
    created_public_api_token: 'Created Public API Token',
    delete_token: 'Delete Token',
    delete_token_description: 'Are you sure you want to delete this token?',
    delete_token_success: 'Delete Token Success',
    developer_api: 'Developer API',
    e180days: '180 days',
    e1day: '1 day',
    e1month: '1 month',
    e1year: '1 year',
    e2month: '2 month',
    e30days: '30 days',
    e3days: '3 days',
    e6month: '6 month',
    e7days: '7 days',
    expiration: 'Expiration',
    my_tokens: 'My tokens',
    my_tokens_description: 'Your API tokens need to be treated as securely as any other password.',
    name: 'Name',
    never: 'No Expiration',
    select_expiration: 'Select expiration date',
  },
  auth: {
    agree_description:
      'Thank you for using Bika.ai. The following terms will help you understand the user and privacy policies, as well as the rights you enjoy. You can view the full content through {privacy} and {team}.',
    agree_title: 'Please read and agree to the terms of service',
    auth_error: 'Authentication failed',
    auth_error_description: 'Authentication failed. Please try again later.',
    back: 'Back',
    back_website: 'Back to official website',
    continue_with_apple: 'Continue with Apple',
    continue_with_email: 'Continue with Email',
    continue_with_facebook: 'Continue with Facebook',
    continue_with_github: 'Continue with GitHub',
    continue_with_google: 'Continue with Google',
    continue_with_phone: 'Continue with Phone',
    continue_with_twitter: 'Continue with Twitter',
    continue_with_username: 'Continue with Username',
    continue_with_weixin: 'Continue with Wexin',
    get_started: 'Get Started',
    invite_you_to_join: 'Invite You to Join',
    invite_you_to_register: '{me} Invite you to register',
    link_account_already_exists:
      'The {type} account has been bound to another Bika account. Would you like to log in directly to the bound account?',
    loading: 'logging in...',
    login: 'Login',
    login_and_register: 'Login & Register',
    login_success: 'Login Successful',
    logout: 'Logout',
    no_permission:
      'Sorry, this invitation link is only for internal corporate users. Please log in with your corporate email or contact the administrator for a valid invitation link',
    or: 'Or',
    password: 'Password',
    password_is_required: 'Password is required.',
    privacy_policy: 'Privacy policy',
    quick_login: 'Quick Login',
    quick_login_description: 'Quick register a new user without password and veriifcation.',
    register_agreement: 'By continuing, you agree to our {team} and {privacy}',
    scan_qrcode_to_login: 'Scan QR code to login',
    sign_in: 'Sign in',
    sign_up: 'Sign up',
    switch_account: 'Switch Account',
    terms_and_conditions:
      "Please read and accept our Privacy Policy and Terms of Service. We're committed to protecting your privacy and data.",
    terms_of_service: 'Terms of Service',
  },
  automation: {
    action: {
      actions: 'Actions',
      ai_summary: {
        description: 'Summarize text content using an AI model.',
        name: 'AI Text Summary',
      },
      call_agent: {
        agent_id_subject: 'Agent ID',
        description: 'Call a specified agent with a message.',
        message_subject: 'Message',
        name: 'Call Agent',
        recipient: 'Recipient',
      },
      condition: {
        description: 'Execute different actions based on the result of a condition.',
        name: 'Condition',
      },
      create_document: {
        description: 'Dynamically generate documents through a combination of multiple steps and variables.',
        name: 'Create Document',
      },
      create_mission: {
        description: 'Create a new mission and assign it to specified members, roles, or teams.',
        name: 'Create Mission',
      },
      create_node_resource: {
        description:
          'Dynamically generate resource nodes (database, document, automation, form, etc.) through a combination of multiple steps and variables.',
        name: 'Create Node Resource',
      },
      create_record: {
        description: 'Create a new record in a specified database.',
        name: 'Create Record',
      },
      deepseek_generate_text: {
        description: 'Generate text content using the DeepSeek API.',
        name: 'DeepSeek - Generate Text',
      },
      delay: {
        description: 'Pause for a certain amount of time before executing the next action.',
        name: 'Delay',
        queue: 'Queue',
        unit: 'Unit',
        unit_day: 'Day',
        unit_hour: 'Hour',
        unit_minute: 'Minute',
        unit_second: 'Second',
        unit_week: 'Week',
        value: 'Value',
      },
      description: 'Note',
      dingtalk_webhook: {
        description: "Send a message to a specified group via the DingTalk custom robot's Webhook URL.",
        message_title_description:
          'Enter the title of the message to be sent, which will be displayed in the summary of the message list on the left side of DingTalk.',
        name: 'Send Message to DingTalk Group',
      },
      dummy_action: {
        description: 'Used for testing and validating automation process actions.',
        name: 'Dummy Action',
      },
      feishu_webhook: {
        description: "Send a message to a specified group via the Feishu custom robot's Webhook URL.",
        name: 'Send Message to Feishu Group',
      },
      filter: {
        description: 'If the filter conditions are met, subsequent actions will continue to run',
        name: 'Filter',
      },
      find_dashboard: {
        description: 'Find a specified dashboard.',
        name: 'Find Dashboard',
      },
      find_members: {
        description: 'Find space members who meet certain criteria.',
        name: 'Get Members List',
        to_email_addresses: 'Email addresses',
        to_email_addresses_description: 'Separate multiple email addresses with commas, type "/" to insert a variable',
      },
      find_missions: {
        description: 'Find specified missions.',
        name: 'Find Missions',
      },
      find_records: {
        description: 'Retrieve several records from a specified database, based on a view or filtering criteria.',
        find_database: 'Filter based on view',
        find_database_select: 'Filter based on database',
        name: 'Get Records',
        placeholder_select_type: 'Please select a type',
        records_limit_description: 'Maximum records per search. Returns a single record when set to 1.',
        records_limit_placeholder: '1 - 100',
        records_limit_title: 'Record limit',
        title_interrupt_if_no_record: 'Interrupt if no record is found',
        type: 'Search method',
      },
      find_widget: {
        description: 'Find a specified widget.',
        name: 'Find Widget',
        widget_empty: 'Widget cannot be empty',
      },
      formapp_ai: {
        create_and_load_formapp: 'Create action and load FormApp apps',
        description:
          'FormApp.ai provides tons of AI models, actions and extensions to help you automate your workflow. You can also custom your own AI models, actions, APIs on it.',
        name: 'FormApp.ai',
      },
      loop: {
        abort_loop_help:
          'The entire loop terminates immediately if any child action fails, skipping all remaining iterations.',
        abort_loop_title: 'Abort Loop on Failure',
        add_action: 'Add Action',
        child_actions: 'Child Actions',
        child_actions_help: 'Actions executed sequentially within each loop iteration.',
        description:
          'Used for automating repetitive tasks by iterating through datasets and triggering corresponding actions based on preset conditions.',
        name: 'Loop',
        retrieve: 'Choose Array Data from Previous Step',
        retrieve_description:
          'Select array output from a previous step for iteration. Only array-type data is supported.',
        sequential_exec: 'Sequential Execution',
        sequential_exec_help:
          'Process array elements in strict index order, completing each iteration before starting the next.Uncheck for parallel processing.',
      },
      not_found: 'Action not found',
      openai_generate_text: {
        apikey: 'API Key',
        description: 'Generate text content using the OpenAI API.',
        model: 'Model',
        name: 'OpenAI - Generate Text',
        placeholder: 'Please select a model',
        prompt: 'Prompt',
        timeout_label: 'Timeout (seconds)',
        timeout_placeholder:
          'Please enter the timeout time, the maximum value is 300 seconds, the default is 60 seconds when it is missing',
      },
      random: {
        description: 'Randomly select one option from several inputs as the output.',
        name: 'Random',
      },
      replace_file: {
        description: 'Bulk replace files using records from the database.',
        name: 'Replace File',
      },
      round_robin: {
        description: 'Sequentially cycle through several inputs as the output.',
        name: 'Round Robin',
      },
      run_script: {
        description: 'Write script code to perform custom operations. Supports Python, JavaScript.',
        name: 'Run Script',
      },
      send_email: {
        description: 'Send an email to specified recipients.',
        name: 'Send Email',
        opens: 'View Rate',
        sent: 'Sent',
      },
      send_report: {
        description: 'Generate a report and send it to specified members or groups.',
        name: 'Send Report',
      },
      slack_webhook: {
        description: "Send a message to a specified channel via the Slack app's Incoming Webhook.",
        name: 'Send Message to Slack Channel',
      },
      telegram_send_message: {
        description: 'Send a message to a specified user or group via Telegram Bot.',
        help_text_chat_id: 'Public channel chat ID needs to be started with character "@", e.g. @channel_name',
        name: 'Send Message to Telegram',
      },
      toolsdk_ai: {
        create_and_load_toolsdk: 'Create action and load ToolSDK tools',
        description:
          'ToolSDK.ai provides 2000+ MCP servers and 10000+ AI tools. It allows you to easily use third-party AI capabilities in your automation processes, quickly expanding your workflow functionality.',
        name: 'MCP Server (by ToolSDK.ai)',
      },
      twitter_upload_media: {
        description: 'Upload media files via X(Twitter) API, supporting images and videos.',
        media: 'Media Url',
        name: 'X(Twitter) - Upload Media',
      },
      type: 'Action type',
      update_record: {
        description: 'Update one or more records in a specified database.',
        name: 'Update Record',
      },
      webhook: {
        description: 'Initiate an HTTP request to interact with other systems. ',
        edit: 'Edit request body',
        name: 'Send HTTP Request',
      },
      wecom_webhook: {
        description: "Send a message to a specified group via the WeCom group robot's Webhook URL.",
        name: 'Send Message to WeCom Group',
      },
      x_create_tweet: {
        auth_method: 'Authentication Method',
        auth_method_help_text:
          'Different authentication methods require different integration instances. Please reselect the integration after changing the authentication method.',
        auth_method_tooltip: 'Learn more about the difference between OAuth 1.0a and OAuth 2.0',
        description: 'Publish a tweet via the X(Twitter) API.',
        media_ids: 'Media',
        media_ids_help_text:
          'To add images or videos to the tweet, please use the "X(Twitter) - Upload Media" action to get the media id, and then insert it here using the variable selector.',
        name: 'X(Twitter) - Create Tweet',
        tweet_content: 'Tweet Content',
        x_account_integration: 'X Account Integration',
      },
    },
    action_help_urls: {
      DINGTALK_WEBHOOK: '/help/reference/automation-action/dingtalk-webhook',
      DINGTALK_WEBHOOK_MARKDOWN: 'https://open.dingtalk.com/document/isvapp/message-type',
      FEISHU_WEBHOOK: '/help/reference/automation-action/feishu-webhook',
      FEISHU_WEBHOOK_MARKDOWN: 'https://open.feishu.cn/document/client-docs/bot-v3/add-custom-bot#5a997364',
      SEND_EMAIL: '/help/reference/integration/smtp-email-account',
      SLACK_WEBHOOK: '/help/reference/automation-action/slack-webhook',
      SLACK_WEBHOOK_MARKDOWN: 'https://api.slack.com/reference/surfaces/formatting',
      TELEGRAM_SEND_MESSAGE: '/help/reference/automation-action/telegram-send-message',
      TELEGRAM_SEND_MESSAGE_MARKDOWN: 'https://core.telegram.org/bots/api#formatting-options',
      TWITTER_UPLOAD_MEDIA: '/help/reference/automation-action/twitter-upload-media',
      WEBHOOK: '/help/reference/automation/webhook-action',
      WECOM_WEBHOOK: '/help/reference/automation-action/wecom-webhook',
      WECOM_WEBHOOK_MARKDOWN: 'https://developer.work.weixin.qq.com/document/path/91770#markdown%E7%B1%BB%E5%9E%8B',
      WECOM_WEBHOOK_TEMPLATE_CARD:
        'https://developer.work.weixin.qq.com/document/path/91770#%E6%A8%A1%E7%89%88%E5%8D%A1%E7%89%87%E7%B1%BB%E5%9E%8B',
      X_CREATE_TWEET: '/help/reference/automation-action/x-create-tweet',
    },
    action_type_intro: {
      DINGTALK_WEBHOOK: 'Send messages to the specified group via the DingTalk custom bot’s webhook URL.',
      FEISHU_WEBHOOK: 'Send messages to the specified group via the Feishu custom bot’s webhook URL.',
      SLACK_WEBHOOK: 'Send messages to the specified channel via the Slack app’s Incoming webhook.',
      TELEGRAM_SEND_MESSAGE: 'Send messages to the specified user or group via Telegram Bot.',
      WEBHOOK: 'Construct an cutom HTTP request and send it to the specified URL.',
      WECOM_WEBHOOK: 'Send messages to the specified WeCom group via the WeCom bot’s webhook URL.',
      X_CREATE_TWEET: 'Create a tweet via the Twitter API and OAuth 2.0.',
    },
    add_action: 'Add an action',
    add_automation: 'Add Automation',
    add_trigger: 'Add a trigger',
    advanced_options: 'Advanced Options',
    automation: 'Automation',
    cancel_delay: 'Cancel delay',
    cancel_delay_content: 'Are you sure you want to cancel the delay?',
    cancel_delay_success: 'Cancel delay successfully',
    choose_imap_integration: 'IMAP Email integration',
    choose_integration: 'Available Integration',
    close_automation: 'Disable automation',
    closed: 'Closed',
    coming_soon_description: 'This feature is coming soon! Any suggestions to speed up faster!',
    coming_soon_feedback: 'Thank you for your feedback!',
    coming_soon_placeholder: 'Please leave your feed here, thank you very much!',
    copy_of: 'Copy of {name}',
    description_empty: 'No description',
    description_help_text:
      'Please provide a concise and clear note, which will be displayed on the automation main interface for better understanding.',
    disabled: 'OFF',
    drop_files_here: 'Drop files here',
    duplicate_success: 'Duplication Successful',
    email_content_type_label: 'Email content type',
    empty_history: 'No run history',
    empty_step: 'Automation steps not set.',
    empty_step_log: 'No history logs for this step',
    enable: 'Enable',
    enabled: 'ON',
    history: 'Run history',
    history_status: {
      cancelled: 'Cancelled',
      delay: 'Delay',
      failed: 'Failed',
      running: 'Running',
      success: 'Success',
      timeout: 'Timeout',
    },
    http_if_change: {
      every_failed: 'Every failed',
      first_failed: 'First failed',
      policy_title: 'Policy',
      response_changed: 'Response changed',
      response_compare_json_path: 'Response compare JSON path',
    },
    inbound_email: {
      description_guide: 'How to write search criteria?',
      label_mailbox_archive: 'Archive',
      label_mailbox_deleted: 'Deleted',
      label_mailbox_drafts: 'Drafts',
      label_mailbox_inbox: 'INBOX',
      label_mailbox_junk: 'Junk',
      label_mailbox_sent: 'Sent',
      placeholder_mailbox_name: 'Enter the folder name, default is INBOX',
      placeholder_search_criteria: 'Enter email search criteria ',
      search_rule_description:
        "See {help}'s search function for more details,If not filled in, it defaults to all emails in the mailbox",
      title_download_attachments: 'Download Attachments',
      title_mailbox_name: 'Folder',
      title_search_criteria: 'Custom email search criteria',
      toast_imap_connection_error: 'IMAP connection failed, please check the configuration',
    },
    input: 'Input',
    item_output: 'Item Output',
    label: {
      manual_input: 'Enter URL manually',
      select_integration: 'Extract URL from integration',
    },
    manual_execution_description: 'Are you sure you want to run this automation now?',
    manual_execution_success: 'The automation has started',
    manual_trigger_fields: 'Dynamic fields',
    manual_trigger_fields_desc:
      'The fields to be filled before triggering. These fields can be referenced by any action throughout the workflow.',
    manual_trigger_result: 'Result',
    manual_trigger_result_desc: 'Test this step to confirm its configuration is correct',
    max_trigger_count: 'The maximum number of triggers is 3',
    no_description: 'No description',
    off: 'Off',
    on: 'On',
    open_automation: 'Activate automation',
    output: 'Output',
    parameter_source: 'Parameter source',
    parameter_source_description:
      'You can choose to manually configure the parameters or reuse the settings from the selected integration. ',
    parameter_source_option1: 'Manual configuration',
    parameter_source_option2: 'Use integration settings',
    placeholder_choose_integration: 'Click to choose or create',
    recent_history: 'Recently run history',
    recent_history_detail: 'Details',
    recent_history_id: 'ID',
    recent_history_start_time: 'Start time',
    recent_history_status: 'Status',
    record_list: 'Record list',
    repeat_for_each_in: 'Run actions below for each item in the list',
    report_content_description: 'Supports Markdown and HTML format, type "/" to insert a variable',
    report_content_type_label: 'Content type',
    report_markdown: 'Content',
    report_markdown_placeholder: 'Please enter the markdown content',
    report_prompt: 'Report prompt',
    report_prompt_placeholder: 'Please enter the report prompt',
    report_subject: 'Subject',
    report_subject_placeholder: 'Please enter the report subject',
    report_type: 'Report type',
    round_robin: {
      label: {
        select_action: 'Select action',
        select_database: 'Select database',
        select_target: 'Target',
        select_view: 'Select View',
      },
      type: {
        database: 'Database',
        database_view: 'Database view',
        prev_action: 'Previous action',
        round_type: 'Type',
        user: 'User',
      },
    },
    run: {
      failed: 'failed',
      status: 'Run {status} at {time}',
      success: 'successful',
    },
    run_immediately: 'Run now',
    run_immediately_description: 'The automation will run immediately and only once',
    run_test: {
      description: 'Run a test to verify the automation flow and ensure it works as expected.',
      preview_button: 'Preview last run',
      run_button: 'Run test',
      title: 'Run test',
    },
    script: 'Script',
    script_language: 'Script language',
    send_message_to_slack: 'Send Message to Slack',
    sort: 'Sort',
    started: 'Started',
    status: 'Status',
    then: 'Then',
    title_manual_execution: 'Run the automation now',
    tooltip_learn_more: 'Learn more',
    tooltips: {
      step_error:
        'Required configuration items are missing in the current step, and this step may not work properly. Please check',
    },
    trigger: {
      add_action: 'Add action',
      add_trigger: 'Add trigger',
      button_clicked: {
        description: 'Perform action when clicking the button in the database button field.',
        name: 'Button Field Click',
      },
      datetime_field_reached: {
        ahead: 'Ahead',
        days: 'Days',
        delay: 'Delay',
        description:
          'Automatically execute actions when a datetime field in the record approaches or reaches a specific date.',
        hours: 'Hours',
        minutes: 'Minutes',
        name: 'Datetime Field Reminder',
        offset: 'Offset',
        offset_day: 'Advance or postpone by several days',
        offset_day_placeholder: 'Neg. for earlier, pos. for later, 0 for today',
        offset_hour: 'Advance or postpone by several hours',
        offset_hour_placeholder: 'Neg. for earlier, pos. for later, 0 for current hour',
        offset_minute: 'Advance or postpone by several minutes',
        offset_minute_placeholder: 'Neg. for earlier, pos. for later, 0 for current minute',
        today: 'Today',
      },
      description: 'Note',
      dummy_trigger: {
        description: 'Used for testing and validating automation flow trigger conditions.',
        name: 'Dummy',
      },
      form_submitted: {
        description: 'Executes actions when a form is submitted.',
        name: 'Form Submitted',
      },
      http_if_change: {
        description: 'Automatically trigger when HTTP response changes are detected.',
        name: 'HTTP If Change',
      },
      inbound_email: {
        description: 'Automatically execute actions when an email is received.',
        name: 'Inbound Email',
      },
      manually: {
        description: "Execute action when triggered by user's manual click.",
        name: 'Manually Run',
      },
      member_joined: {
        description: 'Executes actions when a new member joins the space.',
        name: 'Member Joined',
      },
      not_found: 'Trigger not found',
      record_created: {
        description: 'Executes actions when a new record is added to the database.',
        name: 'Record Created',
      },
      record_match: {
        description: 'Executes actions when a new or edited record meets the criteria.',
        name: 'Record Match',
      },
      scheduler: {
        at_least_one_option: 'Please select at least one option',
        description: 'Automatically executes actions when the set time is reached.',
        name: 'Scheduler',
      },
      select_database: 'Select database',
      select_form: 'Select Form',
      select_form_have_not: 'There are currently no forms available for selection',
      select_form_note: 'This trigger requires selecting a form to complete the configuration',
      select_form_placeholder: 'Please select a form',
      select_match: 'Match condition',
      triggers: 'Triggers',
      type: 'Trigger type',
      webhook_received: {
        description:
          'Used when external systems need to send data to Bika.ai. Webhook provides a unique URL link that allows third-party applications (such as e-commerce platforms, CRM systems, etc.) to automatically transmit information to Bika.ai when specific events occur, thereby triggering corresponding automated workflows.',
        name: 'Webhook Received',
        placeholder: 'A unique Webhook link will be generated after saving',
        url: 'Webhook Received URL',
      },
    },
    trigger_creation_success: 'Create trigger successfully',
    trigger_help_urls: {
      form_submit: '/help/reference/node-resource/form',
      inbound_email: '/help/reference/automation-trigger/inbound-email',
    },
    trigger_history_empty: 'This run was manually triggered by the user and has no trigger history',
    trigger_update_success: 'Update trigger successfully',
    updating: 'Updating',
    variable: {
      placeholder: 'Enter "/" insert a variable',
    },
    variable_select: {
      automation: {
        id: 'Automation ID',
        interrupt_url: 'Interrupt delay automation url',
        name: 'Automation name',
        run_history_id: 'Run ID',
      },
      choose: 'Choose',
      createdAt: 'Created at',
      data_empty: 'No data',
      database: {
        id: 'Database ID',
        name: 'Database name',
        url: 'Database url',
      },
      databaseId: 'Database ID',
      database_title: 'Database',
      field: {
        clear_all: 'clear all',
        data: 'Raw data',
        data_tips: 'Raw field data for updating records or other operations.',
        doc_id: 'Document ID',
        doc_name: 'Document Name',
        id: 'Field ID',
        join: 'Join value',
        name: 'Field Name',
        select_all: 'select all',
        type: 'Field type',
        value: 'Field value',
        value_tips: 'Formatted field data ready for display or insertion into emails/texts.',
      },
      fields: 'Fields',
      getting_variable: 'Getting variable...',
      global: {
        key: 'Global',
        no_result_found: 'No search records',
        official_website: 'Official website',
      },
      id: 'ID',
      insert: 'Insert',
      item: 'Current item',
      item_actions: 'Item actions',
      item_description: 'From the repeating list',
      item_title: 'Repeating group sources',
      member_email: 'Member Email',
      member_id: 'Member id',
      member_ids: 'Member id list',
      member_ids_tips: 'An array of member IDs, which can be processed one by one using a loop action',
      member_name: 'Member name',
      member_user_id: 'Member user id',
      members: 'Member list',
      members_length: 'Members length',
      members_tips: 'An array of members, which can be processed one by one using a loop action',
      menu_insert_variable_title: 'Use data from',
      name: 'Name',
      other_title: 'Other',
      placeholder: 'Select a variable',
      primary_component: 'Primary component',
      record: {
        cell: 'Fields',
        grid_list: 'Grid list(Text)',
        grid_tips: 'Format records as text-based table',
        id: 'Record id',
        list_tips: 'Format records as text-based list',
        record_id_list: 'Record id list',
        record_id_list_tips: 'An array of record IDs, which can be processed one by one using a loop action',
        record_list: 'Record list(Text)',
        record_tips: 'Record instance, can select a specific field from it',
        records: 'Records',
        records_length: 'Records length',
        records_tips: 'An array of records, which can be processed one by one using a loop action',
        selected_fields: 'Selected fields',
        url: 'Record url',
      },
      recordId: 'Record id',
      record_title: 'Record',
      resume_time: 'Resume time​',
      resume_time_tips: 'The time when the workflow automatically continues from this step',
      revision: 'Revision',
      round_robin_item: 'Round robin item',
      run_time: 'Execution Time',
      run_time_tips: 'The time when the workflow is triggered',
      select_data_placeholder: 'Select data',
      select_data_title: 'Select data',
      select_variable: 'Select',
      space: {
        home_page_url: 'Space homepage URL',
        id: 'Space id',
        name: 'Space name',
        report_page_url: 'Report page URL',
        todo_page_url: 'Todo page URL',
      },
      updatedAt: 'Updated at',
      url: 'URL',
      variable_component: 'Variable component',
    },
    webhook: {
      add_field: 'Add a field',
      add_header: 'Add a header',
      description_guide: 'Configuration guide',
      description_message_content: 'Supports Markdown format, type "/" to insert a variable',
      description_webhook_source:
        'The current action requires a Webhook URL as the target address for sending messages. You can either manually enter the webhook URL or select one from the existing integrations in the space for reuse.',
      feishu_type_interactive: 'Message Card',
      feishu_type_post: 'Rich text',
      feishu_type_text: 'Text Message',
      field_value: 'Field Value',
      message_type_actioncard: 'ActionCard',
      message_type_link: 'Link',
      message_type_templatecard: 'TemplateCard',
      message_type_text: 'Text Message',
      placeholder_field_name: 'Enter field name',
      placeholder_field_value: 'Enter field value',
      placeholder_header_name: 'Name',
      placeholder_header_value: 'Value',
      placeholder_request_method: 'Select a request method',
      placeholder_request_url: 'enter the request URL',
      placeholder_webhook_source: 'Please select a kind of webhook source',
      placeholder_webhook_url: 'Please enter the Webhook URL',
      support_format: 'Supported formats',
      title_body_type: 'Body Type',
      title_content_type: 'Content Type',
      title_form_data: 'form-data',
      title_message_content: 'Message Content',
      title_message_title: 'Message Title',
      title_message_type: 'Message Types',
      title_request_content: 'Request Content',
      title_request_headers: 'Headers',
      title_request_method: 'Request Method',
      title_request_url: 'URL',
      title_webhook_source: 'Webhook Source',
      title_webhook_url: 'Webhook URL',
      webhook_json_error: 'Body must be a valid JSON object',
    },
    when: 'When',
  },
  avatar: {
    avatar: 'Avatar',
    cancel_select: 'Cancel selection',
    change_avatar: 'Change avatar',
    edit_image: 'Edit',
    file_tip: 'Supports JPG, PNG, and GIF formats, image size must be within 2 MB',
    gif_no_crop_tip: 'GIF files will be uploaded without cropping to preserve animation',
    paste_image_link: 'Paste an image link...',
    preview: 'Preview',
    preview_avatar: 'Avatar preview',
    re_select: 'Re-select',
    select_from_gallery: 'Select from Gallery',
    tab_color: 'Color',
    tab_link: 'Link',
    tab_link_tip: 'Applicable to any image on the web.',
    tab_upload: 'Upload',
    take_photo: 'Take photo',
    upload_avatar: 'Upload image',
  },
  brand: {
    about_brand: 'About Bika.ai',
    brand: 'Bika.ai',
    website: 'Official website',
  },
  buttons: {
    add: 'Add',
    add_virtual_intelligent_task: 'Add virtual intelligent task',
    back: 'Back',
    back_to_space: 'Back to my space',
    cancel: 'Cancel',
    change_bound_email: 'Change bound email',
    close: 'Close',
    completed: 'Completed',
    confirm: 'Confirm',
    create: 'Create',
    create_space: 'Create space',
    delete: 'Delete',
    edit: 'Edit',
    more: 'More...',
    pre_fill_title_btn: 'Pre-fill',
    remove: 'Remove',
    run: 'Run',
    save: 'Save',
    see_more: 'More',
    send_your_suggestion: 'Send your suggestion',
    submit: 'Submit',
    view_all: 'View all',
  },
  cancel: 'Cancel',
  coming_soon: 'Coming soon',
  components: {
    breadscrumb: {
      root: 'Root',
    },
    configure_multilingual: 'Configure multilingual',
    confirm_remove_multilingual_configuration: 'Are you sure to remove the multilingual configuration?',
    disable_multilingual_warning: 'Disabling will clear the existing content',
    enable_multilingual_warning: 'Enabling will clear the content and reconfigure the language',
    remove_multilingual_configuration_warning:
      'Once deleted, it cannot be restored, and only one language will be retained. If you need to add more, you must reconfigure.',
    view_all_languages: 'View All languages',
  },
  confirm: 'Confirm',
  copy: {
    copy: 'Copy',
    copy_link: 'Copy link',
    copy_link_to_clipboard: 'Copy link to clipboard',
    copy_success: 'Copy successful',
    create_short_url: 'Create short URL',
    delete_short_url: 'Delete short URL',
  },
  dashboard: {
    add_widget: 'Add widget',
    select_data_source: 'Select data source',
    widget_not_editable: 'This widget does not support modification.',
  },
  dashboard_widgets: {
    ai_widget: {
      description: 'A Widget that AI-generated content',
      name: 'AI Widget',
    },
    bika: {
      description: 'A component that displays bika',
      name: 'bika',
    },
    chart: {
      description:
        'Visualize data from tables in various forms such as bar charts, line charts, scatter plots, pie charts, etc.',
      name: 'Chart',
    },
    embed: {
      description: 'Embed content from other websites by entering a URL',
      name: 'Embed webpage',
    },
    icons: {
      description: 'A component that displays icons',
      name: 'Icons',
    },
    list: {
      description: 'A component that displays lists',
      name: 'List',
    },
    number: {
      description: 'Counts any column of data in a database and displays the count in a prominent style on the widget',
      name: 'Highlight number',
    },
    pivot_table: {
      description: 'A quick data analysis tool for categorizing and summarizing detailed databases',
      name: 'Pivot table',
    },
    progress_bar: {
      description: 'A component that displays a progress bar',
      name: 'Progress bar',
    },
    text: {
      description: 'A component that displays text',
      name: 'Text block',
    },
  },
  data: {
    data: 'Data',
    database: 'Database',
  },
  database_fields: {
    ai_photo: {
      description:
        'AI-generated images and visual content suitable for product displays, social media and other scenarios',
      name: 'AI Photo',
    },
    ai_text: {
      ai_write: 'AI Generate',
      ai_writing: 'AI is writing...',
      auto_update: 'Auto update',
      description:
        'AI-generated text content that can reference data within database, suitable for customer service replies, product descriptions, content summaries and other scenarios to improve creation efficiency',
      llm_provider: 'AI Provider',
      name: 'AI Text',
      preview_btn: 'Preview',
      preview_empty: 'No preview',
      preview_result: 'Preview Result',
      preview_result_description: 'This preview is based on the first record in this database',
      prompt_description: 'Please enter the message content, you can insert fields from the database by typing "/".',
      prompt_menu_title: 'Insert field',
      prompt_title: 'Prompt',
    },
    ai_video: {
      description:
        'AI-generated videos and animated content suitable for advertising, social media and other scenarios',
      name: 'AI Video',
    },
    ai_voice: {
      description: 'AI-generated voice and audio content suitable for voice assistants, podcasts and other scenarios',
      name: 'AI Voice',
    },
    api: {
      description: 'Stores API interface information for interaction and data exchange with external systems',
      name: 'API',
    },
    attachment: {
      adaptive: 'Adaptive',
      close: 'Close',
      copy_link: 'Copy link',
      copy_link_success: 'Copy link successfully',
      delete: 'Delete',
      description:
        'Allows uploading and storing various types of files as record attachments, such as documents, images or compressed packages',
      download: 'Download',
      download_success: 'Download successfully',
      initial_size: 'Initial size',
      name: 'Attachment',
      rotate: 'Rotate',
      zoom_in: 'Zoom in',
      zoom_out: 'Zoom out',
    },
    auto_number: {
      description:
        'Automatically generates a unique sequence number for each new record, suitable for order numbers, work order numbers and other scenarios',
      name: 'Auto Number',
    },
    base: {
      field_description: 'Field description',
      field_name: 'Field name',
      field_type: 'Field type',
      field_type_placeholder: 'Please select column type',
    },
    button: {
      description:
        'Creates interactive clickable buttons that can trigger preset automated operations or events when clicked',
      name: 'Button',
    },
    cascader: {
      description:
        'Provides multi-level linked dropdown menus, suitable for data selection with hierarchical relationships, such as regional selection',
      name: 'Cascader',
    },
    checkbox: {
      description: 'Provides a yes/no option checkbox, suitable for status marking or simple boolean value selection',
      name: 'Checkbox',
    },
    created_by: {
      description:
        'Automatically records the user information of who created the record, convenient for tracking record sources',
      name: 'Created By',
    },
    created_time: {
      description:
        'Automatically generated date and time string when a new record is created, cannot be changed manually',
      name: 'Created Time',
      property: {
        auto_fill: 'Auto fill',
        date_format: 'Date format',
        date_format_placeholder: 'Please select date format',
        show_time: 'Show time',
        time_format: 'Time format',
      },
    },
    currency: {
      description:
        'Specifically for storing and formatting currency amounts, supporting different currency symbols and precision settings',
      name: 'Currency',
      property: {
        accuracy: 'Accuracy',
        alignmen_default: 'Default alignment',
        alignmen_left: 'Left alignment',
        alignmen_right: 'Right alignment',
        alignment: 'Symbol alignment',
        symbol: 'Symbol',
        symbol_placeholder: 'Please enter currency symbol',
      },
    },
    cut_video: {
      description: 'Stores video clips that have been edited, preserving editing information and timeline',
      name: 'Video Editing',
    },
    daterange: {
      description:
        'Stores time periods or date ranges, including start and end time points, suitable for project cycles, event times and other scenarios',
      name: 'Date Range',
      property: {
        date_format: 'Date format',
        date_format_placeholder: 'Please select date format',
        show_time: 'Show time',
        time_format: 'Time format',
      },
    },
    datetime: {
      description:
        'Stores precise date and time information, suitable for scenarios that require recording exact time points',
      name: 'Date Time',
      property: {
        auto_fill: 'Autofill creation time when record created',
        date_format: 'Date format',
        date_format_placeholder: 'Please select date format',
        show_time: 'Show time',
        time_format: 'Time format',
      },
      repeat_day: 'Day',
      repeat_hour: 'Hour',
      repeat_minute: 'Minute',
      repeat_month: 'Month',
      repeat_week: 'Week',
      repeat_year: 'Year',
    },
    email: {
      description:
        'Specifically for storing email addresses, suitable for contact information, notifications and other scenarios',
      name: 'Email',
    },
    formula: {
      description:
        'Automatically calculates values through formulas, can reference other fields and perform mathematical or logical operations',
      name: 'Formula',
      property: {
        expression: 'Formula',
        expression_placeholder: 'Please enter the formula',
      },
    },
    json: {
      description: 'Stores structured JSON format data, suitable for complex data structures or API response content',
      name: 'JSON',
    },
    link: {
      description:
        'Creates bidirectional associations with other databases, enabling inter-database data references and relationship maintenance',
      name: 'Link',
      property: {
        relation_database: 'Related Databse',
      },
    },
    long_text: {
      description: 'Used for storing long text content, such as detailed descriptions, comments or article body text',
      name: 'Multi-line Text',
    },
    lookup: {
      description:
        'Automatically looks up and displays values of specific fields from linked databases, enabling dynamic data references',
      name: 'Lookup',
      property: {
        error: 'Database not found',
        lookup_field: 'Lookup field',
        select_link_database: 'Select linked database',
      },
    },
    member: {
      description: 'Stores system member information, can select single or multiple members as field values',
      name: 'Member',
      property: {
        allow_multiple: 'Allow adding multiple members',
        notify_mentioned: 'Notify members once they are selected',
      },
    },
    modified_by: {
      description: 'Automatically records the user information of who last modified the record',
      name: 'Modified By',
    },
    modified_time: {
      description: 'Automatically generated date and time string when a record is updated, cannot be changed manually',
      name: 'Modified Time',
      property: {
        auto_fill: 'Auto fill',
        date_format: 'Date format',
        date_format_placeholder: 'Please select date format',
        show_time: 'Show time',
        time_format: 'Time format',
      },
    },
    multi_select: {
      description: 'Selects multiple options from a predefined list of options, suitable for multi-tag classification',
      name: 'Multi Select',
      property: {
        add_options: 'Add an option',
        default_value: 'Default value',
      },
    },
    number: {
      description: 'Stores numeric data, supports integers and decimals, can set precision and format',
      name: 'Number',
      property: {
        custom_units: 'Custom units',
        custom_units_default: 'Please enter unit name',
        precision: 'Precision',
        thousand_separator: 'Thousand separator',
      },
    },
    one_way_link: {
      description:
        'Creates a one-way association with other databases, can only view data from the linked database from the current database',
      name: 'One Way Link',
    },
    percent: {
      description:
        'Stores percentage values, automatically formatted as percentages, suitable for progress, ratios and other scenarios',
      name: 'Percentage',
      property: {
        default_value: 'Default value',
        precision: 'Precision',
      },
    },
    phone: {
      description:
        'Specifically for storing phone numbers, suitable for contact information, customer profiles and other scenarios',
      name: 'Phone',
    },
    photo: {
      description: 'Stores and displays image files, supports preview and thumbnail functionality',
      name: 'Photo',
    },
    rating: {
      description:
        'Stores rating information in the form of stars or numerical values, visually displaying evaluation levels',
      name: 'Rating',
      property: {
        icon_settings: 'Icon settings',
        max_value: 'Maximum value',
      },
    },
    single_select: {
      description:
        'Selects a single option from a predefined list of options, suitable for status or classification scenarios',
      name: 'Single Select',
      property: {
        add_options: 'Add an option',
        default_value: 'Default value',
      },
    },
    single_text: {
      description: 'Stores brief single-line text, suitable for titles, names and other concise information',
      name: 'Single Line Text',
    },
    url: {
      description: 'Stores web link addresses, supports direct link jumping for access',
      name: 'URL',
    },
    video: {
      description: 'Stores video files, supports uploading, previewing and playback functionality',
      name: 'Video',
    },
    voice: {
      description: 'Stores audio files, supports recording, uploading and playback functionality',
      name: 'Voice',
    },
    work_doc: {
      description:
        'Stores rich text documents that support Markdown format, allowing direct creation and editing of document content within cells',
      name: 'Document',
    },
  },
  database_views: {
    form: {
      description:
        'The form view allows users to create custom forms for easy data entry and collection. Users can share form links to collect data from external users, automatically adding the collected data to the system. Suitable for scenarios where simplifying data entry and collection processes is essential',
      name: 'Form',
    },
    gallery: {
      description:
        'The gallery view displays records in a card format, using images from record attachments as covers. It is suitable for scenarios such as business cards, materials, and menus',
      name: 'Gallery',
    },
    gantt: {
      description:
        'The Gantt view displays project progress on a timeline, allowing users to visually see the start and end times of tasks, as well as the dependencies between tasks. Ideal for scenarios where effective planning and management of project timelines are crucial',
      name: 'Gantt',
    },
    kanban: {
      description:
        'The kanban view displays data in card format, with each column representing a status or category. Users can drag and drop cards between columns to reflect the progress of tasks or projects. Ideal for scenarios where visual tracking of workflows and task progress is needed',
      name: 'Kanban',
    },
    table: {
      description:
        'The grid view offers a spreadsheet-like layout where users can view and manage data in a structured manner. Each column represents a field, and each row represents a record, allowing for quick browsing, filtering, and sorting of data. Suitable for scenarios requiring clear and organized management of large amounts of data',
      name: 'Grid',
    },
  },
  delete: {
    confirm_deletion: 'Confirm Deletion',
    confirm_to_delete_content: 'Are you sure you want to delete {name} ?',
    confirm_to_delete_this_link: 'Confirm to delete this link?',
    confirm_to_delete_title: 'Confirm if delete',
    delete: 'Delete',
    delete_success: 'Deletion Successful',
  },
  document: {
    code_placeholder: 'Enter code here...',
    list_placeholder: 'Enter list item here...',
    status_connected: 'Connected',
    status_connecting: 'Connecting',
    status_disconnected: 'Disconnected',
    text_placeholder: 'Enter "/" to insert',
    title_placeholder: 'Enter title here...',
  },
  editor: {
    add_button_text: 'Add',
    add_column_as_sort_condition:
      'Add a reference field or any field in the table where the reference field is used as a sort condition',
    add_condition: 'Add condition',
    add_filter_condition: 'Set filter condition',
    add_skill: 'Add',
    add_sort_condition: 'Set sorting condition',
    aggegate_records: 'Aggregate reference data',
    all_count: 'All Count',
    all_record: 'All',
    and_cal: 'AND operation',
    and_condition: 'And "{field}" {condition} "{value}"',
    approval: 'approval',
    average: 'Average',
    button_refresh: 'Refresh',
    by_filter_and_sort: 'Filter and sort referenced fields',
    card_style: 'Card style',
    collapse: 'Collapse',
    collapse_all_group: 'Collapse all groups',
    collapse_group: 'Collapse Group',
    column_required: '{name} is a required field',
    concat_as_text: 'Concatenate as text',
    concat_by_semicon: 'Concatenated by semicolon',
    condition: 'When "{field}" {condition} "{value}"',
    content_paste_failure: 'Paste failed',
    content_paste_successfully: 'Paste successful',
    count_sort_and_filter_confition:
      '{filterConditionCount} filter conditions and {sortConditionCount} sort conditions',
    create_mission_line_text: 'Create mission',
    create_record_line_text: 'Create',
    current_data_not_allowed: 'Data cannot be written to this cell',
    custom_skill: 'Custom Skill',
    data_import_process: '{Percent} data is being uploaded, data cannot be updated temporarily',
    delete_n_records_in_list: 'Delete {count} selected records from the list',
    delete_record_warning_content: 'Are you sure you want to delete?',
    enable: 'Enable',
    excel_import_success: 'Successfully imported {Count} records',
    expand_all_group: 'Expand all groups',
    expand_group_or_sub_group: 'Expand Group/Subgroup',
    filter_no_nil: 'Filter all nil values',
    find_records_line_text: 'Find',
    first_record: 'First Record',
    grid_row_height: 'Row height',
    grid_row_height_default: 'Default',
    grid_row_height_extra_large: 'Extra Tall',
    grid_row_height_large: 'Tall',
    grid_row_height_medium: 'Medium',
    grid_row_height_seeting: 'Height setting',
    integration_line_text: 'Integration',
    item_not_supported_currently: 'This option is not supported currently',
    lookup_count: 'Lookup Count',
    lookup_original_values: 'Quote data from the associated database as is',
    maximal_value: 'Maximum Value',
    microphone_disabled:
      'Permission to use microphone was denied. Update your browser permission settings and try again.',
    minimal_value: 'Minimum Value',
    no_search_result: 'No matching search results',
    not_nil_count: 'Non-Empty Value Count',
    not_null_count: 'Not Null Count',
    number_value_format: 'Number Value Format',
    operate_successfully: 'Operation Successful',
    or_calulate: 'OR operation',
    or_condition: 'Or "{field}" {condition} "{value}"',
    original_values: 'Original Values',
    please_add_link_field: 'Please add the associated field first (LINK, ONEWAY_LINK)',
    please_input_data: 'Please enter data',
    please_select_a_skillset: 'Please select a skillset',
    please_select_at_least_one_skill: 'Please select at least one skill',
    please_select_configuration: 'Please select a configuration',
    record_filter_out_tips: 'Filtered records will not be referenced',
    remove_dulplicated: 'Remove duplicates',
    select_skill: 'Please add skills to the AI Agent',
    show_cover: 'Show Cover',
    show_field_name: 'Show field name',
    show_field_name_help_text: 'Show the field name on the card. If disabled, only the field value will be displayed',
    show_logo: 'Show Logo',
    show_time_zone_info: 'Show Time Zone Info',
    skillsets: 'Skillsets',
    start_paste: 'Pasting...',
    stop_microphone: 'Stop',
    sum_value: 'Sum',
    sum_value_tooltip: 'Returns the sum of all the valuesn SUM(1, 3, 5, "", "Apple") => 9 (1+3+5)',
    symbol_align_strategy: 'Symbol Align Strategy',
    table_lock_message: 'Cannot be updated, please try again later',
    text_area_tips: 'Enter for new line, Shift + Enter to finish editing',
    this_field_configuration_missing:
      'There is a configuration error in this field. Please check the formula or the configuration used by the field.',
    this_field_not_allow_edit: 'This field is not allowed to be edited',
    upload_image: 'Upload Image',
    upload_image_banner_size: 'Recommended size: 1440*480',
    use_micro_phone: 'Use Microphone',
    xor_calculate: 'XOR operation',
    you_have_no_saved_change: 'Unsaved Changes',
    you_have_no_saved_change_content: 'Are you sure you want to close the panel? Your changes will not be saved.',
  },
  email: {
    bcc: 'BCC',
    body: 'Content',
    cc: 'CC',
    from_email: 'From Email',
    from_name: 'From Name',
    help_text_reply_to: 'Email address(es) to use in replies to this email.',
    provider: {
      service: 'Bika Email Service',
      smtp: 'Custom SMTP',
      smtp_integration: 'SMTP Integration',
    },
    provider_type: 'Email Provider',
    recipient: 'To',
    reply_to: 'Reply To',
    send_email: 'Send Email',
    smtp_host: 'SMTP Host',
    smtp_password: 'SMTP Password',
    smtp_port: 'SMTP Port',
    smtp_username: 'SMTP Username',
    subject: 'Subject',
  },
  error: {
    back: 'Go Back',
    back_to_home: 'Back to Home',
    error: 'Error',
    error_code: 'Error Code',
    error_description: 'Error Description',
    error_message: 'Error Message',
    export: {
      record_limit: 'The current database record exceeds 50,000 rows and cannot be exported',
    },
    node_server_error: 'The current node has been deleted or does not exist',
    oops: 'Oops!',
    page_error: 'Some errors occurred on the page',
    page_not_found: 'Page Not Found',
    page_not_found_description:
      'Sorry, we cannot find out the page you are looking for. Try to go back to the previous page?',
    screen_not_found: 'Screen Not Found',
    space: {
      back: 'Back to My Space',
      description:
        'Please check that the link is correct and that you have access rights. If you have any questions, please contact the task issuer',
      title: 'You do not have permission to view the content of this link',
    },
  },
  explore: {
    explore: 'Explore',
  },
  filter: {
    and: 'And',
    contains: 'contains...',
    date_after_or_equal: 'is on or after…',
    date_before_or_equal: 'is on or before…',
    date_range: 'Date Range',
    does_not_contains: 'does not contain...',
    equal: 'is...',
    exact_date: 'Exact Date',
    function_date_time_after: 'is after...',
    function_date_time_before: 'is before...',
    is_empty: 'is empty',
    is_not_empty: 'is not empty',
    is_repeat: 'has duplicates',
    name: 'Filter',
    not_equal: 'is not...',
    or: 'Or',
    previous_month: 'Previous Month',
    previous_week: 'Previous Week',
    search: 'Search',
    settings_descritpion: 'The view changes has not been saved, which will only take effect for you',
    settings_name: 'Filter setting',
    some_day_after: 'Number of days from now',
    some_day_before: 'Number of days ago',
    start_end_date: 'Start Time - End Time',
    the_last_month: 'Last 30 Days',
    the_last_week: 'Last 7 Days',
    the_next_month: 'Next 30 Days',
    the_next_week: 'Next 7 Days',
    this_month: 'This Month',
    this_week: 'This Week',
    this_year: 'This Year',
    today: 'Today',
    tomorrow: 'Tomorrow',
    where: 'When',
    yesterday: 'Yesterday',
  },
  formula: {
    abs: {
      description:
        'Description\nReturns the absolute value of a number.\n\nParameter explanation\nvalue: is the number to take the absolute value of.\nAbsolute value: The absolute value of a positive number is itself, and the absolute value of a negative number is the number without the negative sign."',
      example:
        '// value > 0\nFormula: ABS(1.5)\nResult: 1.50\n\n//value = 0\nFormula: ABS(0)\nResult: 0.00\n\n// value < 0\nFormula: ABS(-1.5)\nResult: 1.50"',
      name: 'Abs',
    },
    and: {
      description:
        'Returns true if all arguments are true; otherwise, returns false.\n\n【logical】is a logical argument, which can be a logical value, array, or field reference.',
      example: 'AND(3>2, 4>3)\n=> true',
      name: 'And',
    },
    array: 'Array',
    array_compact: {
      description:
        'Removes empty strings and null values from an array.\n\n【item】Represents array values, such as cell values of multi-select, attachment, link, and lookup field types.\n\nThis function will retain "false" values and strings with blank characters.',
      example: 'ARRAYCOMPACT([1,2,"",3,false," ", null])\n=> [1,2,3,false," "]',
      name: 'Array Compact',
    },
    array_flatten: {
      description:
        'Flattens an array by removing any nested arrays. All data becomes elements of the same array.\n\n【item】Represents array values, such as cell values of multi-select, attachment, link, and lookup field types.',
      example: 'ARRAYFLATTEN([1, 2, " ", 3, ],[false])\n=> [1, 2, 3 ,false]',
      name: 'Array Flatten',
    },
    array_join: {
      description:
        'Concatenates all values in an array into a single string with a delimiter.\n\n【item】Represents array values, such as cell values of multi-select, attachment, link, and lookup field types.',
      example: 'ARRAYJOIN(values, "; ") ',
      name: 'Array Join',
    },
    array_unique: {
      description:
        'Returns only the unique items in an array.\n\n【item】Represents array values, such as cell values of multi-select, attachment, link, and lookup field types.',
      example: 'ARRAYUNIQUE([1,2,3,3,1])\n=> "[1,2,3]"',
      name: 'Array Unique',
    },
    average: {
      description: 'Returns the average of the numbers',
      example: 'AVERAGE(2, 4, "6", "Eight") => (2 + 4 + 6) / 4 = 3',
      name: 'Average',
    },
    blank: {
      description:
        'Returns a blank value.\n\nCan be used to check if a cell is blank, see example one;\nCan be used to fill a cell with a blank value, see example two;',
      example: 'IF(Date = BLANK(), "Please enter date", "Date entered")',
      name: 'Blank',
    },
    ceiling: {
      description:
        'Returns the nearest integer multiple of significance that is greater than or equal to the value. If no significance is provided, a significance of 1 is assumed.',
      example: 'CEILING(1.01, 0.1)\n=> 1.1\n\nCEILING(-1.99, 0.1)\n=> -1.9',
      name: 'Ceiling',
    },
    concatenate: {
      description:
        'Concatenates multiple text values into a single text value (equivalent to &).\n\n【text1..】are the multiple values to concatenate, which can be text, numbers, date parameters, or column references.\n\nEnclose the text values you want to concatenate in double quotes, except for numbers and column references.\nSpecial case: If you want to concatenate double quotes, you need to use a backslash (\\) as an escape character.',
      example: 'CONCATENATE(Name, " - ", Age)',
      name: 'Concatenate',
    },
    count: {
      description:
        'Counts the number of "number" type values.\n\n【number】Can be input parameters or referenced columns.\n\nThis function can count how many numeric values (numbers, currency, percentages, ratings) are in the input parameters or cell.',
      example: 'COUNT(1, 3, 5, "", "seven")\n=> 3',
      name: 'Count',
    },
    count_a: {
      description:
        'Counts the number of non-empty values.\n\n【textOrNumber】Can be input parameters or referenced columns.\n\nThis function can count how many non-empty values are in the input parameters or cell.\nFor example, it can count how many options, pictures, or members are in a cell.\nIt can also count the non-empty values in an array in a lookup cell.',
      example: 'COUNTA(1, 3, 5, "", "seven")\n=> 4',
      name: 'CountA',
    },
    count_all: {
      description:
        'Counts the number of all values, including empty values.\n\n【textOrNumber】Can be input parameters or referenced columns.\n\nThis function can count how many values, including empty values, are in the input parameters or cell.',
      example: 'COUNTALL(1, 3, 5, "", "seven")\n=> 5',
      name: 'CountAll',
    },
    count_if: {
      description:
        'Counts the number of times a keyword appears in values.\n\nvalues: Specifies where to look for data. Supports array or text type data.\nkeyword: The keyword to search for and count.\noperation: Comparison operator, optional. You can enter condition symbols greater than ">", less than "<", equal to "=", not equal to "!=". If not filled, the default is equal to.\nIn example one, no comparison operator is filled, so it counts the number of values equal to "A".\nIn example two, the comparison operator ">" is filled, meaning it counts the number of values greater than "2".\n\nUse cases:\n1) It can count the number of times the character "A" appears in a text array [A, B, C, D, A], as shown in example one.\n2) It can count the number of numbers greater than 3 in a number array [1, 2, 3, 4, 5], as shown in example two.\n3) It can count the number of times "grape" appears in a text string "Eat grapes without spitting out the skins", as shown in example three.',
      example: 'COUNTIF({Rating}, "A")\n=> 2',
      name: 'CountIf',
    },
    created_time: {
      description: 'Returns the creation time of the record.',
      example: 'CREATED_TIME()\n=> "2024-06-10"\n\n"Created on: " & CREATED_TIME()',
      name: 'Created Time',
    },
    date: 'Date',
    date_add: {
      description:
        'Introduction\nAdds a fixed time interval to the specified date.\n\nParameter Description\ndate: is the specified date. This function will add a certain time interval to this date.\ncount: is the time interval, supports input of numbers with positive and negative signs. If it is a positive number, it means adding a few days (custom time unit), see example one; if it is a negative number, it means reducing a few days, see example two;\nunits: is the time unit, i.e., the unit for adding the time interval. For example, calculating by "days" can also be converted to calculating by "years".\n\nTime units include the following symbols, both formats can be used: "Unit Description" → "Abbreviation"\nMilliseconds: "milliseconds" → "ms"\nSeconds: "seconds" → "s"\nMinutes: "minutes" → "m"\nHours: "hours" → "h"\nDays: "days" → "d"\nWeeks: "weeks" → "w"\nMonths: "months" → "M"\nQuarters: "quarters" → "Q"\nYears: "years" → "y"\n\nClick the link below to view all time units.',
      example: 'DATEADD({date of begin}, 10, "days")',
      name: 'Date Add',
    },
    datestr: {
      description:
        'Formats a date as text in the "year-month-day" format (fixed format as YYYY-MM-DD).\n\n【date】The date to be formatted.\n\nAfter formatting, the date becomes a string and no longer has date data properties.',
      example: 'DDATESTR({date of begin})\n=> 2024-06-10',
      name: 'Datestr',
    },
    datetime_diff: {
      description:
        'Returns the difference between two dates (with positive and negative), i.e., date1 minus date2.\n\n【date1】Date 1\n【date2】Date 2\n【units】is the time unit, i.e., the unit for calculating the difference between date1 and date2. For example, calculating by "days" can also be converted to calculating by "years".\n\nTime units include the following symbols, both formats can be used: "Unit Description" → "Abbreviation"\nMilliseconds: "milliseconds" → "ms"\nSeconds: "seconds" → "s"\nMinutes: "minutes" → "m"\nHours: "hours" → "h"\nDays: "days" → "d"\nWeeks: "weeks" → "w"\nMonths: "months" → "M"\nQuarters: "quarters" → "Q"\nYears: "years" → "y"\n\nClick the link below to view all time units.',
      example: 'DATETIME_DIFF( TODAY(), {date of begin},  "days")\n=> 15',
      name: 'Datetime Diff',
    },
    datetime_format: {
      description:
        'Formats a date as text in a custom format.\n\n【date】The date to be formatted.\n【output_specifier】The format specifier. For example:\n"DD-MM-YYYY" means "day-month-year", see example one;\n"YYYY / MM / DD" means "year/month/day", see example two;\n"MM.DD" means "month.day", see example three.\n\nAfter formatting, the date becomes a string.\n\nFor supported date format specifiers, see the link below.',
      example: 'DATETIME_FORMAT(TODAY(), "DD-MM-YYYY")\n=> 10-06-2024',
      name: 'Datetime Format',
    },
    datetime_parse: {
      description:
        'Converts text to a structured date type.\n\n【date】The text to be formatted as a date.\n【input_format】Optional, the date format specifier. For text date content that the system cannot recognize, you can interpret it as a structured date. See example two.\n\nFor supported date format specifiers and locales, see the link below.',
      example: 'DATETIME_PARSE("10 Jan 2024 18:00", "D MMM YYYY HH:mm")\n=> "2024/01/10 06:00"',
      name: 'Datetime Parse',
    },
    day: {
      description:
        'Returns the day of the month for a given date, output as an integer between 1 and 31.\n\n【date】The specified date.\nFor example, the number 1 means the date is the 1st day of the month.',
      example: 'DAY({date of begin})\n=> 8',
      name: 'Day',
    },
    encode_url_component: {
      description:
        'Encodes a text string as a URL component.\n\n【component_string】is the text string to be encoded. The following characters are not encoded: - _ . ~\n\nFor example, copying the output value of the first example into the browser address bar is equivalent to searching for "apple" on Google.',
      example: 'ENCODE_URL_COMPONENT({Search Phrase})',
      name: 'Encode URL Component',
    },
    error: {
      description:
        'Displays an error message and reason in the cell.\n\nYou can input a text explanation of the error reason within the function, such as "Statistical error" in the example.',
      example: 'IF({age}< 0, ERROR("alien"), "normal")',
      name: 'Error',
    },
    even: {
      description:
        'Returns the nearest even number in the direction of increasing absolute value.\n\n【value】is the number to round to even.\n【Increasing absolute value】means it returns a value that is further from 0 (zero)."',
      example: 'EVEN(1.5)\n=> 2\n\nEVEN(-1.8)\n=> -2"',
      name: 'Even',
    },
    example: 'Example',
    exp: {
      description:
        'Returns e raised to the power of a specified number.\n\n【e】is the natural number, approximately 2.718282.\n【power】is the exponent, that is, the power to which e is raised.',
      example: 'EXP(1)\n=> 2.72\n\nEXP(2)\n=> 7.40',
      name: 'Exp',
    },
    false: {
      description:
        'Returns the logical value false.\n\nCan determine whether a checkbox field is "unchecked", as shown in Example 1;\n\nCan be used with TRUE() to output boolean values of true and false, as shown in Example 2;',
      example: 'IF({average score} > 60, TRUE(), FALSE())',
      name: 'False',
    },
    features_list: 'Formulas List',
    find: {
      description:
        'Finds the position of a specific text within content for the first time.\n\n【stringToFind】is the specific text to find.\n【whereToSearch】specifies the content to search within. You can input text parameters or reference fields.\n【startFromPosition】optional, specifies the position to start searching from (using a number to indicate the character position).\n\nThis function can quickly find the position of specific text within a large content.\nIf it returns the number 3, it means the text appears at the 3rd character of the content.\nIf no matching text is found, the result will be 0.\n\nIt is similar to SEARCH(), but when no match is found, SEARCH() returns an empty value instead of 0.',
      example: 'FIND("apple", "This is an apple")\n=> 12',
      name: 'Find',
    },
    floor: {
      description:
        'Returns the nearest integer multiple of significance that is less than or equal to the value. If no significance is provided, a significance of 1 is assumed.',
      example: 'FLOOR(1.01, 0.1)\n=> 1.0\n\nFLOOR(-1.99, 0.1)\n=> -2.0',
      name: 'Floor',
    },
    from_now: {
      description:
        'Returns the difference between the current date and the specified date (absolute value).\n\n【date】is the specified date, i.e., the specified date minus the current date, calculating the number of days (custom time unit) between the two dates, absolute value.\n【units】is the time unit, i.e., the unit for calculating the difference between the specified date and the current date, such as calculating by "days" or converting to "years".\n\nTime units include the following symbols, both formats can be used:\n"Unit Description" → "Abbreviation"\nMilliseconds: "milliseconds" → "ms"\nSeconds: "seconds" → "s"\nMinutes: "minutes" → "m"\nHours: "hours" → "h"\nDays: "days" → "d"\nWeeks: "weeks" → "w"\nMonths: "months" → "M"\nQuarters: "quarters" → "Q"\nYears: "years" → "y"\nClick the link below to view all time units.',
      example: 'FRONOW("2023-08-10", "y")\n=> 1\n\nFROMNOW({Start Date}, "days")\n=> 25',
      name: 'From Now',
    },
    hour: {
      description:
        'Returns the hour of the day for a given date, output as an integer between 0 (12:00 am) and 23 (11:00 pm).\n\n【date】The specified date.\nFor example, 18 means 18:00.',
      example: 'HOUR({date of begin})\n=> 9',
      name: 'Hour',
    },
    if: {
      description:
        'Checks whether a condition is met, returns one value if true and another value if false.\n\n【logical】is the logical condition, an expression that evaluates to true or false.\n【value1】is the value returned if the logical condition is true.\n【value2】is the value returned if the logical condition is false.\n\nIF supports nested usage and can be used to check if a cell is blank/empty.',
      example:
        'IF(Score > 60, "Pass", "Fail")\n\nIF(WaterTemp > 100, IF(WaterTemp < 212, "just right", "too hot"), "too cold")\n\nIF(Date = BLANK(), "Please enter date", "Date entered")',
      name: 'If',
    },
    input_formula: 'Input formula',
    int: {
      description:
        'Rounds a number down to the nearest integer.\n\n【value】is the value to round down.\n【Rounds down】means it returns a value that is less than or equal to the original number."',
      example: 'INT(1.99)\n=> 1\n\nINT(-1.99)\n=> -2"',
      name: 'Int',
    },
    is_after: {
      description:
        'Compares if date1 is later than date2. Returns true if it is, otherwise false.\n\n【date1】Date 1.\n【date2】Date 2.\n\nDates can be input parameters, as in example one;\nDates can also be referenced date fields, as in example two.\n\nIn cells, true and false are represented as "checked" and "unchecked".',
      example: 'IS_AFTER({deadline}, TODAY())\n=> 0',
      name: 'Is After',
    },
    is_before: {
      description:
        'Compares if date1 is earlier than date2. Returns true if it is, otherwise false.\n\n【date1】Date 1.\n【date2】Date 2.\n\nDates can be input parameters, as in example one;\nDates can also be referenced date fields, as in example two.\nIn cells, true and false are represented as "checked" and "unchecked".',
      example: 'IS_BEFORE({deadline}, TODAY())\n=> TRUE',
      name: 'Is Before',
    },
    is_error: {
      description:
        'Checks if a formula results in an error, returns true if it does.\n\n【expr】is the value to be checked. The value can be a formula of types such as arithmetic operations, logical judgments, etc.',
      example: 'IS_ERROR(2/0)',
      name: 'Is Error',
    },
    is_same: {
      description:
        'Determines if date1 is equal to date2. Returns true if it is, otherwise false.\n\n【date1】Date 1.\n【date2】Date 2.\n【units】Optional, the unit of time to compare. For example, to compare if two dates are equal up to the minute.\n\nDates can be input parameters, as in example one;\nDates can also be referenced date fields, as in example four.\nIn cells, true and false are represented as "checked" and "unchecked".\n\nClick the link below to see all time units.',
      example: 'IIS_SAME({Date 1}, {Date 2}, "hour")\n=> 0',
      name: 'Is Same',
    },
    last_modified_time: {
      description:
        'Returns the last modified time of each cell in a row.\nNote: The system only returns the modification time for cells in computed columns.\n\nIf you are only interested in the update time of specific fields, you can specify one or more columns, as shown in examples two and three.',
      example:
        'LAST_MODIFIED_TIME()\n=> "2024-06-10 6:27 p.m."\n\nLAST_MODIFIED_TIME({date of begin})\n=> "2024-06-09 1:27 p.m."',
      name: 'Last Modified Time',
    },
    left: {
      description:
        'Extracts a given number of characters from the start of a text string.\n\n【string】is the text string from which characters are extracted.\n【howMany】is the number of characters to extract, represented as a number. For example, "4" means extracting 4 characters from left to right.',
      example: 'LEFT({date of birth}, 4)\n=> 1994',
      name: 'Left',
    },
    len: {
      description:
        'Counts the number of characters in a text.\n\n【string】is the text to calculate the length of; punctuation marks, spaces, etc. also count as one character.',
      example: 'LEN("apple")\n=> 5',
      name: 'Len',
    },
    log: {
      description:
        'Returns the logarithm of a number with a specified base.\n\n【number】is the number to calculate the logarithm of.\n【base】is the base of the logarithm. If not specified, the default base is 10.',
      example: 'LOG(1024, 2)\n=> 10\n\nLOG(10000)\n=> 4',
      name: 'Log',
    },
    logic: 'Logic',
    lower: {
      description:
        'Converts all uppercase characters in a text string to lowercase.\n\n【string】is the text string to be converted.',
      example: 'LOWER("Hello!")\n=> hello!',
      name: 'Lower',
    },
    max: {
      description: 'Returns the largest of the given numbers.',
      example: 'MAX(1, 3, 5, 7) => 7',
      name: 'Max',
    },
    mid: {
      description:
        'Extracts a fixed-length text from a specific position within content.\n\n【string】is the content you input, which contains the text to be extracted. The content can be input text or referenced field data.\n【whereToSearch】is the position you specify to extract the text from, using a number to indicate the character position. For example, the number "3" means to extract from the 3rd character of the content.\n【count】is the length of the text to extract, using a number to indicate. For example, the number "2" means to extract 2 characters from the specified position.',
      example: 'MID("This is an apple", 12, 5)\n=> apple',
      name: 'Mid',
    },
    min: {
      description: 'Returns the minimum value among the numbers',
      example: 'MIN(1, 3, 5, 7) => 1',
      name: 'Min',
    },
    minute: {
      description:
        'Returns the minute of the hour for a given date, output as an integer between 0 and 59.\n\n【date】The specified date.',
      example: 'MINUTE({date of begin})\n=>30',
      name: 'Minute',
    },
    mod: {
      description:
        'Returns the remainder of a division between two numbers.\n\n【value】is the dividend.\n【divisor】is the divisor.\n\nThe sign of the result is the same as the sign of the divisor.',
      example: 'MOD(10, 3) => 1',
      name: 'Mod',
    },
    month: {
      description:
        'Returns the month corresponding to the specified date.\n\n【date】The specified date.\n\nThe output value of this function is an integer between 1 (January) and 12 (December).',
      example: 'MONTH({date of begin})\n=> 6',
      name: 'Month',
    },
    not: {
      description:
        'Reverses the logical value of its argument.\n\n【boolean】is the boolean parameter, meaning your input value must be a logical judgment with only true and false outputs, such as comparing which of two values is greater.\nWhen the logical judgment of your parameter is true, the function returns false;\nWhen the logical judgment of your parameter is false, the function returns true;\n\nFor example one: 2>3 outputs false, but after reversal, the function outputs true.\nFor example two: NOT({Age} > 18) after the NOT function reversal, it is equivalent to judging {Age} ≤ 18',
      example: 'NOT({Age} > 18)',
      name: 'Not',
    },
    now: {
      description:
        "Returns today's date and time, accurate to the second.\n\nYou can directly use this function to return the year, month, and day, see example one;\n\nYou can also use it with functions like DATEADD or DATETIME_DIFF, such as subtracting the current time from {Deadline} to display the project's countdown, see example two.\n\nNote: The result returned by this function will only update when the formula is recalculated or the database is refreshed.",
      example: 'NOW()\n=> "2024/06/02 07:12"',
      name: 'Now',
    },
    number: 'Number',
    object: 'Object',
    odd: {
      description:
        'Returns the nearest odd number in the direction of increasing absolute value.\n\n【value】is the number to round to odd.\n【Increasing absolute value】means it returns a value that is further from 0 (zero)."',
      example: 'ODD(1.5)\n=> 3\n\nODD(-2.1)\n=> -3"',
      name: 'Odd',
    },
    or: {
      description:
        'Returns true if any argument is true; otherwise, returns false.\n\n【logical】is a logical argument, which can be a logical value, array, or field reference.',
      example: 'OR(3>2, 2>3)\n=>  true',
      name: 'Or',
    },
    power: {
      description:
        'Returns the power of a specified base. That is, the base raised to the power of the exponent.\n\n【base】is the base number.\n【power】is the exponent.',
      example: 'POWER(2, 5)\n=> 32\n\nPOWER(-5, 3)\n=> -125',
      name: 'Power',
    },
    record_id: {
      description: 'Returns the ID of the record',
      example: '"https://awesomeservice.com/view?recordId=" & RECORD_ID()',
      name: 'Record ID',
    },
    replace: {
      description:
        'Replaces a segment of text at a specific position within content with new text.\n\n【string】is the content you input, which contains the text to be replaced. The content can be input text or referenced field data.\n【start_character】is the position you specify to start replacing the text, using a number to indicate. For example, the number "3" means to start replacing from the 3rd character of the content.\n【number_of_characters】is the number of characters you specify to replace, using a number to indicate. For example, the number "2" means to replace 2 characters from the specified position.\n【replacement】is the new text to replace the original text.\n\n(If you want to replace all occurrences of the original text within the content with new text, please refer to SUBSTITUTE.)',
      example: 'REPLACE("This is an apple", 12, 8, "elephant")\n=> This is an elephant',
      name: 'Replace',
    },
    rept: {
      description:
        'Repeats a text string a given number of times.\n\n【string】is the text string to be repeated.\n【number】is the number of times to repeat the text string, represented as a number. For example, "2" means repeating the text string 2 times.',
      example: 'RREPT("Hi! ", 3)\n\n=> Hi! Hi! Hi!',
      name: 'Rept',
    },
    right: {
      description:
        'Extracts a given number of characters from the end of a text string.\n\n【string】is the text string from which characters are extracted.\n【howMany】is the number of characters to extract, represented as a number. For example, "5" means extracting 5 characters from right to left.',
      example: 'RIGHT({date of birth}, 5)\n=> 07-13',
      name: 'Right',
    },
    round: {
      description:
        'Rounds the value to the number of decimal places given by "precision." (Specifically, ROUND will round to the nearest integer at the specified precision, with ties broken by rounding half up toward positive infinity.)',
      example: 'ROUND(1.99, 0)\n=> 2\n\nROUND(18.8, -1)\n=> 20',
      name: 'Round',
    },
    rounddown: {
      description:
        'Rounds the value to the number of decimal places given by "precision," always rounding down, i.e., toward zero. (You must give a value for the precision or the function will not work.)',
      example: 'ROUNDDOWN(1.9, 0)\n=> 1\n\nROUNDDOWN(-1.9, 0)\n=> -1',
      name: 'Round Down',
    },
    roundup: {
      description:
        'Rounds the value to the number of decimal places given by "precision," always rounding up, i.e., away from zero. (You must give a value for the precision or the function will not work.)',
      example: 'ROUNDUP(1.1, 0)\n=> 2\n\nROUNDUP(-1.1, 0)\n=> -2',
      name: 'Round Up',
    },
    search: {
      description:
        'Searches for the position of specific text within content for the first time.\n\n【stringToFind】is the specific text to search for.\n【whereToSearch】specifies the content to search within. You can input text parameters or reference fields.\n【startFromPosition】optional, specifies the position to start searching from (using a number to indicate the character position).\n\nThis function can quickly search for the position of specific text within a large content.\nIf it returns the number 3, it means the text appears at the 3rd character of the content.\nIf no matching text is found, the result will be empty.\n\nIt is similar to FIND(), but when no match is found, FIND() returns 0 instead of an empty value.',
      example: 'SSEARCH("apple", "This is an apple")\n=> 12',
      name: 'Search',
    },
    second: {
      description:
        'Returns the second of the minute for a given date, output as an integer between 0 and 59.\n\n【date】The specified date.',
      example: 'SECOND({date of begin})\n=> 1',
      name: 'Second',
    },
    select_a_formula: 'Select a field or function',
    set_locale: {
      description:
        'Sets a specific locale for the given date and time.\n\n【date】The specified date.\n【locale_modifier】The locale specifier.\n\nThis function must be used with DATETIME_FORMAT. Click the link below to see supported locale specifiers.',
      example: 'DATETIME_FORMAT(SET_LOCALE(NOW(), "en"), "lll")\n=> June 2, 2024 11:04 AM',
      name: 'Set Locale',
    },
    set_timezone: {
      description:
        'Sets a specific timezone for the given date.\n\n【date】The specified date.\n【tz_identifier】The timezone specifier. For example, "8" represents UTC+8, "-2" represents UTC-2.\n\nThis function must be used with DATETIME_FORMAT.',
      example: 'DATETIME_FORMAT(SET_TIMEZONE(NOW(), -8), "M/D/YYYY h:mm")\n=> 9/20/2024 2:30',
      name: 'Set Timezone',
    },
    sqrt: {
      description:
        'Returns the square root of a number.\n\n【value】is the number to find the square root of.\n\nIf the number is negative, SQRT returns NaN.',
      example: 'SQRT(16) => 4',
      name: 'Sqrt',
    },
    substitute: {
      description:
        'Replaces occurrences of a specified text within content with new text.\n\n【string】is the content you input, which contains the text to be replaced. The content can be input text or referenced field data.\n【old_text】is the text you want to replace.\n【new_text】is the new text to replace the old text.\n【instance_num】optional, specifies which occurrence of the old text to replace. If omitted, all occurrences are replaced.',
      example:
        'SUBSTITUTE("gold mold", "old", "et")\n=> get met\n\nSUBSTITUTE("gold mold", "old", "et", 1)\n=> get mold',
      name: 'Substitute',
    },
    sum: {
      description: 'Sum together the numbers. Equivalent to number1 + number2 + ...',
      example: 'SUM(1, 3, 5, "", "VI") => 1 + 3 + 5 = 9',
      name: 'Sum',
    },
    switch: {
      description:
        'This function is a multi-branch selection function. It consists of an expression and multiple branches with return values. If the expression equals a branch value, the function outputs the corresponding return value.\n\n【expression】is the expression whose result will be matched against each branch.\n【pattern】is the branch, each representing a possible result of the expression. Each branch has a corresponding return value.\n【result】is the return value. If the result of the expression matches a branch, the corresponding return value is output.\n【default】is the default value. If the result does not match any branch, the function outputs the default value. If the default value is not provided, it returns an empty value.\n\nFor example, in the first example, {countries} references a column of data, and its output value could be thousands of country names. It is the expression in this function. "China" and "Chinese" are a branch and return value, respectively, indicating that if the output value of {countries} is "China", it returns "Chinese". "General English" is the default value, indicating that if the output value of {countries} does not match any branch, it returns "General English".',
      example: 'SWITCH(1, 1, "one", 2, "two", "many")\n\n=>one',
      name: 'Switch',
    },
    t: {
      description:
        'Returns the text if the input value is text; otherwise, returns an empty value.\n\n【value】is the value to be checked if it is text.\n\nFor example, if the input value references a field of type number or date, it will return an empty value.',
      example: 'T({name})\n=> kelly\n\nT(3.2)\n=> BLANK',
      name: 'T',
    },
    text: 'Text',
    timestr: {
      description:
        'Formats a date as text in the "hour:minute:second" format (fixed format as HH:mm:ss).\n\n【date】The date to be formatted.\n\nAfter formatting, the date becomes a string and no longer has date data properties.',
      example: 'TIMESTR(NOW())\n=> "04:52:12"',
      name: 'Timestr',
    },
    to_now: {
      description:
        'Returns the difference between the current date and the specified date (absolute value).\n\n【date】is the specified date, i.e., the specified date minus the current date, calculating the number of days (custom time unit) between the two dates, absolute value.\n【units】is the time unit, i.e., the unit for calculating the difference between the specified date and the current date, such as calculating by "days" or converting to "years".\n\nTime units include the following symbols, both formats can be used:\n"Unit Description" → "Abbreviation"\nMilliseconds: "milliseconds" → "ms"\nSeconds: "seconds" → "s"\nMinutes: "minutes" → "m"\nHours: "hours" → "h"\nDays: "days" → "d"\nWeeks: "weeks" → "w"\nMonths: "months" → "M"\nQuarters: "quarters" → "Q"\nYears: "years" → "y"\nClick the link below to view all time units.',
      example: 'TONOW("2023-08-10", "y")\n=> 1\n\nTONOW({Start Date}, "days")\n=> 25',
      name: 'To Now',
    },
    today: {
      description:
        "Returns today's date (year, month, day), but does not include hours, minutes, and seconds (default is 00:00:00). If you want to include hours, minutes, and seconds, use the NOW function.\n\nYou can directly use this function to return the year, month, and day, see example one;\nYou can also use it with functions like DATEADD or DATETIME_DIFF, such as subtracting the current time from {Deadline} to display the project's countdown, see example two.\n\nNote: The result returned by this function will only update when the formula is recalculated or the database is refreshed.",
      example: 'TODAY() => 2024/06/02',
      name: 'Today',
    },
    trim: {
      description:
        'Removes spaces from the start and end of a text string.\n\n【value】is the text string to be processed.',
      example: 'TRIM(" Hello! ")\n\n=> Hello!',
      name: 'Trim',
    },
    true: {
      description:
        '【Introduction】\nReturns the logical value true.\n\n【Parameter Description】\nThis function does not require any parameters.\nThis function can determine whether a checkbox field is "checked", as shown in Example 1;\nThis function can be used with FALSE() to output boolean values of true and false, as shown in Example 2.',
      example: 'IF({average score} > 60, TRUE(), FALSE())',
      name: 'True',
    },
    upper: {
      description:
        'Converts all lowercase characters in a text string to uppercase.\n\n【string】is the text string to be converted.',
      example: 'UPPER("Hello!")\n=> HELLO!',
      name: 'Upper',
    },
    usage: 'Usage',
    value: {
      description:
        'Converts a text string to a number.\n\n【text】is the text value to convert.\n\nThis function can extract numbers from within a text string.',
      example: 'VALUE("$10000")\n=> 10000',
      name: 'Value',
    },
    weekday: {
      description:
        'Returns the day of the week corresponding to the specified date.\n\n【date】The specified date.\n【startDayOfWeek】Optional, the start day of the week. By default, each week starts on Sunday (i.e., Sunday is 0). You can also set the start day to "Monday" (Monday, see example two).\n\nThe output value of this function is an integer between 0 and 6.',
      example: 'WEEKDAY(TODAY())',
      name: 'Weekday',
    },
    weeknum: {
      description:
        'Returns the week number of the year for a given date.\n\n【date】The specified date.\n【startDayOfWeek】Optional, the start day of the week. By default, each week starts on Sunday (i.e., Sunday is 0). You can also set the start day to "Monday".\n\nThe output of this function is an integer. For example, 6 means the date falls in the 6th week of the year.',
      example: 'WWEEKNUM(TODAY(), "Monday")',
      name: 'Weeknum',
    },
    workday: {
      description:
        'Returns the date after a specified number of working days from the start date.\n\n【startDate】is the specified start date.\n【numDays】is the number of working days after the start date, represented by a positive number. For example, the number "1" represents the date one working day after the start date, see example one;\n【holidays】optional. These are specific dates to be excluded from the calendar, such as holidays. The input format is "yyyy-mm-dd", with multiple dates separated by commas, see example three.\n\nThis function does not include weekends and the specific dates you specify.',
      example:
        'WORKDAY_DIFF({Product begin date}, {Product launch date} , "2024-06-25, 2024-06-26, 2024-06-27")\n=> 100',
      name: 'Workday',
    },
    workday_diff: {
      description:
        'Counts the number of workdays between two dates (can be positive or negative).\n\n【startDate】Start date.\n【endDate】End date. If the start date is later than the end date, the result will be negative.\n【holidays】Optional. Dates to be excluded from the work calendar, such as holidays. The input format is "yyyy-mm-dd", with multiple dates separated by commas.\n\nThis function counts the workdays between the start and end dates, excluding weekends and specified dates.',
      example:
        'WORKDAY_DIFF({Product begin date}, {Product launch date} , "2024-06-25, 2024-06-26, 2024-06-27")\n=> 100',
      name: 'Workday Diff',
    },
    xor: {
      description:
        'Returns true if an odd number of arguments are true, otherwise returns false.\n\n【logical】is the logical parameter, which can be a logical value, array, or referenced field.',
      example: 'XOR(3>2, 2>3, 4>3)\n=> false',
      name: 'Xor',
    },
    year: {
      description: 'Returns the four-digit year corresponding to the specified date.\n\n【date】The specified date.',
      example: 'YEAR({date of begin})\n=> 2024',
      name: 'Year',
    },
  },
  global: {
    action: {
      cannot_be_empty: 'Cannot be empty',
      detail: 'Detail',
      full_screen: 'Full screen',
      no_result_found: 'No search records',
      preview: 'Preview',
      select: 'Select',
      toggle: 'Toggle',
      un_named: 'Unnamed',
      zoom_in: 'Zoom in',
      zoom_out: 'Zoom out',
    },
    copilot: {
      delete_history: 'Delete conversation',
      delete_history_confirm: 'Are you sure you want to delete this conversation history?',
      history: 'Conversation history',
      history_empty: 'No conversation history',
      history_loading: 'Loading...',
      history_no_description: 'No description',
      history_no_more: 'No more',
      history_no_title: 'No title',
      new_chat: 'New chat',
      title: 'Copilot',
      upload_file: 'Upload file',
      node_resource: 'Node resource',
      welcome: 'Hi, I am your Copilot, ready to help you anytime!',
    },
    dl_link_unavailable: 'Download link unavailable',
    download: 'Download',
    error_description:
      'Our technical team has been notified and is working to resolve the issue. Please try the following actions:',
    error_reason: [
      'Go back to the previous page and refresh, then try again.',
      'Leave temporarily and try again later.',
      'If the issue persists, please contact customer support for further assistance.',
    ],
    guest: 'Guest',
    guest_management: 'Guest',
    hooks: {
      firebase: {
        create_hardware_description: 'Hardware Device Integration Binding',
        create_hardware_name: 'Hardware Device Binding',
      },
    },
    me: 'Me (current visitor)',
    page_not_found: 'Sorry, the page you visited was not found',
    page_not_found_description: 'We couldn’t find the page you requested. It might be due to the following reasons:',
    page_not_found_reason: [
      'The URL you entered is incorrect or misspelled',
      'The requested page has been deleted or moved',
      'Our server is temporarily unable to find the requested resource',
    ],
    retry: 'Retry',
    select: 'Please select',
    select_path: 'Please select the full path',
    server_error: 'Sorry, the server encountered an issue while processing your request',
    toast: {
      description_update_success: 'Description updated successfully',
      open_in_database: 'Please open the database page',
    },
    welcome: 'Welcome to {name}',
  },
  grid: {
    asc_option: 'Ascending Order by Option',
    batch_update_selected_record: 'Bulk Update  {recordCount} records',
    bulk_update: 'Bulk update',
    bulk_update_confirm_content: '{count} records will be modified. Are you sure you want to save?',
    bulk_update_confirm_title: 'Confirm bulk update',
    bulk_update_fields: 'Fields to be updated',
    bulk_update_successful: 'Bulk Update Records Successful',
    bulk_update_title: 'Bulk update records',
    copy_row: 'Copy Row',
    created_doc: 'Create Document',
    delete_n_record: 'Delete {count} Records',
    desc_option: 'Descending Order by Option',
    duplicate_record: 'Duplicate Record',
    edit_record: 'Edit Record',
    filter_to_find_all_records: 'Filter to find all records in the database that need batch editing',
    group: 'Group',
    lookup_unamed_record: 'Unnamed Record',
    new_record: 'New record',
    pasting_multiple_columns_is_not_supportted_currently: 'Pasting multiple columns is not currently supported',
    record_selected: 'Selected {count} records',
    row_group: 'Group',
    see_more_detail: 'More',
    select_record_by_name: 'Select {name}',
    tooltip_new_record: 'Click to create a new record',
    un_named_doc: 'Unnamed Document',
  },
  help: {
    description: 'Want to know more or still need help?',
    help: 'Help',
    help_and_support: 'Help & Support',
    help_center: 'Help center',
    title: 'What can we help you with?',
  },
  integration: {
    advertise: {
      can_do: 'Below are some inspiring use case examples for your reference:',
      connect_to: 'Connect to {name}',
      notice:
        'Note: This app will be available to all users in your account. By installing this app you agree to its Terms of Service.',
    },
    airtable: {
      airtable_token: 'Airtable API Token',
      airtable_token_placeholder: 'xxxxxxxxxxxxxxxxxxxxxxxx',
      description:
        'Airtable integration allows users to directly synchronize Airtable form data into the system. With automation, relevant actions can be automatically triggered when form data is updated, such as updating databases, sending notifications, or generating reports. Suitable for business scenarios requiring real-time data synchronization and management.',
      title: 'Airtable',
    },
    aitable: {
      aitable_token: 'AITable.ai API Token',
      aitable_token_placeholder: 'xxxxxxxxxxxxxxxxxxxxxxxx',
      description:
        'AITable.ai integration enables users to integrate AITable.ai data into the system. With automation, various tasks can be triggered automatically when data changes, such as data synchronization, notification pushing, or report generation. Suitable for scenarios requiring efficient data management and real-time responses.',
      title: 'AITable.ai',
    },
    apitable: {
      apitable_token: 'APITable API Token',
      apitable_token_placeholder: 'xxxxxxxxxxxxxxxxxxxxxxxx',
      description:
        'Through APITable integration, users can seamlessly connect APITable data with the system. Automation can automatically perform preset tasks, such as updating records, sending reminders, or triggering other operations, when data changes. Suitable for applications requiring flexible data management and quick responses.',
      title: 'APITable',
    },
    awsocr: {
      aws_ocr_token: 'AWS Textract API Token',
      aws_ocr_token_placeholder: 'xxxxxxxxxxxxxxxxxxxxxxxx',
      description:
        'AWS Textract is used to connect and manage AWS Optical Character Recognition services, supporting image text recognition and extraction, helping users efficiently process and store text data.',
      title: 'AWS Textract',
    },
    azure: {
      apikey: 'Azure AI API Key',
      apikey_placeholder: 'Enter your Azure AI API key',
      description:
        'Use Azure AI (Azure OpenAI / Azure AI Agents) via API key authentication to access completions, chat, embeddings, and agent deployments.',
      title: 'Azure AI',
    },
    banner_description:
      'The integration center provides a wealth of integration services to help you better connect various applications and services.',
    banner_title: 'Integrations',
    bedrock: {
      apikey: 'Amazon Bedrock API Key',
      apikey_placeholder: 'Enter your Amazon Bedrock API key',
      description:
        'Invoke Foundation Models via Amazon Bedrock to enable robust generative AI features—including fine‑tuning, retrieval‑augmented knowledge bases, and task agents.',
      title: 'Amazon Bedrock',
    },
    byte_doubao: {
      description: 'ByteDance Doubao integration for intelligent conversations and content generation.',
      title: 'ByteDance Doubao',
      use_cases: [
        'Helps students with academic questions and paper writing guidance',
        'Supports professionals with report writing and expert advice',
        'Provides travel planning, entertainment recommendations, and health consultations',
        'Offers creative inspiration and design suggestions for creators',
      ],
    },
    claudeai: {
      description:
        'The Claude model family are large language models (including Haiku, Sonnet, Opus) developed by Anthropic, providing intelligent conversations, content generation, and data analysis services. It can understand complex queries and provide accurate answers, helping users improve work efficiency and creativity.',
      title: 'Claude.ai',
      use_cases: [
        'Summarize records in databases and generate reports',
        'Generate email content based on records in databases',
        'Generate blog posts, press releases, and other text content',
      ],
    },
    deepseek: {
      apikey: 'DeepSeek API Key',
      apikey_help_text:
        'Enter the API key provided by DeepSeek. You can find it in DeepSeek API Keys. https://platform.deepseek.com/api_keys',
      apikey_placeholder: 'sk-xxxxxxxxxxxxxxxxxxxxxxxx',
      custom_base_url: 'Custom Base URL',
      custom_base_url_description:
        'Some third-party platforms also provide access to DeepSeek models, so you can customize the base URL (e.g., Alibaba Cloud, Volcano Engine, Silicon Valley Flow).',
      custom_model: 'Custom Model ID',
      custom_model_description: 'You can use a custom model ID to specify the model you want to use.',
      description:
        'DeepSeek-R1 is a state-of-the-art large language model optimized with reinforcement learning and cold-start data for exceptional reasoning, math, and code performance. ',
      organization_id: 'Organization ID',
      organization_id_help_text:
        'The organization ID is the unique identifier for your organization and can be used in API requests. (Usually not required)',
      title: 'DeepSeek',
    },
    delete_warning_content: 'This action cannot be undone. Are you sure you want to delete?',
    delete_warning_title: 'Delete Configuration',
    description_know_more: 'Learn more',
    dingtalk: {
      description:
        'Through the Webhook of the DingTalk custom bot, automatically send various messages to DingTalk groups. Paired with automation, it can be used for task reminders, status updates, or project reports. By achieving instant push notifications of task completion or important information through DingTalk groups, it enhances internal communication and task management efficiency within the enterprise.',
      form_item_1_label: 'Webhook URL',
      form_item_1_placeholder: 'Webhook URL',
      title: 'DingTalk Custom Bot',
    },
    explore_integration: 'Explore integration',
    features_list: 'Integrations List',
    feishu: {
      description:
        'Send messages to groups through the Webhook of the Feishu custom bot. Used in conjunction with automation, it can automatically push regular updates, alerts, or meeting schedules on the Feishu platform. This integration helps team members stay informed of important developments in Feishu groups, enhancing workflow transparency and information sharing efficiency.',
      form_item_1_label: 'Webhook URL',
      form_item_1_placeholder: 'Webhook URL',
      title: 'Feishu Custom Bot',
    },
    general: {
      err_msg: 'This field cannot be empty',
      note: 'Note',
      note_placeholder: 'Please provide a name that is easy to remember and identify',
    },
    github: {
      description:
        'Bind your Bika account with your GitHub account to easily log in to Bika using your GitHub account, ensuring security and convenience.',
      title: 'GitHub',
    },
    google: {
      description:
        'Bind your Bika account with your Google account to easily log in to Bika using your Google account, ensuring security and convenience.',
      title: 'Google',
    },
    googleai: {
      description:
        'Google AI is a series of large language models (including Gemini) developed by Google, capable of providing intelligent conversations, content generation, and data analysis services. It can understand complex queries and provide accurate answers, helping users improve work efficiency and creativity.',
      title: 'Google AI',
      use_cases: [
        'Deep market analysis using database records, insights into user needs and behavior patterns',
        'Automatically integrate database information to generate professional analysis reports',
        'Intelligent creation of engaging articles and social media content based on given topics and keywords',
      ],
    },
    imap: {
      create_new_integration: 'Connect to new email account',
      description:
        'By configuring an IMAP email account, users can integrate the ability to receive emails into the system. With automation, specific actions can be triggered when particular emails are received, such as automatically creating tasks, archiving emails, or triggering alerts. Suitable for scenarios where information needs to be extracted from emails and responded to.',
      password_label: 'Password',
      password_placeholder: 'Please enter the password',
      port_err_msg: 'Port must be a number',
      port_helper_text: 'Enter the port number used by the IMAP server. Common ports is 993.',
      port_label: 'Port',
      port_placeholder: 'Please enter the port number used by the IMAP server',
      server_helper_text:
        'Enter incoming mail server address (IMAP). If you do not have this information available, contact your email service provider.',
      server_label: 'IMAP Server',
      server_placeholder: 'imap.example.com',
      title: 'IMAP Email Account',
      tls_label: 'Enable TLS',
      user_name_label: 'Username',
      user_name_placeholder: '<EMAIL>',
    },
    integration: 'Integration',
    linkedin: {
      description:
        "LinkedIn is a professional social platform designed to help users build their professional networks, find job opportunities, and share industry insights. By integrating Bika's automation capabilities, you can efficiently manage your company or personal information.",
      title: 'LinkedIn',
      use_cases: [
        'Publish a new blog post on LinkedIn using database record',
        'Update LinkedIn company page with database record',
        'Create LinkedIn share updates for new form submissions',
      ],
    },
    make: {
      description:
        "Make.com is an automation platform that helps users connect applications and services through no-code or low-code solutions, streamlining workflows. By integrating Bika's database and automation, it allows your data to flow seamlessly between platforms.",
      title: 'Make.com',
      use_cases: [
        'post data to thousands of apps with Make.com',
        'save data from Make.com scenario to databases',
        'Create new database records from Make.com scenario',
        'Activate Make.com scenario when new form submissions',
      ],
    },
    my_integration: 'My Integration',
    mysql: {
      database_name: 'Database Name',
      database_name_placeholder: ' ',
      description:
        'MySQL is used to connect and manage MySQL databases, supporting data queries, inserts, updates, and deletions, helping users efficiently handle and store data.',
      host_helper_text:
        'Enter the address of the MySQL server. If you do not have this information available, contact your database administrator.',
      host_label: 'MySQL Server',
      host_placeholder: 'mysql.example.com',
      name_label: 'Username',
      name_placeholder: '<EMAIL>',
      password_label: 'Password',
      password_placeholder: ' ',
      port_err_msg: 'Port must be a number',
      port_helper_text: 'Enter the port number used by the MySQL server.',
      port_label: 'Port',
      port_placeholder: ' ',
      title: 'MySQL',
    },
    openai: {
      apikey: 'API Key',
      apikey_help_text:
        'Enter the API key provided by OpenAI, you can find it in your OpenAI account settings. https://platform.openai.com/api-keys',
      apikey_placeholder: 'sk-xxxxxxxxxxxxxxxxxxxxxxxx',
      custom_base_url: 'Custom Base URL',
      custom_base_url_description:
        'You can custom the OpenAI base url. All OpenAI compatible AI models API will be supported. (Google Gemini, Anthropic Claude, etc.)',
      custom_model: 'Custom Model Id',
      custom_model_description: 'You can use the custom model id to specify the model you want to use.',
      description:
        "Using OpenAI's GPT model, you can automatically generate natural language text, engage in intelligent conversations, write code snippets, or provide personalized suggestions.",
      organization_id: 'Organization ID',
      organization_id_help_text:
        'The organization ID is the unique identifier for your organization which can be used in API requests.(optional)',
      title: 'OpenAI',
    },
    page_description:
      'Integrate hundreds of other apps, AI agents and AI models. Create sophisticated automations between 3rd-apps and your stack by using Bika.ai.',
    page_title: 'Integrations & 3rd-party apps | AI Workflow Automation',
    postgresql: {
      database_name: 'Database Name',
      database_name_placeholder: ' ',
      description:
        'PostgreSQL is used to connect and manage PostgreSQL databases, supporting data queries, inserts, updates, and deletions, helping users efficiently handle and store data.',
      host_helper_text:
        'Enter the address of the PostgreSQL server. If you do not have this information available, contact your database administrator.',
      host_label: 'PostgreSQL Server',
      host_placeholder: 'postgresql.example.com',
      name_label: 'Username',
      name_placeholder: '<EMAIL>',
      password_label: 'Password',
      password_placeholder: ' ',
      port_err_msg: 'Port must be a number',
      port_helper_text: 'Enter the port number used by the PostgreSQL server.',
      port_label: 'Port',
      port_placeholder: ' ',
      title: 'PostgreSQL',
    },
    siri: {
      description:
        'Combining Apple Siri, Shortcuts, and the Bika API enables the automation of various workflows. For instance, users can quickly create a Bika mission for themselves or colleagues using Siri voice commands, freeing their hands and enhancing efficiency.',
      title: 'Siri',
      use_cases: [
        'Retrieve records in databases by Siri voice command and shortcuts',
        'Create records in database using Siri and shortcuts',
        'Upload photos to database using Siri and shortcuts',
        'Open the app and view the specific database via Siri',
        'Sync events from database to mobile calendar using Siri and shortcuts',
      ],
    },
    slack: {
      description:
        'Use the Incoming Webhook of the Slack app to send messages to channels. Combined with automation, it can automatically push notifications upon event triggers, such as task completion, system status updates, or project reports. It helps team members receive timely notifications in Slack channels, improving team collaboration and information transmission efficiency.',
      form_item_1_label: 'Incoming Webhook URL',
      form_item_1_placeholder: 'Incoming Webhook URL',
      title: 'Slack App',
    },
    smtp: {
      data_missing: 'Data missing',
      description:
        'Configure a custom sending email based on the SMTP protocol. Combined with automated workflows, it can automatically send emails upon specific event triggers, suitable for scenarios such as task completion notifications, fault alarms, periodic report sending, and bulk marketing emails.',
      password_label: 'Password',
      password_placeholder: 'Please enter the password',
      port_err_msg: 'Port must be a number',
      port_helper_text: 'Enter the port number used by the SMTP server. Common ports are 25, 465, and 587.',
      port_input_err_msg: 'Please enter the correct port',
      port_label: 'Port',
      port_placeholder: 'Please enter the port number used by the SMTP server',
      server_helper_text:
        'Enter outgoing mail server address (SMTP). If you do not have this information available, contact your email service provider.',
      server_label: 'SMTP Server',
      server_placeholder: 'smtp.example.com',
      title: 'SMTP Email Account',
      user_name_label: 'Username',
      user_name_placeholder: '<EMAIL>',
    },
    telegram: {
      description:
        'Leverage the capabilities of the Telegram Bot to send messages to groups, channels, or private chats. Combined with automation, it can automatically push notifications upon event triggers, such as system status updates, event reminders, or team dynamics, ensuring users receive timely information on the Telegram platform for efficient event management and quick responses.',
      field_bot_token: 'Bot Token',
      field_bot_token_placeholder: 'Enter Bot Token',
      option_manual_token: 'Enter Bot Token Manually',
      option_select_bot: 'Extract Bot Token from Integration',
      title: 'Telegram Bot',
      title_token: 'Bot Token',
    },
    tencenthunyuan: {
      description:
        'Tencent Hunyuan is a large language model developed by Tencent, featuring powerful Chinese composition, logical reasoning in complex contexts, and reliable task execution.',
      title: 'Tencent Hunyuan',
      use_cases: [
        'Provides document creation, text refinement, text proofreading, database and chart generation to improve content creation efficiency',
        'Offers meeting Q&A, summary, and task organization to simplify meeting operations and improve efficiency',
        'Enables intelligent creation of advertising materials to enhance marketing content production efficiency',
        'Builds intelligent shopping guides to help merchants improve service quality and efficiency',
      ],
    },
    third_party_integration: 'Third-Party Integration',
    tongyiqianwen: {
      description:
        'Tongyi Qianwen is a large-scale language model developed by Alibaba Cloud. It can generate various types of text, such as articles, stories, poems, etc., and can provide customized answers and services based on user needs to help users solve problems and complete tasks.',
      title: 'AliCloud Tongyi Qianwen (Qwen)',
      use_cases: [
        'Summarize records in databases and generate reports',
        'Generate email content based on records in databases',
        'Generate blog posts, press releases, and other text content',
      ],
    },
    twitter: {
      create_new_integration: 'Connect to new X(Twitter) account',
      description:
        'Connect to the Twitter account via OAuth to achieve automated tweet creation. Combined with automation, it can automatically post tweets in scenarios such as news releases, daily updates, or marketing campaigns, achieving scheduled information publishing. It helps keep the media account active and increases interaction with followers.',
      form_item_1_label: 'Client ID',
      form_item_1_placeholder: 'Enter Client ID',
      form_item_2_label: 'Client secret',
      form_item_2_placeholder: 'Enter client secret',
      title: 'X(Twitter) OAuth2.0',
    },
    twitter_oauth_1a: {
      access_token_label: 'Access Token',
      access_token_placeholder: 'Enter Access Token',
      access_token_secret_label: 'Access Token Secret',
      access_token_secret_placeholder: 'Enter Access Token Secret',
      api_key_helptext:
        'You can find the Consumer Key on the Twitter Developer Platform, https://developer.x.com/en/portal/projects-and-apps',
      api_key_label: 'API Key',
      api_key_placeholder: 'Enter API Key (Consumer Key)',
      api_secret_label: 'API Secret',
      api_secret_placeholder: 'Enter API Secret (Consumer Secret)',
      description:
        'Connect to a Twitter account via OAuth1.0a User Context to upload media file(images, GIFs, and videos). Combined with automation, you can post tweets with media content.',
      title: 'X(Twitter) OAuth1.0a',
    },
    vika: {
      description:
        'Vika is used to connect and manage Vika databases, supporting data queries, inserts, updates, and deletions, helping users efficiently process and store data.',
      title: 'Vika',
      vika_token: 'Vika API Token',
      vika_token_placeholder: 'xxxxxxxxxxxxxxxxxxxxxxxx',
    },
    webhook: {
      description:
        'Use the Webhook to receive and process HTTP requests from external systems. Combined with automation, it can automatically trigger actions such as data updates, notifications, or workflow executions upon receiving specific events. This integration helps streamline processes and ensures timely responses to external triggers, enhancing overall system efficiency and connectivity.',
      form_item_1_label: 'Webhook URL',
      form_item_1_placeholder: 'Webhook URL',
      title: 'Webhook',
    },
    wechat: {
      description:
        'Bind your Bika account with your WeChat account to log in to Bika easily and quickly by scanning the WeChat QR code.',
      title: 'WeChat Login',
    },
    wecom: {
      description:
        'Send messages to WeCom groups through the Webhook of the WeCom group bot. Combined with automation, it can be used for real-time project updates, system notifications, or meeting reminders within the enterprise. It ensures team members receive timely and important notifications in WeCom groups, improving team collaboration and information transmission efficiency.',
      form_item_1_label: 'Webhook URL',
      form_item_1_placeholder: 'Webhook URL',
      title: 'WeCom Group Bot',
    },
    zapier: {
      description:
        "Zapier is an automation platform that helps users connect applications and services through no-code or low-code solutions, streamlining workflows. By integrating Bika's database and automation, it allows your data to flow seamlessly between platforms.",
      title: 'Zapier',
      use_cases: [
        'post data to thousands of apps with Zapier',
        'Create new database records from Zapier zaps',
        'Activate Zapier zaps when new form submissions',
        'Update database records from Zapier zaps',
        'Delete database records from Zapier zaps',
      ],
    },
    zoom: {
      description:
        'Zoom integration allows users to schedule and manage Zoom meetings directly within the system. With automation, meetings can be automatically created, invitations sent, or attendees reminded when specific events are triggered. Suitable for managing online meetings and video calls conveniently and efficiently.',
      title: 'Zoom',
      use_cases: [
        'Create Zoom registrants for new form submissions',
        'Automatically create Zoom meetings upon event triggers',
        'Send Zoom meeting reminders to participants',
        'Upload new Zoom recordings to databases',
        'Generate Zoom meeting reports and summaries',
      ],
    },
  },
  invite: {
    copy_link: 'Copy Link',
    create_invite_description:
      'Members who join the team through the link you created will be automatically assigned to the appropriate group and role, and both parties will receive',
    create_invite_loading: 'Creating',
    create_public_invitation_link: 'Create public invitation link(s)',
    created_public_invitation_link: 'Public Invitation Link(s) Created',
    delete_link: 'Delete Link',
    email_invite_table: {
      createdAt: 'Created At',
      delete: 'Delete',
      email: 'Email',
      operation: 'Operation',
      resend: 'Resend',
      status: 'Status',
    },
    input_invite_email_invalid: 'Email format is incorrect',
    input_invite_member_email: 'Enter Email',
    input_invite_one_or_more_email: 'Enter one or more email addresses, confirm with the Enter key',
    invite: 'Invite',
    invite_by_email_desc: 'Members invited by email will both receive',
    invite_by_email_desc_next: 'can be used to upgrade the space, purchase premium templates, and redeem souvenirs.',
    invite_description:
      'Team members who join through this link will be automatically assigned to the group and role. Upon successful joining, both the link creator and the member joining the space for the first time will receive',
    invite_description_next:
      'It can be used for space upgrades, purchasing premium templates, and redeeming souvenirs.',
    invite_identify: 'Invite identity',
    invite_identify_guest: 'Guest',
    invite_identify_guest_desc:
      'Partners or external collaborators invited to the space to participate in specific tasks, with default access restrictions to resources',
    invite_identify_member: 'Member',
    invite_identify_member_desc:
      'Internal collaborators of the organization or team, working together on project tasks',
    invite_link_created_by: "'s invitation link",
    invite_link_created_fail: 'Creation Failed',
    invite_link_created_success: 'Creation Successful',
    invite_link_have_coins: 'You currently have',
    invite_members: 'Invite',
    invite_outsider_invite_input_already_exist: 'The email address already exists',
    invite_people: 'Invite Member',
    invite_people_by_email: 'Invite member by email',
    invite_people_by_email_button: 'Invite',
    invite_people_by_email_description: 'Enter email addresses separated by commas',
    invite_people_by_email_placeholder: 'Email addresses',
    invite_people_by_link: 'Invite member by link',
    invite_people_by_link_button: 'Copy',
    invite_people_by_link_copied: 'Link copied',
    invite_people_by_link_description: 'Share the link to invite member to join your team',
    invite_people_description: 'Invite member to join your team',
    invite_record: 'Invitation records',
    invite_record_description:
      'When the invitee joins, both the inviter and invitee will receive rewards that can be used to upgrade spaces, purchase premium templates, and redeem souvenirs.',
    invite_role: 'Role (Optional)',
    invite_role_placeholder: 'Please select a role',
    invite_status: {
      accepted: 'Accepted',
      pending: 'Pending',
      rejected: 'Rejected',
    },
    invite_team: 'Team (Required)',
    invite_team_placeholder: 'Please select a team',
  },
  launcher: {
    ai: 'AI',
    ai_launcher: 'AI Launcher',
    ai_launcher_description:
      "The AI Launcher is an intelligent and quick feature designed to help users efficiently perform various operations. Through a simple and intuitive interface, you can easily execute commands, find files, configure space, and more. Whether it's daily tasks or complex operations, the AI Launcher provides fast and efficient solutions, significantly enhancing work efficiency.",
    ask_me_anything: 'Ask me anything...',
    chat_history: 'Chat History',
    chat_replay: 'Chat Reply',
    command: 'Command',
    command_not_found: 'Result not found. Ask the AI.',
    commands: 'Commands',
    commands_placeholder: 'Which commands you want to do...',
    database_record: 'Record',
    database_record_placeholder: 'Which records you want to find...',
    document: 'Document',
    document_placeholder: 'Which commands you want find...',
    file: 'File',
    file_placeholder: 'Go files or folders by name...',
    getting_started: 'Getting Started',
    help: 'Help',
    help_placeholder: 'What can I help you with...',
    launcher: 'Launcher',
    mission: 'Mission',
    node: 'Nodes',
    pending_todos: 'Pending Missions',
    recently: 'Recently',
    record: 'Record',
    reminder: 'Reminder',
    router: 'Router',
    search_tip: 'Search...',
    shortcuts: 'Shortcuts',
    smart: 'Smart',
    task: 'Task',
    templates: 'Templates',
    ui_modal: 'UI Modal',
    unread_reports: 'Unread Reports',
    url: 'URL',
  },
  license: {
    apply_description: 'Get Bika.ai Self-hosted Installation Package.',
    apply_title: 'Bika.ai Self-hosted license apply',
    company_name: 'What is you company/team name?',
    company_size: 'How many people in your company/team?',
    copy_your_license_key: 'Copy your license key here',
    download_and_install_self_host: 'Download and install self-hosted',
    get_self_host_license_key: 'Get the license key (License Key) for self-hosted deployment',
    get_trial: 'You can get a 30-day trial opportunity',
    industry: 'What is your industry?',
    license_key: 'License Key',
    license_key_expired: 'License Key has expired',
    license_key_expired_desc: 'Your trial period has expired',
    please: 'please',
    submit: 'Install Bika.ai Self-hosted Packages',
    trial_desc: 'Currently, you are within a 30-day trial period. If you encounter any issues during use',
  },
  mission: {
    after_action: 'Next Action After Mission Completion',
    allow_complete_manually: 'Allow Complete Manually',
    allow_reject: 'Allow Reject',
    assign_type: 'Assign Type',
    assign_type_dedicated: 'Dedicated Mission',
    assign_type_dedicated_description:
      'The mission is sent to multiple members, but each member must complete it individually',
    assign_type_shared: 'Shared Mission',
    assign_type_shared_description:
      'The mission is sent to multiple members, and it will be marked as completed once any one member finishes it',
    assigned: 'Assigned To',
    assignee: 'Assignee',
    button_text: 'Button Text',
    can_transfer: 'Allow Transfer',
    complete: 'Complete',
    completedAt: 'Completed At',
    confirm: 'Confirm',
    createAt: 'Create At',
    create_record: 'Create Record',
    create_success: 'Create Mission Success',
    database_record: 'Database Record',
    database_view: 'Database View',
    date_time_now: 'Now',
    date_time_today: 'Today',
    description: 'Mission Description',
    description_placeholder: 'Please enter the mission description',
    detail: 'Details',
    dueAt: 'Due At',
    dynamic_datetime: 'Dynamic DateTime',
    dynamic_datetime_type: 'Dynamic DateTime Type',
    end_time: 'End Time',
    features_list: 'Missions List',
    force_popup: 'Force Popup',
    go_to_mission: 'Go to Mission',
    initiator: 'Initiator',
    know_and_next: 'Understood, next step',
    mission: 'Mission',
    mission_description:
      'Mission is a smart, automative, traceable tasks differs from typical tasks or to-do lists, which you have to check off by yourself.\nFor example, consider the Create Record Mission: when a user receives it, the mission will automatically be marked as complete only when the required record has been created.',
    mission_name: 'Mission Name',
    mission_name_placeholder: 'Please enter the mission name',
    mission_type: 'Mission Type',
    modal_content_mission_invalid:
      'The resource to which the task belongs is no longer accessible. Do you want to delete the task',
    modal_title_mission_invalid: 'Warm Tips',
    more_setting: 'More Setting',
    msg_mission_completed: 'Mission has been completed!',
    msg_mission_rejected: 'Mission rejected.',
    msg_no_next_action: 'No further action.',
    msg_transfer_not_supported: 'Transfer not supported.',
    next: 'Next',
    placeholder_assign_type: 'Please select the type of assignment',
    processing: 'Processing',
    progress: 'Progress',
    reject: 'Reject',
    reminder: 'Mission Reminder Time',
    reminder_description: 'Reminder Description',
    reminder_title: 'Reminder Title',
    reminder_to: 'Remind To',
    repeat: 'Set Repeat Reminder',
    seconds: '({seconds}s)',
    show_end_time: 'Set End Time',
    show_start_end_time: 'Set Start and End Time of the Mission',
    show_start_time: 'Set Start Time',
    specific_datetime: 'Specific DateTime',
    start_time: 'Start Time',
    taskIntroduction: 'Introduction',
    taskObjective: 'Objective',
    taskStatus: 'Status',
    time_internal: 'Time Internal',
    tips_for_readme_mission: 'Open this document in a new tab to help you practice while reading. ',
    transfer: 'Transfer',
    type: {
      ai_create_records: {
        description: 'AI will automatically create records based on specified rules',
        name: 'AI Create Records',
      },
      approval: {
        description:
          'A mission with only one step, where the task owner can click agree, refuse, or transfer to someone else. For example, leave approval',
        name: 'Approval',
      },
      comment_record: {
        description: 'Members who receive the mission need to comment on a specified record to complete the mission',
        name: 'Comment Record',
      },
      create_multi_records: {
        description: 'Members who receive the mission need to create a specified number of database records',
        name: 'Create Multiple Records',
      },
      create_record: {
        description: 'Members who receive the mission need to create a record in the specified database',
        name: 'Create Record',
      },
      create_task: {
        description: 'Members who receive the mission need to create a new task',
        name: 'Create Task',
      },
      enter_view: {
        description: 'Members who receive the mission will be guided to open a specified view of the database',
        name: 'Enter View',
      },
      google_meet: {
        description: 'Mission with a Google Meet meeting link, suitable for online meeting invitations',
        name: 'Google Meet',
      },
      install_template: {
        description: 'Members who receive the mission are required to complete the installation of a template',
        name: 'Install Template',
      },
      invite_member: {
        description: 'Members who receive the mission are required to perform an invitation operation once',
        name: 'Invite Member',
      },
      quest: {
        description:
          'A set of missions consisting of multiple sub-missions related to business. For example, user onboarding',
        name: 'Quest',
      },
      read_markdown: {
        description: 'Members who receive the mission are required to view a specified Markdown document',
        name: 'Read Markdown',
      },
      read_template_readme: {
        description: 'Members who receive the mission are required to view the documentation of a specified template',
        name: 'Read Template Readme',
      },
      redirect_space_node: {
        description: 'AI will automatically create records based on specified rules',
        name: 'Redirect Space Node',
      },
      reminder: {
        description: 'Members who receive this task will get a reminder message',
        name: 'Reminder',
      },
      review_record: {
        description: 'Members who receive the mission will be guided to view a certain record in the database',
        name: 'Review Record',
      },
      sequence: {
        description:
          'Missions that contain multiple steps and must be completed in order. For example, multi-level approval tickets',
        name: 'Sequence',
      },
      set_space_name: {
        description:
          'Members who receive the mission will be guided to the space settings interface to complete the setting of the space name',
        name: 'Set Space Name',
      },
      submit_form: {
        description: 'Create a mission and assign it to a member, requesting them to fill out a specified form',
        name: 'Submit Form',
      },
      submit_multiple_form: {
        description:
          'Members who receive the mission need to fill out all the specified forms before the mission is marked as complete. For example, entering contract orders, sales need to fill out customer, contract, payment record forms',
        name: 'Submit Multiple Form',
      },
      ui_launcher: {
        description: 'Used to launch user interface-related functions or applications',
        name: 'UI Launcher',
      },
      update_record: {
        description:
          'Members who receive the mission need to complete editing of a specified record to finish the mission',
        name: 'Update Record',
      },
      voov_meet: {
        description: 'Mission with a Voov Meet meeting link, suitable for online meeting invitations',
        name: 'Voov Meet',
      },
      zoom_meet: {
        description: 'Mission with a ZOOM meeting link, suitable for online meeting invitations',
        name: 'Zoom Meet',
      },
    },
    update_record: 'Update Record',
  },
  navbar: {
    agent_builder: 'Agent Builder',
    agent_builder_description: 'I am your Agent Builder. I don’t recruit people; I only build Agents',
    beta: 'Beta',
    chief_of_staff: 'Chief of Staff',
    chief_of_staff_description:
      'I am your Chief of Staff, assisting you with various tasks such as information retrieval and content generation',
    expert: 'Expert',
    explore: 'Explore',
    home: 'Super Agents',
    personal: 'Personal',
    personal_resources: 'Personal resources',
    private: 'Private',
    private_description: 'Private: Your exclusive resource space, where you can freely edit, add, or delete resources.',
    report: 'Reports',
    resources: 'Resource',
    shortcuts: 'Shortcuts',
    shortcuts_description:
      'Shortcuts: Includes shortcuts set by administrators or yourself for quick access to frequently used resources.',
    shortcuts_resources: 'Shortcuts resources',
    smart: 'Smart',
    smart_description:
      'Smart: Supports global search, missions, and reports, and is equipped with a trash bin and templates.',
    space_launcher: 'Space Launcher',
    space_launcher_description: 'Space launcher for Space',
    super_agent: 'Super Agent',
    team: 'Team',
    team_resources: 'Resources',
    team_resources_description:
      'Team resources: Collaborate with team members to edit, add, or delete resources, facilitating team collaboration and resource sharing.',
    todo: 'Missions',
  },
  node: {
    delete_node: 'Delete Node',
    delete_node_description: 'Are you sure you want to delete the node "{name}"?',
    delete_node_success: 'Successfully deleted the node "{name}"',
    edit_folder: 'Edit Folder',
    empty_folder: 'Empty Folder',
    export_attachments: 'Export attachments',
    export_bika_file: 'Export .bika file',
    export_excel: 'Export to Excel file',
    export_template: 'Export .bika template (Internal)',
    import_bika_file: 'Import .bika file',
    jump_to_node: 'Jump to Node',
    node: 'Node',
    node_detach: 'Detach from Template',
    node_detach_fail: 'Failed to detach from the template',
    node_detach_success: 'Successfully detached from the template',
    node_guide: {
      automation: {
        description: 'Automation allows you to set triggers and actions to automate workflow processes.',
        feature1: '• Multiple trigger conditions',
        feature2: '• Rich action types',
        feature3: '• Conditional branching logic',
        feature4: '• Execution history',
        tip1: '💡 You can set multiple trigger conditions',
        tip2: '💡 Test automation rules before enabling them',
        title: 'Welcome to Automation',
      },
      dashboard: {
        description: 'Dashboards help you visualize data, create charts and reports to gain insights into data trends.',
        feature1: '• Multiple chart types',
        feature2: '• Real-time data updates',
        feature3: '• Custom layouts',
        feature4: '• Data filters',
        tip1: '💡 You can add multiple data sources',
        tip2: '💡 Support exporting charts and data',
        title: 'Welcome to Dashboard',
      },
      database: {
        description: 'Database is the core feature of Bika, allowing you to store and manage data in a structured way.',
        feature1: '• Create custom field types',
        feature2: '• Multiple views to display data',
        feature3: '• Powerful filtering and sorting',
        feature4: '• Collaboration and permission management',
        tip1: '💡 You can drag and drop to reorder fields',
        tip2: '💡 Use views to create different data display modes',
        title: 'Welcome to Database',
      },
      default: {
        description: 'This is a powerful feature module that makes your work more efficient.',
        feature1: '• Intuitive and easy-to-use interface',
        feature2: '• Powerful feature set',
        feature3: '• Flexible configuration options',
        tip1: '💡 Explore various features to discover more possibilities',
        title: 'Welcome to this Feature',
      },
      form: {
        description:
          'Forms allow you to easily collect and organize information, with data automatically syncing to the associated database.',
        feature1: '• Drag-and-drop form designer',
        feature2: '• Multiple field type support',
        feature3: '• Automatic data validation',
        feature4: '• Conditional logic support',
        tip1: '💡 You can set display conditions for fields',
        tip2: '💡 Form submissions sync to the database in real-time',
        title: 'Welcome to Form',
      },
      got_it: 'Got it',
      main_features: 'Main Features:',
      tips: 'Tips:',
    },
    node_info: 'File information',
    node_name: 'Node Name',
    node_update: 'Update Template',
    permission: {
      add_permission_message: 'Please add the members, groups, or roles that need to set permissions.',
      can_comment: 'Can comment',
      can_edit: 'Can edit',
      can_edit_content: 'Update only',
      can_edit_content_desc: 'Can update existing content, cannot add or delete records in the database.',
      can_edit_desc: 'Can edit content, can add or delete records in the database.',
      can_view: 'Can view',
      can_view_desc: 'Cannot edit, can only view the content of the resource.',
      description:
        'You can finely allocate permissions based on different roles, members, and departments to effectively control data access and operation permissions. Through permission management, you can ensure that each team member can only access and operate functions and data related to their responsibilities, greatly improving data security and privacy.',
      full_access: 'Can manage',
      full_access_desc: 'Has all operation permissions for the resource.',
      login_to_edit: 'Login to edit',
      no_access: 'No access',
      no_access_desc: 'No permission to view the resource.',
      permission_description: {
        CAN_COMMENT: 'You can view and comment on the resource',
        CAN_EDIT: 'You can edit content and manage records in the database',
        CAN_EDIT_CONTENT: 'You can update existing content but cannot add or delete records',
        CAN_VIEW: 'You can only view the content of the resource',
        FULL_ACCESS: 'You have all operation permissions for the resource',
        NO_ACCESS: 'You do not have permission to access this resource',
      },
      remove: 'Remove permission',
      title: 'Permission Management',
    },
    publish_to_template_center: 'Publish to template center',
    republish: 'Republish',
    view_original_template: 'View original template',
  },
  notification: {
    all: 'All',
    all_notifications_marked_as_read: 'All notifications marked as read',
    app_notification: 'App notification',
    app_push_notification: 'App push notification',
    browser_notification: 'Browser notification',
    clean_all_notifications: 'Clean all notifications',
    confirm_to_clean_all_notifications: 'Confirm to clean all notifications?',
    mail_notification: 'Mail notification',
    mark_all_as_read: 'Mark all as read',
    new_agenda: 'New Agenda',
    new_mission: 'New Mission',
    new_report: 'New Report',
    no_notification_so_far: 'No notification so far',
    notification: 'Notification',
    notification_settings: 'Notification settings',
    notification_type: 'Notification type',
    sms_notification: 'SMS notification',
    system_message: 'System message',
    unread: 'Unread',
  },
  ok: 'OK',
  pagination: {
    load_more: 'Load more',
    loading: 'Loading...',
    no_more: 'No more',
  },
  pricing: {
    business: 'Business',
    change_your_plan: 'Change your plan',
    community: 'Community',
    currency_symbol: '$',
    customize: 'Custom',
    customize_seat: 'Custom seats',
    enterprise: 'Enterprise',
    enterprise_private_cloud: 'Dedicated cloud',
    enterprise_self_hosted: 'Self-hosted',
    experience_now: 'Experience now',
    features: {
      advanced_automation_integrations: 'Advanced Automation Integrations',
      advanced_automation_integrations_tips:
        'Advanced automation triggers and actions to connect external tools like advanced AI models, etc.',
      api_calls_per_month: 'API calls per month',
      api_rate_limits: 'API rate limits',
      authorized_email_domain: 'Authorized email domain',
      automation_integrations: 'Automation integrations',
      automation_integrations_tips: 'Automation triggers and actions to connect external tools',
      automation_run_history: 'Automation run history',
      automation_runs_per_month: 'Automation runs per month',
      browser_notifications: 'Browser notifications',
      byok_support: 'Bring your own key',
      coming_soon: 'Coming soon',
      community: 'Community',
      credits_per_seat_per_month: 'monthly AI credits/seat',
      credits_per_seat_per_month_tips:
        'Credits are distributed to the workspace, calculated as "number of seats × monthly credits", and reset every month. Credits are used for AI model calls.',
      custom_domain: 'Custom domain',
      data_sync: 'Data sync',
      email_support: 'Email support',
      export_bika_file: 'Export .bika File',
      export_bika_file_tips:
        '.bika is a file format that can be imported into Bika with complete data and structure like relations, automations and everything',
      export_excel_csv: 'Export Excel/CSV file',
      help_center: 'Help center',
      im_support: 'Whatsapp support',
      im_support_tips: 'You can get support via Whatsapp',
      integration_instances: 'Integration instances',
      managed_emails_per_month: 'Bika email service per month',
      managed_emails_per_month_tips:
        'The Bika official email service, it will use bika domain to send emails in automation',
      max_guest: 'Max guests',
      max_records_per_database: 'Records per database',
      max_records_per_space: 'Records per space',
      max_seat: 'Max seats',
      mirror_sync: 'Mirror sync',
      missions_per_month: 'Missions per month',
      mobile_notifications: 'Mobile notifications',
      planned_feature: 'Planned feature',
      private_template: 'Private template',
      professional_services: 'Professional services',
      publish_and_share: 'Publish and share',
      publish_template: 'Publish template',
      remove_logos: 'Remove branding logos',
      remove_logos_tips: 'Bika logos will not appear on the UI',
      reports_per_month: 'Reports per month',
      resource_nodes: 'Node Resources',
      resource_permissions: 'Resource permissions',
      self_hosted: 'Self-hosted & White-label',
      self_hosted_tips: 'Deploy Bika on your own servers and even white-labeled the installation instance',
      sell_template: 'Sell template',
      sms_notifications: 'SMS notifications',
      smtp_emails_per_month: 'SMTP emails per month',
      space_audit_log: 'Space audit log',
      space_sessions_log: 'Space sessions log',
      storage: 'Storage',
      storage_tips:
        'The total storage limit for all attachments stored in databases and documents within a single workspace.',
      sub_admin: 'Sub-admin',
      sub_domain: 'Sub-domain',
      unlimited: 'Unlimited',
      user_sessions_log: 'User sessions log',
      webinar: 'Webinar',
    },
    for_businesses_and_enterprises: 'For Self-hosted and Enterprises',
    for_individual_and_teams: 'For Individuals and Teams',
    free: 'Free',
    free_number: '$0',
    free_trial_7_days: '7 days free, then {price} per year',
    includes_word: 'Includes',
    modal_title: 'Change your plan',
    month: 'Month',
    monthly: 'Monthly',
    oncely: {
      oncely_code: 'Oncely Code',
      oncely_code_management: 'Oncely code management',
      oncely_code_placeholder: 'Please enter the Oncely code',
    },
    page_section_detail_title: 'Compare plans and features',
    page_section_question_title: 'Product Price Q&A',
    payment_successful: 'Congratulations on successfully upgrading to {plan}',
    payment_successful_description:
      'You have successfully upgraded to {plan}. You have received the following benefits:',
    per_seat: 'per seat',
    plus: 'Plus',
    popular: 'Most Popular',
    price: 'Price',
    pro: 'Pro',
    question: {
      answer_1: 'Answer 1',
      question_1: 'Question 1',
    },
    renew_and_cancel: 'Plans auto-renew annually until canceled',
    seat: 'Seats',
    team: 'Team',
    user: 'User',
    view_benefit_details: 'View Benefit Details',
    view_detail: 'View details',
    year: 'Year',
    yearly: 'Yearly',
  },
  publish_template: {
    allow: 'Allow',
    allow_detach_description:
      'Allowing detachment means that users who have installed the template can detach from it. After detachment, they cannot upgrade from the original template but can use it for secondary publishing.',
    allow_users_to_detach_template: 'Allow Users to Detach Template',
    author_space_title: 'Show my space name',
    author_user_title: 'Show my nickname',
    cancel: 'Cancel',
    category: 'Category',
    coming_soon: 'Template application coming soon',
    coming_soon_description:
      'All users can search for your template in the template application, but they cannot install it',
    configure_template: 'Configure Template',
    forbid: 'Forbid',
    init_mission: 'Init Mission',
    init_mission_description: 'You can set up an initialization mission to better guide users',
    keywords: 'Keywords',
    private: 'Private',
    private_description: 'Private: Only space members can see it. Public: All bika users can see it.',
    public: 'Public template application',
    public_description: 'All users can search for and install your template in the public template application',
    publish_data: 'Publish Data',
    publish_data_description: 'Whether to publish the data in the database together',
    publish_success: 'Publish Successful',
    publish_template: 'Publish Template',
    publish_to_template_center: 'Publish to Template Center',
    space: 'Template Application within the Space',
    space_description:
      'Only members of this space can search for and install your template in the "Template from your space"',
    template_author: 'Author',
    template_id: 'Template ID',
    template_published: 'Template has been published',
    template_published_description_coming_soon:
      'All users can search for your template in the template application, but they cannot install it',
    template_published_description_public:
      'All users can search for and install your template in the public template application',
    template_published_description_space:
      'Only members of this space can search for and install your template in the "Template from your space"',
    template_visibility: 'Template Visibility',
    use_cases: 'Use Cases',
    version_already_exist: 'Version Already Exists',
    version_description: 'Version Description',
    version_number: 'Version Number',
    view_template_center: 'View Template Center',
  },
  record: {
    active: 'Activity',
    activity: {
      anonymous: 'Anonymous',
      comment_tip: 'Shift + Enter Wrap, Enter Send',
      empty_activity: 'No activity',
      empty_comment: 'No comment',
      just_changelog: 'Revision history only',
      just_comment: 'Comments only',
      load_more: 'Load More',
      loading: 'Loading...',
      no_more: 'No More',
      record_comment_and_change: 'Record comments and revision history',
    },
    add_attachment: 'Add Attachment',
    add_local_file: 'Add local file',
    create: 'Create',
    create_record: 'Create Record',
    create_record_button: 'Create Record',
    create_record_description: 'Create a new record',
    create_record_failed: 'Create Record Failed',
    create_record_success: 'Create Record Success',
    delete_comment: 'Delete Comment',
    delete_comment_description: 'Are you sure you want to delete the comment?',
    delete_record: 'Delete Record',
    drop_file_upload: 'Drop file to upload',
    empty_comment: 'Empty Comment',
    first_record: 'First Record',
    go_next: 'Go next',
    go_previous: 'Go Previous',
    input_comment_placeholder: 'Comment record, @mention someone',
    max_file_size: 'Max file size: 500MB',
    modify_record: 'Modify Record',
    no_next_record: 'No next record',
    no_previous_record: 'No next record',
    paste_or_drop_file_upload: 'Paste/Drop file to upload here',
    record: 'Record',
    record_comment: 'Record Comment',
    record_delete: 'Record Delete',
    record_detail: 'Record Detail',
    record_detail_description:
      'In Bika.ai, users can click to expand each record. The Record Detail is the expanded view that contains all detailed information of the specific record.',
    record_pin: 'Record Pin',
    record_unnamed: 'Unnamed Record',
    request_modify: 'Request Modify',
    request_new_record: 'Request record',
    select_date: 'Select Date',
    select_from_files: 'Select from Files',
    select_from_gallery: 'Select from Gallery',
    select_member: 'Select members',
    select_option: 'Select Option',
    tab_general: 'General',
    take_photo_or_record_video: 'Take Photo or Record Video',
  },
  redeem: {
    oncely: {
      congratulations: 'Congratulations!',
      contact_service: 'Contact customer service',
      email: 'Email',
      email_code: 'Verification code',
      enter_space: 'Enter Space',
      input_email: 'Please enter your email address',
      input_email_code: 'Please enter the verification code',
      input_oncely_code: 'Please enter the redemption code',
      logout: 'Log out',
      new_user_tip:
        'Please be aware that you will receive a new space with paid rights through the redemption code, the old space does not support redemption code upgrades!',
      new_user_tip_ignore_code:
        'Upon successful activation, a new space will be created with full access to all tier-specific features. Please note that existing spaces cannot be upgraded through this method.',
      old_user_tip:
        'Detected that you have previously registered, with {spaceCount} spaces existing. Upon successful activation, a new space will be created with full access to all tier-specific features. Please note that existing spaces cannot be upgraded through this method.',
      oncely_code: 'Redemption code',
      question: 'Encountered a problem',
      reedem_oncely: 'Redeem your transaction',
      submit: 'Submit',
      success_redeem: '✨ New space created with all premium features unlocked!',
      you_have_used_fragment_1: 'You are currently logged in as',
      you_have_used_fragment_2:
        '. Click the Submit button below to instantly create a new space and activate all corresponding features for your subscription tier.',
    },
  },
  referral: {
    bika_coins_description: 'Credit can be used to redeem various services and products',
    check_usage: 'Check Usage',
    current_space_plan: 'Current Space Plan',
    earn_bika_coins: 'Earn credits',
    method_1: 'Method 1: Invite via the invitation link',
    method_2: 'Method 2: Invite via invitation code',
    method_3: 'Method 3: Invite to join the space',
    method_4: 'Method 4: Install the mobile app',
    other_referral_code: 'Invite code',
    referral: 'Referral',
    referral_code: 'Referral code',
    referral_rewards: 'Enter someone else’s invitation code and both parties will receive rewards',
    reward_history: 'Reward History',
    total: 'Total: ',
    view_my_referral_code: 'View my referral code',
    your_bika_coins: 'Your credits',
  },
  reminder: {
    no_reminder_so_far: 'No reminder so far',
    remind: 'Remind',
    remind_me: 'Remind Me',
    reminder: 'Reminder',
    reminders: 'Reminders',
  },
  report: {
    create_report: 'Send Report',
    create_report_description:
      'You can trigger the generation of reports through an automated process. The report content will be sent to specified individuals or groups in formats such as Markdown. This report is automatically generated by AI based on your settings to provide data and information to help you better understand project progress.',
    mark_all_as_read: 'Mark all as read',
    mark_all_as_read_content: 'Confirm whether to mark all unread reports as read with one click',
    mark_as_read: 'Mark as Read',
    no_report_so_far: 'No report so far',
    read: 'Read',
    read_report: 'Read the report',
    report: 'Report',
    report_description:
      'Generated by AI or automation based on predefined rules or data. It can take the form of an email, article, or document.',
    report_detail: 'Report detail',
    report_detail_description:
      'Displays detailed information about the report. After the automation process is completed, the AI automatically generates a report based on the user’s settings, helping users better review the progress of their work.',
    report_info: 'Report Info',
    reports: 'Create Report',
    unread: 'Unread',
  },
  resource: {
    add_filter_condition: 'Add filter conditions',
    add_shortcut_success: 'Shortcut added successfully',
    ai_page: {
      settings_html_copilot: 'Copilot',
      settings_html_description:
        'Enter HTML page code below, the page will be automatically generated after clicking save',
      settings_html_placeholder: 'Please enter HTML code',
      settings_html_title: 'HTML Page',
      welcome: 'Tell the Copilot your idea on the right, and it will generate a page for you',
    },
    all_resources: 'All resources',
    automation_name: 'Automation name',
    can_not_create_integration: 'Cannot create integration, please contact the administrator to configure',
    cancel_excel_import: 'Cancel import',
    cancel_excel_import_description: 'Are you sure you want to cancel the import?',
    cancel_incremental_import: 'Cancel incremental import',
    cancel_incremental_import_description: 'Are you sure you want to cancel the incremental import?',
    change_cover: 'Change cover',
    change_form_logo: 'Change logo',
    close_export_modal_warning: 'Are you sure you want to close the export?',
    content_changed_warning: 'The database content has been updated, please refresh to view the latest content',
    content_is_empty: 'Empty',
    create_ai_agent_success: 'Create AI Agent "{name}" successfully',
    create_ai_page_success: 'Created AI Page "{name} " successfully',
    create_automation_action_success: 'Create automation action successfully',
    create_automation_success: 'Create automation "{name}" successfully',
    create_dashboard_success: 'Create dashboard "{name}" successfully',
    create_database_success: 'Create database "{name}" successfully',
    create_document_success: 'Create document "{name}" successfully',
    create_folder_success: 'Create folder "{name}" successfully',
    create_form_success: 'Create form "{name}" successfully',
    create_from_blank: 'Create from Blank',
    create_from_blank_automation_description: 'Build automated processes from scratch',
    create_from_blank_dashboard_description: 'Create a new dashboard to visualize data',
    create_from_blank_database_description: 'Create a new database to store data',
    create_from_blank_document_description: 'Create a new document to write content',
    create_from_blank_folder_description: 'Create a new folder to organize resources',
    create_from_template:
      'Choose a template below to quickly start a scenario. If there is no suitable template, you can also click "Create from Blank" to customize the build.',
    create_integration: 'Create Integration',
    create_mirror_success: 'Create mirror "{name}" successfully',
    create_view: 'Create a view',
    create_view_success: 'Create view "{name}" successfully',
    dashboard_description: 'Dashboard description',
    dashboard_name: 'Dashboard name',
    data_is_fetching: 'Data is fetching, please wait...',
    day: 'Day',
    delete_field: 'Delete field',
    delete_field_description:
      'Once the field is deleted, it cannot be recovered. Are you sure you want to delete the field ',
    delete_folder_error: 'Failed to delete folder "{name}"',
    delete_folder_success: 'Delete folder "{name}" successfully',
    delete_resource_description: 'Are you sure you want to delete the resource: {name}?',
    delete_resource_error: 'Failed to delete resource "{name}"',
    delete_resource_success: 'Delete resource "{name}" successfully',
    delete_view: 'Delete view',
    delete_view_description: 'Are you sure you want to delete the view: {name}?',
    delete_view_error: 'Failed to delete view "{name}"',
    delete_view_success: 'Delete view "{name}" successfully',
    description: 'Node Resource is a type of node implementaion like database, automation, form, document etc.',
    download_again_file: 'Download failed? Click here to try again',
    download_done_file: "File parsed and downloading. You can check it in your browser's download history",
    download_loading_file:
      'Your request is being processed, which may take a few minutes. Please do not refresh or go back to avoid canceling the operation',
    download_template: 'Download Template.xlsx',
    edit_automation: 'Edit automation',
    edit_automation_action: 'Action configuration',
    edit_automation_trigger: 'Trigger configuration',
    edit_dashboard: 'Edit dashboard',
    edit_database: 'Edit database',
    edit_database_view: 'Edit database view',
    edit_field: 'Edit field',
    edit_form: 'Edit form',
    edit_template: 'Edit template',
    edit_widget: 'Edit widget',
    error_import_excel: 'Import failed, error message: {message}',
    error_import_excel_button: 'Continue importing',
    export_bika_file_include_data:
      'Exporting to a bika file (.bika) allows you to bundle your folders, database, and other resources together for local backup. If you check the "Include database records" option, the size of the bika file will increase accordingly',
    export_bika_file_title: 'Export Bika File',
    export_data_include_data: 'Include database records',
    export_for_excel: 'Export to Excel',
    features_list: 'Node Resources List',
    field: 'Field',
    field_not_found: 'Field not found',
    fields: 'Fields',
    filter_condition: 'Filter conditions',
    first_field_not_allow_drag: 'The first field cannot be dragged',
    folder_description: 'Folder description',
    folder_empty_description: 'You have no resources in this folder',
    folder_empty_title: 'Empty Folder',
    folder_loading_description: 'Loading resources...',
    folder_loading_title: 'Loading resources...',
    folder_name: 'Folder name',
    folder_no_content: 'There are no other folders in this directory',
    folder_readme: 'Folder Readme',
    form: {
      add_logo: 'Add Logo',
      click_to_view: 'Click to view',
      form_description: 'Please enter a description',
      link_to_resource: 'Link to resource:',
      submitted_successfully: 'Submitted successfully',
    },
    gallery: {
      cover: 'Cover image',
      cover_help_text:
        'Pick the attachment field as the cover image displayed at the top of each card within the gallery view.',
      crop_cover_image: 'Crop cover image',
      crop_cover_image_help_text:
        'The image will be cropped to center and fill the card, ensuring no empty spaces are visible.',
      custom_cards_per_row: 'Customize cards per row',
      custom_cards_per_row_help_text:
        'Manually set the number of cards displayed per row. Default is auto-layout (based on screen resolution).',
    },
    home: 'Edit Resources',
    import_bika_file_success: 'Successfully imported',
    import_bika_file_support: 'Supports uploading .bika files',
    import_excel_import_button: 'Import',
    import_excel_records_count: 'Your preview shows the first 10 rows. The actual import will include {count} rows',
    import_excel_step1:
      'Step 1: Download the template and fill in the data. Please note: Do not change the titles in the template to avoid import failure. Calculated fields, member fields, attachment fields, and relationship fields are not supported.',
    import_excel_step2: 'Step 2: Drag the completed Excel file here to upload.',
    import_file_for_box: 'Import data from .bika file and create a new resource',
    import_from_excel: 'Import from Excel',
    import_from_vika: 'Import from Vika',
    import_from_vika_helper_text:
      'Multiple resources are separated by commas, e.g. fod1, fod2. \nNote: \n1. First, Make sure you have the VIKA application integrated. \n2. If there is a member field in the database, make sure to import the member information from the Vika platform to Bika first, otherwise the member field will be automatically cleared after importing data. \n3. Vika users and Bika users are associated by email.',
    import_from_vika_label: 'Vika resource ID',
    include_widgets: 'Include widgets',
    included_resources: 'Included Resources',
    incremental_import_from_excel: 'Incremental Import from Excel',
    kanban: {
      add_kanban_group_card: 'New record',
      delete_kanban_tip_content: 'All records in this group will be moved to the uncategorized group',
      delete_kanban_tip_title: 'Delete group',
      editing_group: 'Edit group',
      group_already_exists: 'The group already exists',
      group_by_option_or_member:
        'Organize records into groups based on a specific "Member" field or "Single Select" field.',
      hide_kanban_grouping: 'Hide group',
      kanban_add_new_group: 'New group',
      kanban_no_data: 'No records',
      kanban_not_group: 'Uncategorized',
      kanban_view_limit:
        'The maximum number of records that can be displayed in the kanban view is 1000. Please filter the records to display them.',
      no_multiple_selection_member_field: 'Member field with multiple selection is not supported',
      no_single_choice_or_member_field: 'You currently have no single choice or member fields, go ',
      please_select_single_or_member_field:
        'Please select a single select field or member field to create a kanban view',
    },
    layout: 'Layout',
    layout_help_text: 'Layout defines how records are arranged and displayed within the view.',
    mirror_type_label: {
      database_view: 'Choose an existing view',
      node_resource: 'Choose an existing resource',
      view: 'Independent View',
    },
    month: 'Month',
    move_resource_error: 'Move failed',
    move_resource_success: 'Move successful',
    move_resource_to: 'Move {name} to the current folder',
    move_resource_to_public_description: 'Are you sure you want to move "{name}" to Team Resources?',
    move_resource_to_public_error: 'Failed to move to Team Resources',
    move_resource_to_public_success: 'Successfully moved to Team Resources',
    new_field: 'New field',
    no_cover: 'No cover',
    no_member_field: 'No member field',
    no_permission_operation: 'You do not have permission to perform this operation',
    no_permission_operation_description:
      'Sorry, you do not have permission to perform this operation, please notify the administrator',
    node_detail: 'Node detail',
    not_support_import_field:
      'Import data from Excel to create a new database. Please note: Calculated fields, member fields, attachment fields, and relationship fields cannot be imported.',
    operation_failed: 'Operation failed',
    parsing_excel_data: 'Parsing data',
    placeholder_no_field: 'No field',
    placeholder_no_number_field: 'No number-type field available',
    placeholder_select_field: 'Please select a field',
    placeholder_select_member_field: 'Please select a member field',
    placeholder_select_record: 'Please select a record',
    placeholder_select_resource: 'Please select a resource',
    placeholder_select_view: 'Please select a view in the database',
    placeholder_select_widget: 'Please select a widget',
    placeholder_view_name: 'Please enter the view name',
    record_detail: {
      link_new_record: 'Link new record',
      link_record: 'Link existing record',
      linked_from: 'Linked records from "{name}"',
      no_linkable_record: 'No linkable records',
      no_related_records: 'No related records',
      record_detail_not_found: 'Record detail not found',
      select_record: 'Select record',
      tip_refresh: 'The content has changed. Please copy your edited content and refresh the page.',
    },
    record_index: 'Record {index}',
    remove_field_success: 'Field removed successfully',
    remove_folder_description:
      'The folder and all its contained resources will be deleted and cannot be recovered after deletion. Are you sure you want to delete the folder [{name}]?',
    remove_shortcut_success: 'Shortcut removed successfully',
    required_field: 'Required field',
    resource: 'Node Resource',
    resources: 'Resources',
    set_form_required: 'Set as required',
    set_form_required_description:
      'When you set a field as required, users must fill in the field when creating or editing records.',
    success: 'successfully',
    success_import_excel: '{rows} rows of data have been started to import under {columns} columns',
    success_import_excel_button: 'View',
    support_upload_file: 'Supports uploading .xlsx/.csv files',
    template_creator: 'Template creator',
    template_folder_editor: 'Template folder editor',
    title_create_folder: 'New folder',
    title_dashboard_id: 'Dashboard',
    title_database_id: 'Database',
    title_delete_resource: 'Delete resource',
    title_edit_resource: 'Edit resource',
    title_export: 'Export data',
    title_field_id: 'Field',
    title_form_id: 'Form',
    title_form_name: 'Form name',
    title_import: 'Import data',
    title_member_field_id: 'Member field',
    title_mirror_type: 'Mirror type',
    title_move_resource_to_public: 'Move to Team Resources',
    title_move_to: 'Move to',
    title_new_ai: 'New AI Agent(Beta)',
    title_new_automation: 'New automation',
    title_new_computer: 'New Computer(alpha)',
    title_new_dashboard: 'New dashboard',
    title_new_database: 'New database',
    title_new_document: 'New document',
    title_new_file: 'New file',
    title_new_form: 'New form',
    title_new_mirror: 'New mirror',
    title_new_other_resource: 'New from template',
    title_new_page: 'New AI Page(Beta)',
    title_new_resource: 'New resource',
    title_new_view: 'New view',
    title_record_id: 'Record',
    title_resource: 'Resource',
    title_resource_description: 'Resource description',
    title_resource_id: 'Resource',
    title_resource_name: 'Resource name',
    title_resource_type: 'Resource type',
    title_shortcut: 'Add to shortcut',
    title_shortcut_cancel: 'Remove from shortcut',
    title_shortcut_personal: 'Personal shortcut',
    title_shortcut_personal_remove: 'Remove from personal shortcut',
    title_shortcut_space: 'Space shortcut',
    title_shortcut_space_remove: 'Remove from space shortcut',
    title_show_hidden_field: 'Show Hidden',
    title_space_admin: 'Admin functions',
    title_space_shortcut: 'Add to shortcut (visible to everyone)',
    title_update_folder: 'Edit folder',
    title_view_id: 'View',
    title_view_type: 'View type',
    toggle_view: 'Toggle View',
    type: {
      ai: 'AI Agent',
      ai_agent: {
        data_source: 'Data sources',
        description:
          'Enter your questions, and the AI Agent will generate relevant content and data based on your needs',
        settings_datasource_sitemap_placeholder: 'Please enter data source Sitemap',
        settings_datasource_url_placeholder: 'Please enter data source URL',
        settings_tool_toolsdks_package_key_placeholder: 'Please enter Package Key',
        settings_tool_toolsdks_package_version_placeholder: 'Please enter Package Version',
        skillset: 'Skillsets',
        system_prompt_description:
          "Instructions and limitations for the AI Agent's responses, tell it how to better answer questions.",
        system_prompt_placeholder:
          "Instructions and limitations for the AI's responses. \nYou can refer to the following writing style:\n\n# Role\nYou are a professional nearby travel advisor who can provide users with a variety of short trip and nearby travel plans, and vividly describe the characteristics of various attractions.\n\n# Limitations\n\nOnly discuss content related to short trips and nearby travel, and refuse to answer unrelated topics.\nAll output must be organized according to the given format and must not deviate from the framework requirements.\nThe feature introduction section must not exceed 100 words.\n      ",
        title: 'AI Agent',
      },
      ai_description: 'A resource for artificial intelligence features',
      ai_wizard_description:
        'AI Wizard is the chat interface in Bika.ai. It can be used for various types and purposes of AI conversations within the platform.',
      ai_wizard_features_list: 'AI Wizard List',
      ai_wizard_intent_ui_description:
        'In Bika.ai\'s AI Wizard, various types and purposes of AI conversations are categorized, and this category is referred to as "intent."\nDifferent intents lead to different action outcomes, as each intent is designed to trigger specific responses or functionalities within the system. ',
      ai_wizard_intent_ui_features_list: 'AI Wizard Intent UI List',
      app_page: 'App Page',
      app_page_description: 'App Page',
      automation: 'Automation',
      automation_action_description:
        'An automation action refers to a step that carries out a task, activity, event, or change, such as sending an email.\nYou can think of an action like this: When something occurs (the trigger) and the specified conditions are met, then this event (the action) takes place.',
      automation_action_features_list: 'Actions List',
      automation_description: 'A resource for setting up and managing automation workflows',
      automation_trigger_description:
        'An automation trigger acts as the "switch" that initiates an automation when specific conditions are met.\nConsider a Trigger as: When a specific event occurs (the Trigger) and certain conditions hold true, then the resulting event (the Action) takes place.',
      automation_trigger_features_list: 'Triggers List',
      canvas: 'Canvas',
      canvas_description: 'A canvas for drawing and designing',
      chat: 'Chat',
      chat_description:
        'With AI Wizard, you can engage in natural language conversations with the system, obtain the necessary information and support, and improve work efficiency.',
      chat_menu: {
        fold: 'Hide',
        pin: 'Pin',
        unpin: 'Unpin',
      },
      code_page: 'AI Page',
      code_page_description: 'Code a page via React / Vue with Bika Page Data API',
      create_node_resource: 'Create Node Resource',
      create_node_resource_description:
        'With AI Wizard, you can quickly and easily generate and manage various node resources, facilitating project planning and management. This feature allows you to more effectively organize and allocate resources, ensuring the smooth progress of the project.',
      create_record: 'Create Record',
      create_record_description:
        'With AI Wizard, you can quickly and easily create new data records, ensuring the accuracy and timeliness of information.',
      create_reminder: 'Create Reminder',
      create_reminder_description:
        'With AI Wizard, you can quickly and easily set reminders for various tasks and events, ensuring you do not miss important matters. You can customize the time and content of the reminders to meet your specific needs.',
      dashboard: 'Dashboard',
      dashboard_description: 'A dashboard for summarizing and displaying key data',
      database: 'Database',
      database_description:
        'The database is similar to a spreadsheet but more versatile. Each database consists of rows and columns, where rows represent records and columns represent fields. You can create multiple databases within a folder to organize and categorize different data types. Databases support various field types, such as text, numbers, attachments, and links, allowing for diverse information storage. You can utilize views to filter, sort, and group data, enhancing data management and analysis efficiency.',
      database_field_description:
        'Database fields contain details or metadata for each record in a database.\nDatabase fields hold information or metadata for each entry in a database. These fields can take various forms, allowing data to be stored as text, single or multiple selections, images, checkboxes, numbers, user tags, and more.',
      database_field_features_list: 'Database Fields List',
      database_view_description:
        'A database view provides a specific way to visualize and arrange the underlying data in a database.\nThe standard view is a grid, but other formats include forms, calendars, galleries, and kanban layouts.\nA single database can support multiple views and various types of views.',
      database_view_features_list: 'Database Views List',
      doc: 'Document',
      doc_description: 'A resource for creating and storing documents',
      file: 'File',
      file_description: 'A resource for storing and managing pure files',
      folder: 'Folder',
      folder_description: 'Folder for storing and managing files',
      form: 'Form',
      form_description:
        'The form feature allows you to create custom forms to collect and input data into specified databases. You can quickly generate a form by specifying a view of the database, and then share it to various social groups. The submitted data will automatically update to the corresponding database, making it easy to manage and analyze. The form feature supports various field types such as text, attachments, checkboxes, etc., to meet different data collection needs.',
      integration_description:
        'Integrations are connections between Bika.ai and external services or applications that enable seamless data transfer between both platforms.\nThe choice of integration you select will ultimately depend on the specific problem you aim to address with your data.\nFor instance, if you have a database records that tracks tasks and you want to utilize AI for summarization, you can leverage the OpenAI integration to send the data to OpenAI, and then use the returned information to send emails.',
      mirror: 'Mirror',
      mirror_description: 'A resource for synchronizing and reflecting data',
      report_template: 'Report Template',
      report_template_description: 'A template for creating and managing reports',
      view: 'View',
      view_description: 'A resource for displaying and browsing data',
      web_page: 'Web Page',
      web_page_description: 'A resource for creating and publishing web pages',
    },
    unbind_template_modal_content:
      'After unbinding the template, you can freely modify this file, but you will not receive subsequent updates for the template',
    unbind_template_modal_title: 'You need to unbind the template before performing this operation',
    update_automation_action_success: 'Update automation action successfully',
    update_automation_success: 'Update automation "{name}" successfully',
    update_dashboard_success: 'Update dashboard "{name}" successfully',
    update_database_success: 'Update database "{name}" successfully',
    update_folder_success: 'Update folder "{name}" successfully',
    update_view_success: 'Update view "{name}" successfully',
    view_count: '{count} views',
    view_hidden_all_field: 'Hide all field',
    view_hidden_field: 'Hide field',
    view_name: 'View name',
    view_show_all_field: 'Show all field',
    view_show_field: 'Show field',
    view_type: {
      form: 'Form',
      gantt: 'Gantt',
      grid: 'Grid',
      kanban: 'Kanban',
    },
    views: 'Views',
    week: 'Week',
    widget_setting: 'Widget setting',
    widgets: {
      description: 'Widgets are used to display data in a visual and interactive way.',
      features_list: 'Dashboard Widgets List',
      name: 'Widgets',
    },
  },
  role: {
    allow: 'Allow',
    msg_create_role_success: 'Role created successfully',
    msg_delete_role_error: 'Failed to delete {name}, {message}',
    msg_delete_role_success: 'Successfully deleted {name}',
    msg_load_role_error: 'Failed to load role information',
    msg_update_role_success: 'Role updated successfully',
    role: 'Role',
    roles: 'Roles',
    select_role: 'Select role',
  },
  scheduler: {
    daily_base: 'Daily',
    friday: 'Friday',
    hourly_base: 'Hourly',
    last_day: 'Last day',
    minute_base: 'Minute',
    monday: 'Monday',
    monthly_base: 'Monthly',
    repeat_frequency: 'Repeat frequency',
    repeat_interval: 'Repeat interval',
    repeat_per_monthday: 'Monthday',
    repeat_per_weekday: 'Weekday',
    saturday: 'Saturday',
    started_at: 'Started at',
    sunday: 'Sunday',
    thursday: 'Thursday',
    timezone: 'Timezone',
    tuesday: 'Tuesday',
    wednesday: 'Wednesday',
    weekly_base: 'Weekly',
    yearly_base: 'Yearly',
  },
  settings: {
    about: {
      about_brand: 'About Bika.ai',
      help_center: 'Help center',
    },
    account: {
      account: 'Account',
      account_information: 'Account information',
      api: 'Developer',
      api_description: 'Please keep your developer API Token safe!',
      connected_account: 'Connected accounts',
      connected_account_description: 'Connect your accounts from the platforms below for quick login to Bika.',
      login_record_description:
        'We have recorded your login activity in the system to help protect your account and provide a better user experience.',
      notification: 'Notification',
      referral: 'Referrals',
      session_logs: 'Session logs',
      top_up: 'Top-up',
      unbind: 'Unbind',
      unbind_description: 'Are you sure you want to unbind the account?',
    },
    audit: {
      action: 'action',
      action_name: {
        invitation_email_accept: 'Accept Invitation',
        invitation_email_delete: 'Delete Invitation Email',
        invitation_email_resend: 'Resend Invitation Email',
        invitation_email_send: 'Send Invitation Email',
        invitation_link_accept: 'Accept Invitation Link',
        invitation_link_create: 'Create Invitation Link',
        invitation_link_delete: 'Delete Invitation Link',
        node_create: 'Create Resource',
        node_delete: 'Delete Resource',
        node_detach: 'Detach Template',
        node_export: 'Export Resource',
        node_get: 'Access Resource',
        node_import: 'Import Resource',
        node_publish: 'Publish Resource',
        node_update: 'Update Resource',
        node_upgrade: 'Upgrade Template',
        share_grant: 'Grant Resource Access',
        share_password_create: 'Create Share Password',
        share_password_delete: 'Delete Share Password',
        share_password_update: 'Update Share Password',
        share_restore: 'Restore Resource Access',
        share_revoke: 'Revoke Resource Access',
        share_scope_update: 'Update Share Scope',
        space_update: 'Update Space',
        template_install: 'Install Template',
      },
      actor: 'Actor',
      description: 'Description',
      empty: 'No audit logs yet',
      page_info: 'Showning 1 to {size} of {total} events',
      search_empty: 'Search result not found',
      template: {
        invitation_email_accept: 'Accepted the invitation {email}',
        invitation_email_delete: 'Deleted the email {email}',
        invitation_email_resend: 'Resent {email}',
        invitation_email_send: 'Invited {emails}',
        invitation_link_accept: "Accepted <a href='/space/join/{token}'>the invitation link</a>",
        invitation_link_create: "Created <a href='/space/join/{token}'>an invitation link</a>",
        invitation_link_delete: "Deleted the <a href='/space/join/{token}'>invitation link</a>",
        node_create: "Created the resource <a href='/space/{spaceid}/node/{id}'>{name}</a> under {parent}",
        node_delete: 'Delete the resource {name}',
        node_detach: "Detached the resource <a href='/space/{spaceid}/node/{id}'>{name}</a>",
        node_export: "export the resource <a href='/space/{spaceid}/node/{id}'>{name}</a>",
        node_get: "Access resource <a href='/space/{spaceid}/node/{id}'>{name}</a>",
        node_import: "Import the resource <a href='/space/{spaceid}/node/{id}'>{name}</a>",
        node_publish: "Published the resource <a href='/space/{spaceid}/node/{id}'>{name}</a>",
        node_update: "Updated the resource <a href='/space/{spaceid}/node/{id}'>{name}</a>",
        node_upgrade: "Upgrade the template folder <a href='/space/{spaceid}/node/{id}'>{name}</a>",
        share_grant: 'Granted {units} access privilege to the resource {name}',
        share_password_create: 'Created a password for access the share resource {name}',
        share_password_delete: 'Updated the password for access the share resource {name}',
        share_password_update: 'Updated the password for access the share resource {name}',
        share_restore: 'Restored access privilege to the resource {name}',
        share_revoke: 'Revoked {unit} access privilege to the resource {name}',
        share_scope_update: 'Updated the share scope of the resource {name}',
        space_update: 'Update the space setting',
        template_install: "Installed the template (<a href='/space/{spaceid}/node/{nodeid}'>{name}</a>)",
      },
      time: 'Time',
    },
    billing: {
      already_support: 'Unlocked',
      apply_refund: 'Apply for Refund',
      cancel_subscribe: 'Cancel Subscription',
      cancel_subscription_content: 'Are you sure you want to unsubscribe?',
      cancel_subscription_tips:
        'Cancel successfully, you will still retain full access to all features until the end of your current billing period',
      cancel_subscription_title: 'Cancel Subscription',
      canceled_at: 'Canceled at',
      change_payment_method: 'Change Payment Method',
      change_plan: 'Change Plan',
      current_plan: 'Current plan',
      manage: 'Manage Billing',
      next_invoice_date: 'Next invoice date',
      no_billing_day: 'None',
      not_support: 'Not support',
      payment_status: {
        buying_plan: 'You are purchasing {plan} plan',
        guest_limit: '🌟 1000 guests',
        payment_failed: 'Payment failed',
        payment_failed_description: 'Sorry, subscription to {plan} plan failed, please try again',
        privileges_gained: 'You have gained the following privileges:',
        storage_limit: '🌟 200GB storage space',
        try_again: 'Try again',
        try_now: 'Try now',
        unlimited_members: '🌟 Unlimited members',
        upgrade_success: 'Congratulations on upgrading to {plan} successfully!',
        waiting_payment: 'Waiting for payment',
      },
      resume_subscription: 'Resume Subscription',
      start_now: 'Start now',
      usage: 'Usage & Billing',
      usage_detail: 'Usage details',
      usage_limit_template:
        'Sorry, the <strong>{feature}</strong> limit for this space is <strong>{max}</strong>, and you have already used <strong>{current}</strong>. Please upgrade your plan to unlock higher limits.',
      usage_tips: 'Contact us if you need purchase assistance',
      usages: {
        automation_runs: 'Automation runs per month',
        api_request: 'API requests per month',
        resource_permission: 'Assign Permission Resources',
        emails: 'Emails per month',
        guests: 'Total guests',
        missions: 'Missions per month',
        records: 'Total records',
        reports: 'Reports per month',
        resources: 'Total node resources',
        seats: 'Total seats',
        space_integrations: 'Total integration instance',
        storages: 'Total storage',
        unused: 'Unused',
        used: 'Used',
      },
      will_cancel_subscribe: 'Will cancel subscription, you can cancel the subscription',
      you_will_lose_all_privileges: 'You will lose all privileges after canceling the subscription',
    },
    coin_rewards: {
      all_plan: 'All plan',
      all_plan_description: 'If you need help, you can contact us',
      and: 'and',
      choose_plan: 'Choose plan',
      coin_description: 'Can be used for',
      copy_invitation_code: 'Copy invitation code',
      copy_invitation_link: 'Copy invitation link',
      current_plan: 'Current plan',
      get_coin_history: 'Reward history',
      get_rewards_methos_1: 'Method 1: When inviting new users via invitation link, both parties will receive rewards',
      get_rewards_methos_2: 'Method 2: Share your invitation code with new users to earn rewards for both parties.',
      get_rewards_methos_3: 'Method 3: Invite members to the Space, both parties get',
      get_rewards_methos_4: 'Method 4: Install the App, you will get',
      invite_member_rewards_coin: 'Invite members to get credits',
      no_coin_history: 'No reward history',
      plan_unit: 'Per member/month',
      purchase_advanced_mode: 'Buy the advanced mode',
      recommend_get_bika_coin: 'Recommended reward credits',
      to_invite_member: 'Go invite members',
      upgrade_space: 'Upgrade space',
      view_usage: 'View usage',
      your_coin: 'Your credits',
    },
    general: 'General',
    guest: {
      create_guest_team: 'Create guest team',
      delete_guest_team_description:
        'Once deleted, the guest team cannot be restored. Are you sure you want to delete this guest team?',
      delete_guest_team_success: 'Guest team deleted successfully',
      delete_guest_team_title: 'Delete guest team',
      edit_guest_team: 'Edit guest team',
      no_guest: 'No guests',
      select_guest: 'Select guest',
      select_guest_team: 'Select guest team',
    },
    member: {
      assign_members: 'Assign',
      choose_member_role: 'Choose member role (optional)',
      choose_member_role_placeholder: 'Please select a role',
      choose_member_team: 'Set teams',
      choose_member_team_placeholder: 'Please select a team',
      delete_member: 'Delete member',
      delete_member_confirm: 'Are you sure about moving members out of the space?',
      description: 'Description',
      edit_error: 'Edit failed',
      edit_member: 'Edit member',
      edit_success: 'Edit successful',
      empty_email: 'No email available',
      member_name_setting_description: 'The member name is an in-station nickname that does not change between spaces',
      member_or_group_not_found: 'Member or group unfound',
      remove_member: 'Remove member',
      remove_member_confirm: 'Are you sure you want to remove the selected member from this role?',
      remove_member_confirm_content:
        'The selected member may lose some permissions. Are you sure you want to remove them from this role?',
      remove_members: 'Remove {count} member(s)',
      set_space_member_name: 'Set the nickname of this space',
    },
    message_member_name_modified_failed: 'Failed to modify the member name',
    message_member_name_modified_successfully: 'Member name edited',
    nickname_modified_failed: 'Failed to modify the name',
    notification: {
      advertise:
        'It is recommended to download the Bika.ai mobile app for more timely notifications and an exclusive experience',
      description:
        'Bika supports sending you reminder messages through a variety of notification methods for the latest status of tasks, schedules, and reports, allowing you to grasp various developments in more real-time.',
      email: {
        description: 'You can receive notifications through the browser after binding',
        title: 'Receive email notifications',
      },
      push: {
        description: 'You can receive notifications via SMS after binding',
        title: 'Receive browser notifications',
      },
      sms: {
        description: 'You can receive notifications via email after binding',
        title: 'Receive SMS notifications',
      },
      title: 'Notification',
      wechat_push: {
        setting: 'Settings',
        title: 'WeChat official account notifications',
      },
    },
    other: 'Other',
    role: {
      confirm_delete_role: 'Are you sure you want to delete this role?',
      create_role: 'Create role',
      create_role_empty_error: 'Role name cannot be empty',
      create_role_placeholder: 'Please enter role name',
      delete_role: 'Delete role',
      delete_role_confirm_content: 'Deleting this role is irreversible. Are you sure you want to delete it?',
      delete_roles: 'Delete ${count} roles',
      deselect: 'Deselect',
      edit_role: 'Edit role',
      edit_role_error: 'Unable to edit, please refresh the page',
      edit_role_placeholder: 'Please enter role name',
      management_role: 'Management role',
      member_no_role_tips: 'This role has no members yet',
      need_select_a_role: 'Please select a role',
      need_select_role: 'Please select roles',
      non_management_role: 'Non-management role',
      role_name: 'Role name',
      role_permission: 'Role permission',
      role_type: 'Role type',
      select: 'Select',
    },
    space: {
      announcement_description:
        'The announcement content will appear on the homepage of the space, visible to all members upon entering the space. Additionally, it supports Markdown format editing to enhance the readability and aesthetics of the announcement.',
      announcement_placeholder: 'Please enter the announcement content to keep members informed of the latest updates.',
      authorized_domain: 'Authorized domain',
      authorized_domain_description:
        'Set up authorized email domains to restrict access to the space. Only email accounts from these domains can join, while others will be denied.',
      authorized_domain_settings: 'Authorized email domain settings',
      config_watermark: 'Watermark',
      create_mission: 'Create Mission',
      create_mission_description:
        'You can set up automation to trigger new tasks of different types, supporting configurations such as assigning task executors, node resources, due dates, and other details. Once created, tasks will be automatically triggered based on the set time and resources, and managed and tracked by AI to ensure efficient task completion.',
      current_permission: 'Current space default permission is',
      current_permission_tips:
        'This means that when anyone creates a new resource and does not assign permissions, all members in the space will have by default',
      delete_space: 'Delete space',
      delete_space_confirm_desc:
        'This action cannot be undone! To prevent accidental operations, please enter space id {spaceId} to confirm. After deletion, all data will be permanently lost.',
      delete_space_confirm_title: 'Delete space',
      delete_space_desc:
        'Once deleted, all data in the space (including node resources, attachments, etc.) will be permanently removed and cannot be recovered',
      delete_space_success: 'Space deletion successful',
      description:
        'Space is a virtual workspace where you can create, manage, and collaborate on node resources with your team.',
      disable_global_manage_resource:
        'Control member permissions for resource creation, access, and operations under the root node',
      disable_global_manage_resource_description:
        'Controls the initial access permissions for resources when members join the space. If disabled, all members will have "Can manage" permissions by default.',
      disable_global_manage_resource_learn_more: 'Go to help documentation to learn more',
      invite_members: 'Invite members',
      ip_address_placeholder: 'Please enter IP address',
      join: 'Join',
      member_management: 'Members',
      member_management_description:
        'Member management is designed to help you efficiently manage team members. Through this module, you can add, delete, and edit member information, and assign appropriate roles and permissions to each member. The member management module also provides search and filtering functions, making it easy to quickly find specific members. Additionally, you can create and manage groups, adding members to groups for more detailed organizational structure management.',
      mission: 'Mission',
      mission_description:
        'A specific task or goal assigned by AI, typically part of an automated workflow. In Bika.ai, tasks are automatically generated by AI based on your settings, prompting you to complete them.',
      permission_settings_description: 'Space resource management',
      placeholder_authorized_domain: 'Please enter the authorized email domain, e.g., example.com',
      please_input_space_id: 'Please input the space id',
      request_new_record: 'Request Record',
      request_new_record_description:
        'A log of requests submitted to the system, including details such as timestamps and statuses. Bika.ai uses this to track automation tasks and user inputs for reporting purposes.',
      rich_document: 'Rich Document',
      rich_document_description:
        'A document format that supports text, images, and other media elements for enhanced content presentation. Bika.ai allows the integration of various document types for reporting and automation purposes.',
      role_management: 'Roles',
      role_management_description:
        'Role management is designed to help you efficiently manage and assign permissions. When installing templates, some templates will preset certain roles, and you can add members to these roles to better complete the template configuration. Additionally, you can freely create custom roles and assign any department or member to one or more roles, achieving more flexible permission control. Through role management, you can ensure that each member has appropriate permissions, enhancing team collaboration efficiency.',
      setting_info: 'Setting info',
      setting_info_description:
        'Setting information refers to the settings interface that pops up when you open personal settings. Here, you can update personal information, choose a theme, set the time zone, and configure the system language. This interface helps users customize preferences to achieve a more personalized experience.',
      space: 'Space',
      space_audits: 'Audit logs',
      space_has_be_deleted: 'The space has been deleted',
      space_has_be_deleted_desc: 'Sorry, you do not have permission to view, please notify the administrator',
      space_settings: 'Space settings',
      space_sidebar: 'Space sidebar',
      space_sidebar_description:
        'The Space Sidebar provides quick navigation, helping you easily access various functions and modules within the space. Through the sidebar, you can view the home page, tasks, reports, resources, settings, and more, simplifying operations and improving work efficiency. The sidebar is designed to offer you a streamlined experience, enhancing team collaboration.',
      third_party_integration: 'Integrations',
      upgrade: 'Upgrade',
      usage: 'Billing',
      wallpaper_button: 'Set wallpaper',
      wallpaper_description:
        'The wallpaper will appear on the homepage of the space, visible to all members upon entering the space.',
      wallpaper_preset_photos: 'Preset wallpapers',
      wallpaper_title: 'Homepage wallpaper',
      watermark_description: 'Global watermark',
      watermark_description_2:
        'To ensure the security of enterprise information, resources support displaying a global watermark. The watermark content is the name of the current accessing member + the suffix of the phone number or the prefix of the email address, to prevent screenshot leaks',
      workflow: 'Workflow',
      workflow_description:
        'A series of tasks or processes designed to achieve a specific outcome. In Bika.ai, workflows are managed by AI automation, simplifying repetitive tasks across different functions.',
    },
    upgrade: {
      action_record: 'Details',
      ai_invoke_consume: 'AI invoke consume',
      ai_invoke_count_达标奖励: 'AI invoke count达标奖励',
      benefit_details: 'View benefit details',
      bkc: 'Use BKC for deduction',
      bkc_deduction: 'BKC deduction',
      cancel_subscription: 'Cancel Subscription',
      consumption_log: 'Usage',
      credit: 'Space credits',
      credit_desc:
        'Space credits is a credit that users can get through subscription, invite members, and consume credits, which can be used to exchange space services.',
      currency: 'Currency',
      currently_owns: 'Currently owns',
      cycle: 'Subscription period',
      cycle_descritpion: 'Plan will auto-renew monthly until cancelled.',
      date: 'Date',
      detail: 'Detail',
      get_more_bika: 'Get more credits',
      gift_credit: 'Daily credits',
      gift_credit_desc:
        'Free credits gifted daily by the Bika. You can earn them simply by logging in, and they refresh automatically every day.',
      invite_logup: 'Invite logup',
      invite_member: 'Invite',
      invite_people: 'Invite friends',
      invite_space: 'Invite to join the space',
      loading_tips: 'Calculating price, please wait...',
      member: 'Member',
      or: 'or',
      other_method_bkc: 'Can also be obtained through',
      pay_annual: 'Pay annually',
      pay_monthly: 'Pay monthly',
      pay_now: 'Pay now',
      pay_tips: 'By continuing, you agree to the Bika.ai Terms and Conditions.',
      payment: 'Choose payment method',
      permanent_credit: 'Gift',
      permanent_credit_desc: 'Credits earned through Bika activities and member invitations.',
      plan: 'Choosen plan',
      quanty: 'Seats',
      recharge_consumption_integral: 'Credits change',
      resume_subscription: 'Resume Subscription',
      serial_number: 'Index',
      space: 'Space name',
      space_member_num: 'Number of space members',
      subscribe_credit: 'Current plan',
      subscribe_credit_desc:
        'Determined by the space subscription plan. After subscribing, you can obtain corresponding space credits, which are automatically refreshed monthly based on your subscription date.',
      subtotal: 'Subtotal',
      total: 'Total',
      unit_price: 'Unit amount',
      user_referral: 'User referral',
    },
  },
  share: {
    already_set_permission: 'Permissions have been restricted, no longer inheriting permissions from the parent folder',
    change_permission_success: 'Permission change successful',
    change_pwd: 'Change password',
    change_pwd_success: 'Password has been changed',
    change_share_pwd: 'Change share password',
    close_short_link_warning: 'After disabling the short link, the link below will become invalid',
    copy_pwd_and_link: 'Copy link and password',
    copy_pwd_and_link_success: 'Link and password copied successfully',
    create_short_link_warning: 'Create a short link, the sharing link below will become invalid',
    has_num_member_share: '{memberCount} members are sharing',
    member_of_share_permission: 'Members with shared permissions',
    network_user_need_pwd:
      'Users on the internet need a password to access, users within the organization can access directly without a password',
    open_pwd: 'Enable password',
    permission: {
      input_pwd: 'Enter Password',
      no_login_visit: 'You may not have permission or you may not be logged in',
      no_permission_visit: 'You do not have permission to view this resource',
      no_pwd_visit: 'This resource is password protected, please enter the password',
      notify_admin: 'Notify Administrator',
      notify_admin_for_permision: 'Sorry, you do not have permission to view this. Please notify the administrator',
      notify_admin_success: 'Already notified! Please wait patiently!',
      right_pwd: 'Correct Password',
      share_permission_can_edit: 'Internet users with the link can edit',
      share_permission_can_view: 'Internet users with the link can view',
      share_permission_default: 'Only space members or visitors can access',
      share_permission_form_anonymous_write: 'Link visitors can submit without logging in',
      share_permission_form_login_write: 'Link visitors must log in to submit',
      wrong_pwd: 'Wrong Password',
      publish_to_the_community: 'Publish to the community	',
      replay_mode: 'Replay mode	',
    },
    pwd_pattern: 'Enter New Password',
    recover: 'Recover',
    recover_permission_success: 'Permission recovery successful',
    set_share_pwd: 'Set share password',
    share: 'Share and permissions',
    share_text: 'Share',
  },
  shortcuts: {
    no_pin_so_far: 'There are no shortcuts yet. Quickly set your frequently used files as shortcuts',
    pin_to_top: 'Pin to Top',
    pinned: 'Pinned',
    shortcuts: 'Shortcuts',
    unpin_from_top: 'Unpin from Top',
  },
  skillset: {
    page_description:
      'Bika.ai skillsets includes MCP servers, 3rd-party apps, and integrations for your AI agents and automation workflow.',
    page_title: 'Skillset, Apps, Integrations',
  },
  slogan: {
    alternatives: [
      {
        name: 'Airtable',
        url: 'https://airtable.com/',
        description:
          'Compare to Airtable, Bika.ai is more focused on AI automation and proactive assistance. Bika.ai is more suitable for users who need more automation and AI assistance in their work and life.',
      },
      {
        name: 'Zapier',
        url: 'https://zapier.com/',
        description:
          'Compare to Zapier, Bika.ai is more focused on out-of-box templates and database workflow. Bika.ai is more suitable for users who need more automation and AI assistance in their work and life.',
      },
      {
        name: 'Make',
        url: 'https://www.make.com/',
        description:
          'Compared to Make, Bika.ai provides more integrated AI-driven solutions and proactive automation directly within its platform. Bika.ai is ideal for users seeking deep automation with advanced AI capabilities to streamline complex workflows and data management tasks.',
      },
    ],
    highlights: [
      {
        icon: '/assets/icons/highlights/auto-template.png',
        name: 'Teams for AI Agents',
        description: 'Chat, build, manage an agentic AI organization like a messenger app.',
        keywords: '',
      },
      {
        icon: '/assets/icons/highlights/ai-automation.png',
        name: 'MCP-powered App Integrations',
        description:
          'Connect to or customize over 10k+ MCP tools. Preset skillset tools include search (pages, images), research, and office tools (slides, documents, spreadsheets), among others.',
        keywords: '',
      },
      {
        icon: '/assets/icons/highlights/data-visual.png',
        name: 'Supercharged No-code Workspace',
        description:
          'Enjoy no-code components with a billion-row database, automated workflows, real-time collaboration on documents, dashboards, and more—all in one place. OpenAPI-friendly and extensible.',
        keywords: '',
      },
      {
        icon: '/assets/icons/highlights/auto-publish.png',
        name: 'The Agentic AI Store',
        description:
          'Make and publish your own agentic AI templates and agentic AI teammates, and share with the community. ',
        keywords: '',
      },
    ],
    keywords:
      'AI Organizer, One-Person Company, Multi-Agents Swarm, Workflow Automation, Email Automation, CRM Automation',
    personas: [
      {
        name: 'Marketers and content creators',
        description: 'Marketers and content creators',
      },
      {
        name: 'Influencers',
        description: 'Influencers',
      },
      {
        name: 'Automation consultants',
        description: 'Automation consultants',
      },
      {
        name: 'Project managers',
        description: 'Project managers',
      },
      {
        name: 'Sales managers',
        description: 'Sales managers',
      },
    ],
    screenshots: ['/assets/blog/what-is-bika-ai/template.en.gif'],
    slogan_mkt_e:
      "Bika.ai, World's First AI organizer for vibe working, combines AI agents, automation, databases, dashboards and documents, manage and automate endless business processes.",
    slogan_mkt_s: 'Chat, build, manage one-person AI company like a messenger app. ',
    slogan_prd_e:
      'Bika.ai mixes Airtable (database) and Zapier (automation) into an incredible easy-to-use platform for building data-critical Business AI Agents such as CRM, marketing automation system, project management system, BI and ERP, all at an amazing price.',
    slogan_prd_l: 'Vibe working with agentic AI team, automation, databases, dashboards, and documents.',
    slogan_prd_m: "The World's First AI Organizer",
    slogan_prd_xl:
      'Bika.ai, the first AI organizer for vibe working. Build your own agentic AI team combines AI agents, automation, databases, dashboards, and documents. Just chat, build, manage agentic AI teams like a messenger app across sales, marketing, research, design, engineering, and more.',
    slogan_title: 'Bika.ai: AI Organizer for One-Person Company',
    use_cases: [
      {
        name: 'Marketing Automation',
        description:
          'Automatically send marketing content such as emails, YouTube videos, Twitter tweets, and SMS notifications in bulk, on schedule, and at intervals, achieving fast and efficient marketing automation.',
      },
      {
        name: 'Lead Management',
        description:
          'Automatically collect, track, and manage millions of sales leads, helping you systematically follow up with potential customers and improve sales conversion rates.',
      },
      {
        name: 'AI Reporting to You',
        description:
          'Regularly suggests AI strategies and automation processes to you, and only executes them after your decision. AI will also generate regular reports for you.',
      },
      {
        name: 'All-in-one Solution',
        description:
          'No need for complex professional software. Bika.ai’s lightweight AI automation database can meet your needs for customer data storage, management, and tracking.',
      },
      {
        name: 'Custom Editing',
        description:
          'Bika.ai provides a powerful low-code/no-code editor, allowing you to easily customize various automation task processes and data systems, enabling project management, support tickets, order management, and more application scenarios.',
      },
    ],
    usp: 'Bika.ai offers an plug-and-play automation database with built-in automations and many integration options. It handles big data databases, up to billions of entries, effortlessly. You won’t need to constantly chat with the AI, and data volume is no longer a concern.\n\nBika.ai saves time by doing tasks automatically and making work more accurate. Users can effortlessly publish, share, and replicate automation templates, making it easy to keep improving. Bika.ai is an ideal solution for businesses wanting to simplify marketing, sales, project management operations and enhance data handling through AI automation.',
    video_ai_agent: 'https://www.youtube.com/embed/POLa4KmVtVo?si=EdkgoshBfbHbCmML',
    video_automation: 'https://www.youtube.com/embed/g0WOF2hkSH0?si=k_hZ-m0BDdcyuSVg',
    video_dashboard: 'https://www.youtube.com/embed/VsrUHkjbbbU?si=1K6XO_liycfxNyyc',
    video_database: 'https://www.youtube.com/embed/BdP9qskz89s?si=KbWMCzSFsu9OQzVG',
    video_documents: 'https://www.youtube.com/embed/XuWV2nSvvoA?si=vYmwS-JUxduCAJMd',
    video_forms: 'https://www.youtube.com/embed/Wi6scmIzDKE?si=ggjeyXaTI2KGjPbw',
    video_marketing: 'https://www.youtube.com/embed/3jolpKcb1Zo?si=W6Mf1B8FpZRXsvle',
    video_onboarding: 'https://www.youtube.com/embed/bP0vz0MdJr8?si=loeHf3ULPObo8Hbk',
    video_partners: 'https://www.youtube.com/embed/8ZJXN9uFfL0?si=6whBq2q_yZHVaP_l',
    video_product: 'https://www.youtube.com/embed/8ZJXN9uFfL0?si=6whBq2q_yZHVaP_l',
  },
  sort: {
    sort_setting: 'Sort setting',
    sort_title: 'Sort',
  },
  space: {
    advanced: 'Advanced',
    all_members: 'My team members (including subteams)',
    announcement: 'Space homepage announcement',
    default_space_name: 'My Space',
    email_domain: 'Email Domain',
    enter_announcement: 'Enter Announcement',
    features_list: 'Space features list',
    goto_space: 'Go to my space',
    group_members: 'My team members (excluding subteams)',
    home: {
      installed_templates: 'Installed templates',
      invite_members: 'Invite friends to join',
      set_space_announcement: 'Set space announcement',
      space_announcement: 'Space announcement',
      view_help_document: 'View help document',
      view_templates: 'See what templates are available',
    },
    import: 'Import data',
    import_description: 'You can directly import local files into the Bika Space.',
    integration: 'Integration',
    integrations: 'Integrations',
    members: 'Members',
    members_and_teams: 'Members & Teams',
    msg_go_to_space_settings: 'Edit and modify',
    msg_space_name_modified_success: 'Name change successful, changed to: {spaceName}',
    new_space: 'New space',
    no_data: 'No data',
    no_name: 'Unnamed',
    no_permission_content:
      'Please make sure that the link is correct and that you have access rights. If you have any questions, please contact the task publisher.',
    no_permission_title: 'No permission to view this task',
    preview_import: 'Preview the currently imported data',
    removal_from_space: 'Removal from space',
    removal_from_space_description: 'Are you sure you want to remove the member from the space?',
    role: 'Role',
    show_watermark: 'Show watermark',
    space: 'Space',
    space_creator: 'Creator',
    space_domain: 'Space domain',
    space_logo: 'Space logo',
    space_name: 'Space name',
    space_settings: 'Space settings',
    space_subdomain: 'Space subdomain',
    teams: 'Teams',
    unnamed: 'Unnamed space',
    watermark: 'Watermark',
    you_will_be_assigned_a_subdomain: 'You will be assigned a subdomain:',
  },
  tags: {
    completed: 'Completed',
    due: 'Expired',
    invalid: 'Invalid',
    pending: 'Pending',
    read: 'Read',
    rejected: 'Reject',
    request_changed: 'Change',
    review: 'Review',
    unread: 'Unread',
  },
  task: {
    cutoff_time: 'Cut-off Time',
    task: 'Task',
  },
  team: {
    create_team: 'Create team',
    delete_team: 'Delete team',
    delete_team_description:
      'Once the team is deleted, it cannot be restored. Are you sure you want to delete this team?',
    delete_teams: 'Delete {count} team(s)',
    edit_team: 'Edit team name',
    join_team_description:
      'Team members who join through this link will be automatically assigned to groups and roles.',
    menu_remove_member_from_space: 'Remove from space',
    menu_remove_member_from_team: 'Remove from team',
    msg_add_member_success: 'Add member successfully',
    msg_create_team_success: 'Create team successfully',
    msg_delete_team_error: 'Failed to delete team',
    msg_delete_team_success: 'Successfully deleted team',
    msg_remove_member_from_space_success: 'Remove member(s) from space successfully',
    msg_remove_member_from_team_success: 'Remove member(s) from team successfully',
    msg_rename_team_success: 'Rename team successfully',
    msg_team_name_not_empty: 'Please enter a team name',
    placeholder_new_team: 'Please enter a team name',
    placeholder_select_members: 'Please select the members to add to the current team',
    remove_index_members: 'Remove {{index}} Members',
    remove_member_from_space: 'Remove members from space',
    remove_member_from_space_description:
      'Members will be removed but their history, comments, uploads, missions, and everything else will remain and will not be deleted. The removed members may still appear in search and filters.',
    remove_member_from_team: 'Remove members from team',
    remove_member_from_team_description: 'Are you sure you want to remove the member from the team?',
    select_team: 'Select teams',
    show_ai: 'Show AI',
    show_all: 'Show All',
    show_member: 'Show Members',
    team: 'Team',
    teams: 'Teams',
    unselect_all: 'Unselect All',
  },
  template: {
    ai_create: 'AI Consultant Team',
    architecture: 'Workflow Graph',
    architecture_description: 'Workflow of {name}',
    change_log: 'Changelog',
    check_original_template: 'Check Original Template',
    coming_soon:
      'The template is still in development and will be launched soon. you can ask author if you have any ideas or suggestions',
    coming_soon_tooltip: 'Template is being made, please stay tuned',
    comments: 'Comments',
    delete_template: 'Delete Template',
    delete_template_description: 'Are you sure you want to delete the template "{name}"?',
    delete_template_success: 'Successfully deleted the template "{name}"',
    empty_change_log: 'No changelog',
    export: 'Export',
    favorite: 'Favorite',
    feedback_email: 'Your email',
    feedback_email_placeholder: 'Please enter your email',
    feedback_ideas: 'What are your ideas?',
    feedback_placeholder: 'Enter your ideas or suggestions',
    feedback_thanks: 'Thank you for your feedback. I will notify you as soon as the template is online.',
    get: 'Install',
    install: 'Install',
    install_template: 'Install Template',
    install_toast: 'Template installed',
    make_it_faster: 'Make it faster',
    no_readme: 'Author has not write any README',
    no_template: 'No template under the current category',
    not_found_template: "Can't find the template you want? Please tell us",
    official_certification: 'Official Certification',
    open: 'Open',
    read_more: '→ Read more about what is Bika.ai',
    readme: 'Readme',
    release_notes: 'Release notes',
    release_notes_description: 'Release notes of {name}',
    releases_history: 'Releases History',
    select_one_space: 'Select a space',
    select_space: 'Space',
    star_success: 'Starred successfully',
    template: 'Template',
    title: 'Templates',
    try_other_templates: 'Try other templates',
    unstar_success: 'Unstarred successfully',
    upgrade: 'Upgrade',
    website_description:
      'Discover powerful <%= category %> Business AI Agents and Automation Workflows. Let AI work for you to save time and optimize results.',
    website_title:
      'Top <%= count %> <%= category %> Business AI Agents Templates & Database Workflows <%= year %> | Bika.ai',
  },
  theme: {
    colorful_theme: 'Colorful Theme',
    dark: 'Dark',
    light: 'Light',
    single_color_gradient_theme: 'Monochrome Gradient Theme',
    system: 'System',
    theme: 'Theme',
    theme_blue: 'Blue',
    theme_brown: 'Brown',
    theme_color_1: 'Theme Color 1',
    theme_color_2: 'Theme Color 2',
    theme_color_3: 'Theme Color 3',
    theme_color_4: 'Theme Color 4',
    theme_deepPurple: 'Dark purple',
    theme_green: 'Green',
    theme_indigo: 'Indigo',
    theme_orange: 'Orange',
    theme_pink: 'Pink',
    theme_purple: 'Purple',
    theme_red: 'Red',
    theme_tangerine: 'Orange',
    theme_teal: 'Blue-green',
    theme_yellow: 'Yellow',
  },
  time: {
    hour: 'Hour',
    minute: 'Minute',
  },
  tips: {
    drop_files_here: 'Drop files here',
    empty: 'Empty',
    invalid_file_type_error: 'Invalid file type(s): {invalidFileNames}. Accepted types: {uploadAccept}',
    setting_announcement: 'Setting announcement',
  },
  todo: {
    complete_all: 'Complete all',
    create: 'Create a todo',
    create_todo: 'Create mission',
    finished: 'Completed',
    my: 'My',
    my_created: 'My created',
    no_todo_so_far: 'No todo so far',
    overdue: 'Expired',
    pending: 'Pending',
    recent: 'Recent',
    rejected: 'Rejected',
    today: 'Today',
    todo: 'ToDo',
    todos: 'ToDos',
    unfinished: 'Unfinished',
  },
  toolbar: {
    hide_all: 'Hide all',
    hide_fileds: 'Hide fields',
    hide_kanban_grouping: 'Hide group',
    previous: 'Previous',
    show_all: 'Show all',
  },
  top_up: {
    choose_top_up_amount: 'Choose top-up amount',
    no_balance: 'Your balance is insufficient',
    read_and_accept_toc: 'Read and accept the terms of service',
    top_up: 'Top-up',
    top_up_success: 'Top-up successful',
    your_bika_coins: 'Your credits',
  },
  trash: {
    delete: 'Delete',
    delete_description: 'Once permanently deleted, it cannot be restored',
    delete_title: 'Permanently Delete Item',
    recover: 'Restore',
    recover_description: 'This item will be restored to its original path',
    recover_success: 'Restore successfully',
    recover_title: 'Restore Item',
    trash: 'Trash',
  },
  tutorial: 'Tutorial',
  unit: {
    pcs: 'piece',
    row: 'row',
    to: {
      admin: 'Admin',
      admin_description: 'All administrators within the space',
      all_members: 'All Members',
      all_members_description: 'All members within the space',
      current_operator: 'Current Operator',
      current_operator_description: 'Current operator',
      email_field: 'Email Field',
      email_field_description: 'Select the emails in a Email field in the database',
      email_string: 'Email String',
      email_subject_and_content: 'Email subject and content',
      member_field: 'Member field in database',
      member_field_description: 'Select the member in a Member field in the database',
      recipient: 'Recipient',
      recipient_and_more: 'Recipients and more',
      recipient_description: 'Select specified member, role, or team',
      role_select_label: 'Select role',
      show_more_options: 'Show more options',
      specify_members_description: 'Select members, teams or roles',
      specify_units: 'Members, teams, roles',
      specify_units_description: 'Select members, teams, or roles, does not support selecting creator, updater',
      team_select_label: 'Select team',
      to_title: 'Find',
      unit_member: 'Member',
      unit_member_description: 'Select one or more specified members',
      unit_role: 'Specify role',
      unit_role_description: 'Select one or more specified roles',
      unit_team: 'Specify team',
      unit_team_description: 'Select one or more specified teams',
      user: 'User',
      user_description: 'User in the space, such as creator, updater',
      user_select_label: 'Select user',
    },
  },
  unit_selected_modal: {
    empty_data: 'No data available',
    guest: 'Guest',
    organization: 'Organization',
    role: 'Role',
    selected_team: 'Selected',
  },
  upgrade: {
    upgrade: 'Upgrade',
    upgrade_title: 'Upgrade space',
    upgrade_to_pro: 'Upgrade to Pro',
    upgrade_to_pro_button: 'Upgrade to Pro',
    upgrade_to_pro_description: 'Unlock more features by upgrading to Pro',
  },
  user: {
    about: 'About',
    account: 'Account',
    avatar: 'Avatar',
    bind_email: 'Bind Email',
    bind_phone: 'Bind Phone',
    change_password: 'Change Password',
    confirm_password: 'Confirm Password',
    current_password: 'Current Password',
    custom_colors: {
      custom: 'Custom',
      default: 'Default',
      label: 'Custom colors (Beta)',
    },
    debug: 'Debug',
    download: 'Download App',
    email: 'Email',
    email_already_bound: 'Email is already bound',
    email_bind_success: 'Successful',
    email_send_success: 'Success',
    enter_email: 'Please enter your email',
    enter_phone: 'Please enter your phone number',
    enter_verification_code: 'Please enter verification code',
    get_verification_code: 'Get Code',
    invite_your_friends_to_register_and_get_1000_bk_coins: 'Invite your friends to register and get 1,000 credits',
    language: 'Language',
    loading: 'Loading...',
    member_name: 'Member name',
    name: 'Name',
    new_email: 'New Email',
    new_password: 'New Password',
    nickname: 'Nick name',
    no_email: 'No Email',
    no_name: 'No Name',
    password: 'Password',
    personal_info: 'Personal Info',
    personal_settings: 'My settings',
    phone: 'Phone',
    phone_already_bound: 'Phone is already bound',
    phone_bind_success: 'Successful',
    phone_send_success: 'Success',
    preference: 'Preferences',
    profile: 'Profile',
    sessions_current: 'Current',
    settings: 'Settings',
    sign_out: 'Sign out',
    theme: 'Theme mode',
    theme_style: {
      bika: 'Bika',
      dracula: 'Dracula (Purple)',
      label: 'Theme style',
      solarized: 'Solarized (Green)',
    },
    timezone: 'Timezone',
    update_email: 'Update Email',
    updated: 'Update succeeded',
    verification_code: 'Verification Code',
    verification_code_send_success: 'Verification code sent successfully',
    verify_email: 'Verify Email',
    website: 'Website',
  },
  website: {
    about_bika: 'About Bika',
    api_doc: 'API documentation',
    architecture_of: 'Workflow of <%= name %>',
    blog: 'Blog',
    blog_description: 'Discover how to build your own agentic AI company with Bika.ai, featuring tips and insights.',
    blog_title: 'Bika.ai Blog',
    change_region: 'Change Language',
    coming_soon: 'This Feature Is Coming Soon',
    compares: 'Product Comparison',
    contact: {
      contact_discord: 'Join Discord Community',
      contact_email: 'Contact',
      contact_form: 'Contact us via Form',
      contact_us: 'Contact Us',
      discord: 'Discord',
      discourse_community: 'Community',
      done_button: 'I have contacted',
      email: 'Email',
      line: 'LINE',
      more: 'More contact methods',
      sales: 'Contact Sales',
      scan_code: 'Please use WeChat to scan the QR code for inquiries',
      support: 'Contact Support',
      via_discord: 'Join Our Discord Community',
      via_email: 'Contact us via Email: <EMAIL>',
      via_line: 'Please scan our LINE QR Code',
      via_sales: 'Contact Sales',
      via_support: 'Bug Report & Feature Request',
      via_wecom: 'Please scan our WeChat QR Code',
      wechat: 'WeChat',
    },
    contact_sales: 'Contact sales',
    contact_service: 'Contact customer service',
    create_template_with_ai: 'Create Apps with AI',
    discover: 'Discover',
    help: 'Help',
    help_center: 'Help center',
    help_video: 'Tutorial videos',
    install_selfhosted: 'Donwload & Install',
    other_solutions: 'More Solutions and Use Cases',
    price: 'Pricing',
    pricing: {
      description:
        'Bika.ai provides flexible pricing options including self-hosted, on-premise, and dedicated cloud plan to cater to your specific requirements. Select the plan that best fits your needs.',
      title: 'Pricing',
    },
    search_or_build_ai_app_placeholer: 'Search or tell AI what you want?',
    template: 'Template',
    video: {
      marketing: '1.What is Bika.ai?',
      onboarding: '2. Quick start',
      product: '3. Dive into',
    },
    visit_website: 'Visit official website',
  },
  welcome: {
    explore_templates: 'Explore AI Agent Templates',
    get_started: 'Get Started',
    message:
      'Nice to meet you~ Bika.AI is an AI organizer that allow you to chat, build, manage agentic AI teams to build your own one-person AI company.',
    mobile_get_started_description: 'Visit {{web}} to use web version',
    more: 'You can download the mobile app of bika.ai',
    title: "HI, I'm Bika",
  },
  widget_config_invalid: 'The widget configuration is invalid, please reselect',
  widget_database_has_delete: 'The related database was deleted',
  widget_no_data: 'No data',
  widget_settings: {
    add_number: 'Add number',
    add_summary_description: 'Add summary description',
    add_target_value: 'Add target value',
    all_records: 'All records',
    chart_option_database_had_been_deleted: 'The database is invalid, please select again',
    chart_option_field_had_been_deleted: 'The field is invalid, please select again',
    chart_option_view_had_been_deleted: 'The view is invalid, please select again',
    column_dimension_field: 'Column dimension',
    column_dimension_sort_config: 'Sets the sorting of column',
    exclude_zero_point: 'Exclude zero point',
    jump_link_url: 'Go to linked database',
    more_settings: 'More settings',
    null: '[None]',
    options_config: {
      aggregation_by_field: 'Aggregate by field',
      ai_write: 'AI Write',
      asc: 'Ascending',
      avg: 'Average',
      bar_chart: 'Bar chart',
      count_records: 'Count records',
      custom: 'Custom',
      database: 'Built-in database',
      default: 'Default',
      desc: 'Descending',
      filled: 'Filled',
      hidden: 'Hidden',
      line_chart: 'Line chart',
      max: 'Maximum',
      min: 'Minimum',
      mysql: 'MySQL',
      none: 'None',
      not_filled: 'Not filled',
      percent_empty: 'Percent empty',
      percent_filled: 'Percent filled',
      percent_unique: 'Percent unique',
      pie_chart: 'Pie chart',
      postgresql: 'PostgreSQL',
      sort_by_x: 'Sort by X',
      sort_by_y: 'Sort by Y',
      sum: 'Sum',
      unique: 'Unique',
    },
    row_dimension_field: 'Row dimension',
    row_dimension_sort_config: 'Sets the sorting of row',
    select_axis_sort: 'Select an axis to sort by',
    select_chart_type: 'Select chart type',
    select_data_source: 'Select data source',
    select_statistics_field: 'Select a field for statistics',
    select_statistics_type: 'Select a statistics type',
    select_theme_color: 'Select theme color',
    select_view_source: 'Select a view as data source',
    select_widget_metrics_types: 'Select a field for summary',
    select_widget_type: 'Select widget type',
    separeted_multi_value: 'Separated multi value',
    set_sort_rules: 'Set sort rules',
    show_data_tips: 'Show data tips',
    show_empty_values: 'Show empty values',
    show_totals: 'Show totals',
    summary_by_field: 'Summarize by',
    widget_Value_x: 'Value (X-axis)',
    widget_Value_y: 'Value (Y-axis)',
    widget_dimension: 'Dimension (X-axis)',
    widget_name: 'Widget name',
    widget_operate_delete: 'Delete widget',
    widget_url_config: 'URL configuration',
  },
  widget_title_agenda: 'Agenda',
  widget_title_report: 'Recent reports',
  widget_title_todo: 'Recent to-dos',
  wizard: {
    agent_engineer: 'Agent Engineer',
    ai_wizard: 'AI Wizard',
    ai_wizard_description:
      'The AI Wizard is an intelligent guidance system designed to help users fully understand and master product features. Through a user-friendly interface and step-by-step introduction, the AI Wizard will guide you to become familiar with the usage methods and application scenarios of each module.',
    already_freshed: 'Refreshed',
    an_error_occured: 'An error has occurred',
    are_sure_to_top_up: 'Confirm Top-up',
    bika_tip1_description:
      'Bika.AI subscription service is officially launched! The current workspace is a free version and has been gifted {count}. Using Bika.AI features will consume credits.',
    bika_tip1_title: '🚀 Membership Plans Launch',
    bika_tip2_description:
      'Bika.AI subscription service is officially launched! The current workspace is a free version and has been gifted 500 credits. Using Bika.AI features will consume credits.',
    bika_tips1:
      "Bika.AI continues to grow thanks to your passion and creativity～Now, we're launching membership plans and feature upgrades to bring every creator a more breakthrough AI experience!",
    bika_upgraded_tip2_description:
      'Your invite code is ready! When new users register with your invite code, you and your friends will each get {count}. Come and invite your friends to join!',
    bika_upgraded_tip2_title: '👫 Invite Friends, Earn Points',
    build_scratch_description: 'Getting Started by Building from Scratch',
    build_scratch_name: '🏗️ Build from Scratch',
    check_invite_code: 'View Invite Code',
    choose_tailor_myself:
      'I am familiar with Bika.ai and wish to build my own AI agents and resources from scratch with full customization.',
    close_conversation: 'Close Conversation',
    contact_label: 'Your other contact information? Such as phone number, Whatsapp, LinkedIn, Twitter, etc.',
    create_space: 'Create Space',
    creating: 'Creating',
    credits: '{sum} Credits',
    fill_write_code:
      'Please enter a valid invitation code to enjoy all the exciting content of the system and start your exclusive experience journey ~',
    filter_by_columns: 'Filtered by {Count} columns',
    finance: '📊 Finance',
    finance_description: 'Agentic apps for assets tracker, HR resume agent',
    get_invite_code: 'Go to community to get invite code',
    industry_job_label: 'What industry are you currently working in? What is your job position?',
    installation_completed: 'Installation completed',
    installer: 'Installer',
    invite_code_added: 'Invitation filled successfully, reward received',
    invite_coin_description: "Fill in someone else's invitation code, and both parties can get {count}",
    marketing: '📣 Marketing',
    marketing_description:
      'Agentic apps for marketing, includes AI Writer, Slides Agent, AI Designer, Email Marketer, X(Twitter) Social Manager',
    membership_modal_title: 'Bika.AI Brand New Upgrade!',
    mirror_from: 'Mirror from',
    my_credits: 'My Credits',
    navigate_premium_plan: 'Learn about premium plan',
    new_gift_detail:
      'Thank you for your support! Bika.AI is giving you an extra {count}, please accept this gesture of appreciation～',
    new_wizard_created: 'New AI Chat is created',
    official_website: 'Official Website',
    onboarding: 'Onboarding',
    one_person_company: '👨‍💻 One-person Company',
    one_person_company_description:
      'Agentic apps for one-person company, includes AI Writer, Slides Agent, AI Designer, AI Programmer, ',
    placeholder_invite_code: 'Please fill in the invitation code',
    please_change_invite_code: 'Please change the invite code and try again.',
    product: '📋 Product',
    product_description: 'Agentic apps for product, includes AI Writer, Email Marketer, X(Twitter) Social Manager',
    quick_start: 'Quick Start',
    row_group_by_columns: 'Already grouped by {Count} columns',
    sales: '💼 Sales',
    sales_description:
      'Agentic apps for sales, includes AI Writer, Email Marketer, X(Twitter) Social Manager, Github Issues Creators',
    select_role: 'Please select your role. We will install the preset Agentic Apps template for you.',
    selected_templates: 'The following templates will be installed',
    some_columns_hided: 'Hidden {Count} columns',
    sorted_by_columns: 'Sorted by {Count} columns',
    support: '🤝 Support',
    support_description: 'Agentic apps for support, includes AI Writer, Email Marketer',
    topup_space_by_credits: 'You currently have {count}, recharge all to "{spaceName}"',
    topup_to_space: 'Top up to space',
    use_case_label: 'What specific use cases are you hoping to solve by using Bika.ai?',
    welcome: 'Welcome to Bika.AI',
    welcome_website: 'Welcome to Bika.ai',
    wizard: 'Wizard',
    wizard_new_user_gift: 'Exclusive benefits for new users!',
  },
};
export default dict;

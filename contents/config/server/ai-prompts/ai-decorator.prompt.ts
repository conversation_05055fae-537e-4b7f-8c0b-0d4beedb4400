import _ from 'lodash';

const filePromptDecorator = `
<attachment-list>
<% _.forEach(urls, function(url) { %><li><%- url %></li><% }); %>
</attachment-list>
`;

const systemTimePromptDecorator = `
<system-time>
Current date and time: <%= systemTime %>
</system-time>
`;

export const getDecoratorPrompt = (param: { attachments: string[] }) => {
  const { attachments } = param;
  const attachmentDecorator = attachments.length ? _.template(filePromptDecorator)({ urls: attachments }) : '';

  const systemTimeDecorator = _.template(systemTimePromptDecorator)({ systemTime: new Date().toISOString() });
  return attachmentDecorator + systemTimeDecorator;
};

import {
  BillingPlanFeatureConfigMapSchema,
  BillingPlanFeatureConfigMap,
  BillingPlanFeatureBooleanValue,
  BillingPlanFeatureMap,
  BillingPlanFeatureConfig,
} from '@bika/types/pricing/bo';
import featureConfig from './feature.json';

let _cacheFeatureConfigs: BillingPlanFeatureConfigMap | undefined;

/**
 * lazy load sku configs
 * @returns
 */
export const getFeatureConfigMap = () => {
  if (_cacheFeatureConfigs) return _cacheFeatureConfigs!;
  _cacheFeatureConfigs = BillingPlanFeatureConfigMapSchema.parse(featureConfig);
  return _cacheFeatureConfigs!;
};

const transformBoolean = (value: BillingPlanFeatureBooleanValue): boolean => {
  if (value === 'COMING_SOON') return false;
  if (value === 'YES') return true;
  if (value === 'NO') return false;
  return false;
};

export const featureConfigToMap = (config: BillingPlanFeatureConfig): BillingPlanFeatureMap => ({
  SEATS: config.seats,
  GUESTS: config.guests,
  STORAGES: config.storage,
  RECORDS_PER_SPACE: config.recordsPerSpace,
  RECORDS_PER_DATABASE: config.recordsPerDatabase,
  RESOURCES: config.resources,
  MISSIONS: config.missions,
  REPORTS: config.reports,
  MANAGED_EMAILS: config.managedEmails,
  AUTOMATION_RUNS: config.automationRuns,
  AUTOMATION_RUN_HISTORY: config.automationRunsHistory,
  // STANDARD_AI: config.standardAI,
  // ADVANCED_AI: config.advancedAI,
  CREDITS_PER_SEAT: config.creditsPerSeat,
  SPACE_INTEGRATIONS: config.spaceIntegrations,
  API_REQUEST: config.apiRequest,
  OPENAPI_RATE: config.openapiRate,
  SPACE_SESSIONS_LOGS: config.spaceSessionsLogs,
  SPACE_AUDIT_LOGS: config.spaceAuditLogs,
  SMTP_EMAILS: config.smtpEmails,
  TOTAL_SMS_NOTIFICATIONS: config.sms,
  RESOURCE_PERMISSION: config.resourcesPermission,

  VIEW_RESOURCE: transformBoolean(config.viewResource),
  SUB_ADMINS: transformBoolean(config.subAdmins),
  USER_SESSIONS_LOGS: transformBoolean(config.userSessionsLogs),
  EMAIL_SUPPORT: transformBoolean(config.emailSupport),
  IM_SUPPORT: transformBoolean(config.imSupport),
  PROFESSIONAL_SERVICES: transformBoolean(config.professionalServices),
  BROWSER_NOTIFICATIONS: transformBoolean(config.browserNotifications),
  MOBILE_NOTIFICATIONS: transformBoolean(config.mobileNotifications),
  PUBLIC_SHARE: transformBoolean(config.publishShare),
  RESTRICT_DOMAINS: transformBoolean(config.restrictDomains),
  PUBLISH_TEMPLATE: transformBoolean(config.publishTemplate),
  COMMUNITY: transformBoolean(config.community),
  HELP_CENTER: transformBoolean(config.helpCenter),
  WEBINAR: transformBoolean(config.webinar),

  AUTOMATION_INTEGRATIONS: transformBoolean(config.automationIntegrations),
  ADVANCED_AUTOMATION_INTEGRATIONS: transformBoolean(config.advancedAutomationIntegrations),
  BRING_YOUR_OWN_AI_KEY: transformBoolean(config.bringYourOwnAIKey),
  REMOVE_LOGOS: transformBoolean(config.removeLogos),
  DATA_SYNCER: transformBoolean(config.syncer),
  SUB_DOMAIN: transformBoolean(config.subDomain),
  CUSTOM_DOMAIN: transformBoolean(config.customDomain),
  EXPORT_EXCEL_CSV: transformBoolean(config.exportExcel),
  EXPORT_BIKA: transformBoolean(config.exportBika),
  SELL_TEMPLATE: transformBoolean(config.sellTemplate),
  PRIVATE_TEMPLATE: transformBoolean(config.privateTemplate),
  SELF_HOSTED: transformBoolean(config.selfHosted),
});

export const getPlanFeature = (plan: string): BillingPlanFeatureMap => {
  const config = getFeatureConfigMap()[plan];
  if (!config) {
    throw new Error(`plan ${plan} not found in feature config`);
  }
  return featureConfigToMap(config);
};

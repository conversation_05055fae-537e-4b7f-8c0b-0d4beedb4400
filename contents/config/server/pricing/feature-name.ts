import {
  BillingBooleanPlanFeature,
  BillingBooleanPlanFeatureSchema,
  BillingDatePlanFeature,
  BillingDatePlanFeatureSchema,
  BillingFixNumberPlanFeature,
  BillingFixNumberPlanFeatureSchema,
  BillingPlanFeature,
  BillingUsagePlanFeature,
  BillingUsagePlanFeatureSchema,
} from '@bika/types/pricing/bo';
import { iStringRecord } from '@bika/types/system';

// 这里管理订阅相关功能的名称, 其他地方别再重复维护, 保证统一性维护

const BooleanPlanFeatureName: Record<BillingBooleanPlanFeature, iStringRecord> = {
  BROWSER_NOTIFICATIONS: {
    en: 'Browser Notifications',
    'zh-CN': '浏览器通知',
    'zh-TW': '瀏覽器通知',
    ja: 'ブラウザ通知',
  },
  AUTOMATION_INTEGRATIONS: {
    en: 'Automation Integrations',
    'zh-CN': '自动化集成',
    'zh-TW': '自動化集成',
    ja: '自動化インテグレーション',
  },
  ADVANCED_AUTOMATION_INTEGRATIONS: {
    en: 'Advanced Automation Integrations',
    'zh-CN': '高级自动化集成',
    'zh-TW': '高級自動化集成',
    ja: '高度な自動化インテグレーション',
  },
  BRING_YOUR_OWN_AI_KEY: {
    en: 'Bring Your Own Key',
    'zh-CN': '自定义密钥',
    'zh-TW': '自定義密鑰',
    ja: '独自のキーを持参',
  },
  PUBLIC_SHARE: {
    en: 'Publish And Share',
    'zh-CN': '发布与分享',
    'zh-TW': '發布與分享',
    ja: '投稿して共有する',
  },
  SUB_ADMINS: {
    en: 'Sub Admins',
    'zh-CN': '子管理员',
    'zh-TW': '子管理員',
    ja: 'サブ管理者',
  },
  SUB_DOMAIN: {
    en: 'Sub Domain',
    'zh-CN': '子域名',
    'zh-TW': '子網域',
    ja: 'サブドメイン',
  },

  RESTRICT_DOMAINS: {
    en: 'Customize Authorized Email Domains',
    'zh-CN': '自定义授权邮箱域名',
    'zh-TW': '自定義授權電子郵件網域',
    ja: 'カスタマイズされた承認メールドメイン',
  },
  EXPORT_EXCEL_CSV: {
    en: 'Export Excel/CSV',
    'zh-CN': '导出 Excel/CSV',
    'zh-TW': '匯出 Excel/CSV',
    ja: 'Excel/CSVをエクスポート',
  },
  EXPORT_BIKA: {
    en: 'Export Bika File',
    'zh-CN': '导出 Bika 文件',
    'zh-TW': '匯出 Bika 文件',
    ja: 'Bikaファイルをエクスポート',
  },
  PUBLISH_TEMPLATE: {
    en: 'Publish Template',
    'zh-CN': '发布模板',
    'zh-TW': '發布模板',
    ja: 'テンプレートを公開',
  },
  SELL_TEMPLATE: {
    en: 'Sell Template',
    'zh-CN': '出售模板',
    'zh-TW': '出售模板',
    ja: 'テンプレートを販売',
  },
  PRIVATE_TEMPLATE: {
    en: 'Private Template',
    'zh-CN': '私有模板',
    'zh-TW': '私有模板',
    ja: 'プライベートテンプレート',
  },
  COMMUNITY: {
    en: 'Community Support',
    'zh-CN': '社区支持',
    'zh-TW': '社區支持',
    ja: 'コミュニティサポート',
  },
  HELP_CENTER: {
    en: 'Help Center',
    'zh-CN': '帮助中心',
    'zh-TW': '幫助中心',
    ja: 'ヘルプセンター',
  },
  WEBINAR: {
    en: 'Webinar',
    'zh-CN': '网络研讨会',
    'zh-TW': '網絡研討會',
    ja: 'ウェビナー',
  },
  EMAIL_SUPPORT: {
    en: 'Email Support',
    'zh-CN': '电子邮件支持',
    'zh-TW': '電子郵件支持',
    ja: 'メールサポート',
  },
  IM_SUPPORT: {
    en: 'Whatsapp Support',
    'zh-CN': '企微专属客服',
    'zh-TW': 'Whatsapp專屬客服',
    ja: 'WhatsAppサポート',
  },
  PROFESSIONAL_SERVICES: {
    en: 'Professional Services',
    'zh-CN': '专属 V+ 顾问',
    'zh-TW': '專屬 V+ 顧問',
    ja: 'プロフェッショナルサービス',
  },
  SELF_HOSTED: {
    en: 'Self-hosted & White-label',
    'zh-CN': '在自己的服务器上部署 Bika，甚至可以将安装实例进行白标（即去除品牌标识，进行品牌定制）',
    'zh-TW': '在自己的伺服器上部署 Bika，甚至可以將安裝實例進行白標（即去除品牌標識，進行品牌定制）',
    ja: 'セルフホスティング',
  },

  // 以下关闭显示
  VIEW_RESOURCE: {
    en: 'Mirror',
    'zh-CN': '镜像',
    'zh-TW': '鏡像',
    ja: 'ミラー',
  },
  MOBILE_NOTIFICATIONS: {
    en: 'Mobile Notifications',
    'zh-CN': '移动通知',
    'zh-TW': '移動通知',
    ja: 'モバイル通知',
  },
  REMOVE_LOGOS: {
    en: 'Remove Logos',
    'zh-CN': '移除Logo',
    'zh-TW': '移除Logo',
    ja: 'ロゴを削除',
  },
  DATA_SYNCER: {
    en: 'Data Sync',
    'zh-CN': '数据同步',
    'zh-TW': '數據同步',
    ja: 'データ同期',
  },
  CUSTOM_DOMAIN: {
    en: 'Custom Domain',
    'zh-CN': '自定义域名',
    'zh-TW': '自定義網域',
    ja: 'カスタムドメイン',
  },
  USER_SESSIONS_LOGS: {
    en: 'User Sessions Logs',
    'zh-CN': '用户会话日志',
    'zh-TW': '用戶會話日誌',
    ja: 'ユーザーセッションログ',
  },
};

const DatePlanFeatureName: Record<BillingDatePlanFeature, iStringRecord> = {
  AUTOMATION_RUN_HISTORY: {
    en: 'Automation Run History',
    'zh-CN': '自动化运行历史',
    'zh-TW': '自動化執行歷史',
    ja: '自動化実行履歴',
  },
  SPACE_SESSIONS_LOGS: {
    en: 'Space Sessions Logs',
    'zh-CN': '空间会话日志',
    'zh-TW': '空間會話日誌',
    ja: 'スペースセッションログ',
  },
  SPACE_AUDIT_LOGS: {
    en: 'Space Audit Logs',
    'zh-CN': '空间审计日志',
    'zh-TW': '空間審計日誌',
    ja: 'スペース監査ログ',
  },
};

const UsagePlanFeatureName: Record<BillingUsagePlanFeature, iStringRecord> = {
  SEATS: {
    en: 'Total Seats',
    'zh-CN': '席位数',
    'zh-TW': '席位數',
    ja: '席',
  },
  GUESTS: {
    en: 'Total Guests',
    'zh-CN': '访客数',
    'zh-TW': '访客数',
    ja: 'ゲスト',
  },
  STORAGES: {
    en: 'Total Storage',
    'zh-CN': '附件容量',
    'zh-TW': '附件容量',
    ja: 'ストレージ',
  },
  RECORDS_PER_SPACE: {
    en: ' Total Records',
    'zh-CN': '数据表总记录数',
    'zh-TW': '資料表總記錄數',
    ja: 'データベースごとのレコード数',
  },
  RECORDS_PER_DATABASE: {
    en: 'Records Per Database',
    'zh-CN': '单表记录数',
    'zh-TW': '單表記錄數',
    ja: 'データベースごとのレコード数',
  },
  RESOURCES: {
    en: 'Resources',
    'zh-CN': '资源数',
    'zh-TW': '資源數',
    ja: 'リソース数',
  },
  MISSIONS: {
    en: 'Missions Per Month',
    'zh-CN': '每月任务数',
    'zh-TW': '每月任務數',
    ja: '月間ミッション数',
  },
  REPORTS: {
    en: 'Reports Per Month',
    'zh-CN': '每月报表数',
    'zh-TW': '每月報表數',
    ja: '月間レポート数',
  },
  MANAGED_EMAILS: {
    en: 'Bika Email Service Send Volume Per Month',
    'zh-CN': '每月Bika邮件服务发送量',
    'zh-TW': '每月Bika郵件服務發送量',
    ja: '月間Bikaメールサービス送信量',
  },
  AUTOMATION_RUNS: {
    en: 'Automation Runs Per Month',
    'zh-CN': '每月自动化运行次数',
    'zh-TW': '每月自動化執行次數',
    ja: '月間自動化実行回数',
  },
  CREDITS_PER_SEAT: {
    en: 'AI Credits Per Seat',
    'zh-CN': '每个席位的AI积分',
    'zh-TW': '每個席位的AI積分',
    ja: '1席あたりのAIクレジット',
  },
  SPACE_INTEGRATIONS: {
    en: 'Space Integrations',
    'zh-CN': '空间集成数',
    'zh-TW': '空間集成數',
    ja: 'スペース統合数',
  },
  API_REQUEST: {
    en: 'API Request Per Month',
    'zh-CN': '每月API请求数',
    'zh-TW': '每月API請求數',
    ja: '月間APIリクエスト数',
  },
  SMTP_EMAILS: {
    en: 'Custom SMTP Emails',
    'zh-CN': '自定义SMTP邮件数',
    'zh-TW': '自定義SMTP郵件數',
    ja: 'カスタムSMTPメール数',
  },
  TOTAL_SMS_NOTIFICATIONS: {
    en: 'Total SMS Notifications',
    'zh-CN': '短信通知数',
    'zh-TW': '簡訊通知數',
    ja: '合計SMS通知',
  },
  RESOURCE_PERMISSION: {
    en: 'Assign Permission Resources',
    'zh-CN': '可分配权限资源数',
    'zh-TW': '可分配權限資源數',
    ja: 'リソース権限数',
  },
};

const FixedNumberPlanFeatureName: Record<BillingFixNumberPlanFeature, iStringRecord> = {
  OPENAPI_RATE: {
    en: 'OpenAPI Rate Limit',
    'zh-CN': 'OpenAPI速率限制',
    'zh-TW': 'OpenAPI速率限制',
    ja: 'OpenAPIレートリミット',
  },
};

export const getBooleanPlanFeatureName = (feature: BillingBooleanPlanFeature): iStringRecord =>
  BooleanPlanFeatureName[feature];

export const getDatePlanFeatureName = (feature: BillingDatePlanFeature): iStringRecord => DatePlanFeatureName[feature];

export const getUsagePlanFeatureName = (feature: BillingUsagePlanFeature): iStringRecord =>
  UsagePlanFeatureName[feature];

export const getFixedNumberPlanFeatureName = (feature: BillingFixNumberPlanFeature): iStringRecord =>
  FixedNumberPlanFeatureName[feature];

const isUsageFeature = (feature: BillingPlanFeature): feature is BillingUsagePlanFeature =>
  BillingUsagePlanFeatureSchema.options.some((option) => option === feature);

const isFixedNumberFeature = (feature: BillingPlanFeature): feature is BillingFixNumberPlanFeature =>
  BillingFixNumberPlanFeatureSchema.options.some((option) => option === feature);

const isSupportedFeature = (feature: BillingPlanFeature): feature is BillingBooleanPlanFeature =>
  BillingBooleanPlanFeatureSchema.options.some((option) => option === feature);

const isDateFeature = (feature: BillingPlanFeature): feature is BillingDatePlanFeature =>
  BillingDatePlanFeatureSchema.options.some((option) => option === feature);

export const getPlanFeatureName = (feature: BillingPlanFeature): iStringRecord => {
  if (isUsageFeature(feature)) {
    return getUsagePlanFeatureName(feature);
  }
  if (isFixedNumberFeature(feature)) {
    return getFixedNumberPlanFeatureName(feature);
  }
  if (isSupportedFeature(feature)) {
    return getBooleanPlanFeatureName(feature);
  }
  if (isDateFeature(feature)) {
    return getDatePlanFeatureName(feature);
  }
  return {};
};

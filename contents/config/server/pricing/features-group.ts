import pluralize from 'pluralize';
import { BillingPlanFeature } from '@bika/types/pricing/bo';
import { iString } from '@bika/types/system';
import {
  getBooleanPlanFeatureName,
  getDatePlanFeatureName,
  getFixedNumberPlanFeatureName,
  getUsagePlanFeatureName,
} from './feature-name';
import type { ILocaleContext } from '../../../i18n/context';

export type FeatureFormatterDefinition = {
  name: string;
  // 鼠标hover上去的时候的文字，问号提示
  tips?: string;
  key: BillingPlanFeature;
  customFormat?: (value: string | number) => string;
};

export type FeaturesGroupFormatterConfigType = {
  groupName: iString;
  features: FeatureFormatterDefinition[];
};

export function featuresGroupFormatterConfigs(locale: ILocaleContext): FeaturesGroupFormatterConfigType[] {
  const { t, i } = locale;
  return [
    {
      groupName: {
        en: 'Space',
        'zh-CN': '空间站',
        'zh-TW': '空間站',
        ja: 'スペース',
      },
      features: [
        {
          key: 'GUESTS',
          name: i(getUsagePlanFeatureName('GUESTS')),
        },
        {
          key: 'RECORDS_PER_SPACE',
          name: i(getUsagePlanFeatureName('RECORDS_PER_SPACE')),
        },
        {
          key: 'RECORDS_PER_DATABASE',
          name: i(getUsagePlanFeatureName('RECORDS_PER_DATABASE')),
        },
        {
          key: 'STORAGES',
          name: i(getUsagePlanFeatureName('STORAGES')),
          customFormat: (value: string | number) => `${value} GB`,
          tips: t.pricing.features.storage_tips,
        },
        {
          key: 'RESOURCES',
          name: i(getUsagePlanFeatureName('RESOURCES')),
          tips: t.resource.description,
        },
      ],
    },
    {
      groupName: {
        en: 'Missions & Notifications',
        'zh-CN': '任务 & 通知',
        'zh-TW': '任務 & 通知',
        ja: 'ミッション & 通知',
      },
      features: [
        {
          key: 'BROWSER_NOTIFICATIONS',
          name: i(getBooleanPlanFeatureName('BROWSER_NOTIFICATIONS')),
        },
        // {
        //   key: BillingBooleanPlanFeatureSchema.enum.MOBILE_NOTIFICATIONS,
        //   name: t.pricing.features.mobile_notifications,
        // },
        // {
        //   key: BillingUsagePlanFeatureSchema.enum.TOTAL_SMS_NOTIFICATIONS,
        //   name: t.pricing.features.sms_notifications,
        // },
        {
          key: 'MISSIONS',
          name: i(getUsagePlanFeatureName('MISSIONS')),
        },
        {
          key: 'REPORTS',
          name: i(getUsagePlanFeatureName('REPORTS')),
        },
        {
          key: 'MANAGED_EMAILS',
          name: i(getUsagePlanFeatureName('MANAGED_EMAILS')),
          tips: t.pricing.features.managed_emails_per_month_tips,
        },
        {
          key: 'SMTP_EMAILS',
          name: i(getUsagePlanFeatureName('SMTP_EMAILS')),
        },
      ],
    },
    {
      groupName: {
        en: 'AI & Automation',
        'zh-CN': 'AI & 自动化',
        'zh-TW': 'AI & 自動化',
        ja: 'AI & 自動化',
      },
      features: [
        {
          key: 'AUTOMATION_RUNS',
          name: i(getUsagePlanFeatureName('AUTOMATION_RUNS')),
        },
        {
          key: 'AUTOMATION_RUN_HISTORY',
          name: i(getDatePlanFeatureName('AUTOMATION_RUN_HISTORY')),
          customFormat: (value: string | number) => `${value} ${pluralize(t.resource.day, Number(value))}`,
        },
        {
          key: 'AUTOMATION_INTEGRATIONS',
          name: i(getBooleanPlanFeatureName('AUTOMATION_INTEGRATIONS')),
          tips: t.pricing.features.automation_integrations_tips,
        },
        {
          key: 'ADVANCED_AUTOMATION_INTEGRATIONS',
          name: i(getBooleanPlanFeatureName('ADVANCED_AUTOMATION_INTEGRATIONS')),
          tips: t.pricing.features.advanced_automation_integrations_tips,
        },
        {
          key: 'CREDITS_PER_SEAT',
          name: i(getUsagePlanFeatureName('CREDITS_PER_SEAT')),
          tips: t.pricing.features.credits_per_seat_per_month_tips,

          // generalHandler: formatPerMonth,
        },
        // 隐藏advanced
        // {
        //   name: t.pricing.features.advanced_ai_requests_per_month,
        //   key: 'advancedAI',
        // },
        {
          key: 'BRING_YOUR_OWN_AI_KEY',
          name: i(getBooleanPlanFeatureName('BRING_YOUR_OWN_AI_KEY')),
        },
      ],
    },
    {
      groupName: {
        en: 'Share & Permissions',
        'zh-CN': '分享 与 权限',
        'zh-TW': '分享 & 權限',
        ja: '共有 & 権限',
      },
      features: [
        {
          key: 'RESOURCE_PERMISSION',
          name: i(getUsagePlanFeatureName('RESOURCE_PERMISSION')),
        },
        {
          key: 'PUBLIC_SHARE',
          name: i(getBooleanPlanFeatureName('PUBLIC_SHARE')),
        },
        // {
        //   name: t.pricing.features.mirror_sync,
        //   key: BillingBooleanPlanFeatureSchema.enum.VIEW_RESOURCE,
        // },
        {
          key: 'REMOVE_LOGOS',
          name: i(getBooleanPlanFeatureName('REMOVE_LOGOS')),
          tips: t.pricing.features.remove_logos_tips,
        },
      ],
    },
    {
      groupName: {
        en: 'API & Integrations',
        'zh-CN': 'API 与 三方集成',
        'zh-TW': 'API & 三方集成',
        ja: 'API & インテグレーション',
      },
      features: [
        {
          key: 'SPACE_INTEGRATIONS',
          name: i(getUsagePlanFeatureName('SPACE_INTEGRATIONS')),
        },
        {
          key: 'OPENAPI_RATE',
          name: i(getFixedNumberPlanFeatureName('OPENAPI_RATE')),
          customFormat: (value: string | number) => `${value} QPS`,
        },
        {
          key: 'API_REQUEST',
          name: i(getUsagePlanFeatureName('API_REQUEST')),
        },
        {
          key: 'DATA_SYNCER',
          name: i(getBooleanPlanFeatureName('DATA_SYNCER')),
        },
      ],
    },
    {
      groupName: {
        en: 'Admin & Security',
        'zh-CN': '管理 与 安全',
        'zh-TW': '管理 & 安全',
        ja: '管理 & セキュリティ',
      },
      features: [
        {
          key: 'SUB_ADMINS',
          name: i(getBooleanPlanFeatureName('SUB_ADMINS')),
        },
        {
          key: 'SUB_DOMAIN',
          name: i(getBooleanPlanFeatureName('SUB_DOMAIN')),
        },
        {
          key: 'CUSTOM_DOMAIN',
          name: i(getBooleanPlanFeatureName('CUSTOM_DOMAIN')),
        },
        {
          key: 'RESTRICT_DOMAINS',
          name: i(getBooleanPlanFeatureName('RESTRICT_DOMAINS')),
        },
        // {
        //   name: t.pricing.features.user_sessions_log,
        //   key: BillingBooleanPlanFeatureSchema.enum.USER_SESSIONS_LOGS,
        // },
        // {
        //   key: 'SPACE_SESSIONS_LOGS',
        //   name: i(getDatePlanFeatureName('SPACE_SESSIONS_LOGS')),
        //   customFormat: (value: string | number) => `${value.toString()} ${pluralize(t.resource.day, Number(value))}`,
        // },
        {
          key: 'SPACE_AUDIT_LOGS',
          name: i(getDatePlanFeatureName('SPACE_AUDIT_LOGS')),
          customFormat: (value: string | number) => {
            const days = Number(value);
            if (days < 1) {
              // 小于1，折算小时
              return `${Math.round(days * 24).toString()} ${pluralize(t.automation.trigger.datetime_field_reached.hours, days * 24)}`;
            }

            return `${days.toString()} ${pluralize(t.resource.day, days)}`;
          },
        },
        {
          key: 'EXPORT_EXCEL_CSV',
          name: i(getBooleanPlanFeatureName('EXPORT_EXCEL_CSV')),
        },
        {
          key: 'EXPORT_BIKA',
          name: i(getBooleanPlanFeatureName('EXPORT_BIKA')),
          tips: t.pricing.features.export_bika_file_tips,
        },
      ],
    },
    {
      groupName: {
        en: 'Template & Apps',
        'zh-CN': 'AI应用 与 模板',
        'zh-TW': 'AI應用 & 模板',
        ja: 'AIアプリ & テンプレート',
      },
      features: [
        {
          key: 'PUBLISH_TEMPLATE',
          name: i(getBooleanPlanFeatureName('PUBLISH_TEMPLATE')),
        },
        {
          key: 'SELL_TEMPLATE',
          name: i(getBooleanPlanFeatureName('SELL_TEMPLATE')),
        },
        {
          key: 'PRIVATE_TEMPLATE',
          name: i(getBooleanPlanFeatureName('PRIVATE_TEMPLATE')),
        },
      ],
    },
    {
      groupName: {
        en: 'Services & Support',
        'zh-CN': '服务 与 支持',
        'zh-TW': '服務 & 支援',
        ja: 'サービス & サポート',
      },
      features: [
        {
          key: 'COMMUNITY',
          name: i(getBooleanPlanFeatureName('COMMUNITY')),
        },
        {
          name: i(getBooleanPlanFeatureName('HELP_CENTER')),
          key: 'HELP_CENTER',
        },
        {
          name: i(getBooleanPlanFeatureName('WEBINAR')),
          key: 'WEBINAR',
        },
        {
          name: i(getBooleanPlanFeatureName('EMAIL_SUPPORT')),
          key: 'EMAIL_SUPPORT',
        },
        {
          name: i(getBooleanPlanFeatureName('IM_SUPPORT')),
          key: 'IM_SUPPORT',
          tips: t.pricing.features.im_support_tips,
        },
        {
          key: 'PROFESSIONAL_SERVICES',
          name: i(getBooleanPlanFeatureName('PROFESSIONAL_SERVICES')),
        },
        {
          key: 'SELF_HOSTED',
          name: i(getBooleanPlanFeatureName('SELF_HOSTED')),
          tips: t.pricing.features.self_hosted_tips,
        },
      ],
    },
  ];
}

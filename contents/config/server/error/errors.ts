import { Locale } from '@bika/types/system';
import { AIErrors } from './ai_errors';
import { AutomationErrors } from './automation_errors';
import { BillingErrors } from './billing_errors';
import { CommonErrors } from './common_errors';
import { DatabaseErrors } from './database_errors';
import { MirrorErrors } from './mirror_errors';
import { MissionErrors } from './mission_errors';
import { NodeErrors } from './node_errors';
import { SpaceErrors } from './space_errors';
import { TrashErrors } from './trash_errors';
import { UnitErrors } from './unit_errors';
import { UserErrors } from './user_errors';
import { WidgetErrors } from './widget_errors';

// 错误定义(分组形式)
export const errors = {
  // 1000 - 1999
  common: CommonErrors,
  // 1100 - 1199
  trash: TrashErrors,
  // 2000 - 2999
  user: UserErrors,
  // 3000 - 3099
  space: SpaceErrors,
  // 3100 - 3199
  unit: UnitErrors,
  // 3200 - 3299
  node: NodeErrors,
  // 4100-4199
  database: DatabaseErrors,
  // 4200 - 4299
  automation: AutomationErrors,
  // 4300 - 4399
  mirror: MirrorErrors,
  // 5000 - 5099
  mission: MissionErrors,
  // 6000 - 6099
  widget: WidgetErrors,
  // 10000 - 10999
  billing: BillingErrors,
  // 11000 - 11999
  ai: AIErrors,
  // ...other error definitions
};

export type ErrorDefinition = typeof errors;
export type ErrorGroup = keyof ErrorDefinition;
export type ErrorName = keyof ErrorDefinition[ErrorGroup];
export type ErrorCode = number;

export type ErrorType = {
  code: number;
  message: { [key in Locale]: string };
};

// 索引映射：error code -> error object
const codeToNameMap = Object.keys(errors).reduce<Record<ErrorCode, ErrorType>>((acc, group) => {
  const groupError = errors[group as ErrorGroup];
  Object.keys(groupError).forEach((name) => {
    const e = groupError[name as keyof typeof groupError] as ErrorType;
    acc[e.code] = e;
  });
  return acc;
}, {});

// code -> error
export function getErrorFromCode(code: ErrorCode): ErrorType | undefined {
  return codeToNameMap[code];
}

export function getErrorMessage(code: ErrorCode, locale: Locale = 'en'): string | undefined {
  const error = getErrorFromCode(code);
  return error && error.message[locale];
}

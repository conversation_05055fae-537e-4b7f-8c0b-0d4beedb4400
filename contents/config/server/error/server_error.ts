import _ from 'lodash';
import { iStringParse, Locale } from '@bika/types/system';
import { transformFeatureLockedErrorData, transformUsageLimitErrorData } from '@bika/types/utils';
import { errors, ErrorType, getErrorFromCode } from './errors';
import { getPlanFeatureName, getUsagePlanFeatureName } from '../pricing/feature-name';
import { SpacePlanDescConfig } from '../pricing/plan-desc';

/**
 * 服务错误, 根据业务码快速抛出异常
 */
export class ServerError extends Error {
  private readonly _error: ErrorType;

  private _data?: Record<string, unknown>;

  constructor(error: ErrorType, data?: Record<string, unknown>) {
    const err = getErrorFromCode(error.code);
    if (!err) {
      throw new Error(`Unknown error code: ${error.code}`);
    }
    super(error.message.en);
    this._error = error;
    this._data = data;
  }

  get code() {
    return this._error.code;
  }

  getMessage(locale: Locale = 'en') {
    if (this.data) {
      if (this.code === errors.billing.usage_exceed_limit.code) {
        // 用量的Feature需要翻译
        const errorData = transformUsageLimitErrorData(this.data);
        if (errorData) {
          // 使用翻译后的订阅计划名称
          const i18nPlanName = SpacePlanDescConfig[errorData.plan].planName;
          const planName = iStringParse(i18nPlanName, locale);
          // 使用翻译后的Feature名称
          const i18nFeatureName = getUsagePlanFeatureName(errorData.feature);
          const featureName = iStringParse(i18nFeatureName, locale);
          // 追加featureName翻译名称到变量里
          this._data = { ...this._data, planName, featureName };
        }
      }
      if (this.code === errors.billing.feature_locked.code) {
        // 功能解锁
        const errorData = transformFeatureLockedErrorData(this.data);
        if (errorData) {
          // 使用翻译后的订阅计划名称
          const i18nPlanName = SpacePlanDescConfig[errorData.plan].planName;
          const planName = iStringParse(i18nPlanName, locale);
          // 使用翻译后的Feature名称
          const i18nFeatureName = getPlanFeatureName(errorData.feature);
          const featureName = iStringParse(i18nFeatureName, locale);
          // 追加featureName翻译名称到变量里
          this._data = { ...this._data, planName, featureName };
        }
      }
      const compiled = _.template(this._error.message[locale], {
        interpolate: /{([\s\S]+?)}/g,
      });
      return compiled(this.data);
    }
    return this._error.message[locale];
  }

  get data() {
    return this._data;
  }
}

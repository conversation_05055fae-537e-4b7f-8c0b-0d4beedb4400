/**
 * 订阅错误码: 10000 - 11000
 */
export const BillingErrors = {
  usage_exceed_limit: {
    code: 10000,
    message: {
      en: 'Your current subscription plan is {planName}. The maximum allowed {featureName} for this space is {max}, and you have currently used {current}. Please upgrade your plan to unlock a higher limit',
      'zh-CN':
        '您当前的订阅等级为{planName}, 该空间站的{featureName}上限值为{max}, 截止当前已使用数为{current}, 请升级计划即可解锁更多上限.',
      'zh-TW':
        '您目前的訂閱等級為{planName}，此空間站的{featureName}上限值為 {max}，截至目前已使用{current}。請升級方案以解鎖更多上限。',
      ja: '申し訳ありませんが、{featureName}の上限は{max}で、現在{current}を使用しています。プランをアップグレードして、より高い上限を解除してください。現在のご契約プランは {planName} です。本スペースステーションの {featureName} の上限値は {max} で、現在 {current} を使用しています。上限を増やすにはプランをアップグレードしてください。',
    },
  },
  feature_locked: {
    code: 10001,
    message: {
      en: 'Your current subscription plan {planName} has not unlocked the {featureName} feature yet. Upgrade now to enjoy more powerful features and premium services.',
      'zh-CN': '您的订阅计划 {planName} 尚未解锁 {featureName} 功能。立即升级，体验更多强大功能与优质服务',
      'zh-TW': '您的訂閱計劃 {planName} 尚未解鎖 {featureName} 功能。立即升級，體驗更多強大功能與優質服務。',
      ja: '現在ご利用中のサブスクリプションプラン {planName} では、{featureName} 機能がまだ有効になっていません。今すぐアップグレードして、より多くの便利な機能と優れたサービスをご体験ください。',
    },
  },
  ai_credit_not_enough: {
    code: 10002,
    message: {
      en: 'AI credit not enough for redeem',
      'zh-CN': 'AI 余额不足, 请充值.',
      'zh-TW': 'AI 餘額不足, 請充值.',
      ja: 'AI 残高が不足しています。プランをアップグレードしてください。',
    },
  },
};

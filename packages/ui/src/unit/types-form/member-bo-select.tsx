import React, { useState } from 'react';
import type { ILocaleContext } from '@bika/contents/i18n';
import type { INodeResourceApi } from '@bika/types/node/context';
import { useSpaceId } from '@bika/types/space/context';
import type { ToSpecifyUnits } from '@bika/types/unit/bo';
import { Box } from '@bika/ui/layouts';
import { Typography } from '@bika/ui/texts';
import { UnitPickFormComponent } from '@bika/ui/user';
import { ensureArray } from '@bika/ui/utils';
import { MemberSelectModal, SelectType } from './member-select-modal';
import { useSelectMemberUnitModal } from '../../database/filter/use-select-member-unit-modal';
import { FormLabel } from '../../form-components';

interface MemberBoSelectProps {
  locale: ILocaleContext;
  targets?: SelectType[];
  value: ToSpecifyUnits;
  multiple?: boolean;
  onChange: (newVal: ToSpecifyUnits) => void;
  api: INodeResourceApi;
  label?: string;
  required?: boolean;
}

export const MemberBoSelect: React.FC<MemberBoSelectProps> = (props) => {
  const { value, onChange, api, label, required, locale, targets, multiple } = props;
  const { t } = locale;
  const spaceId = useSpaceId();
  const {
    isShowModal,
    setIsShowModal,
    checkedOptions,
    dropdownOptions: originalDropdownOptions,
    setSearchText,
  } = useSelectMemberUnitModal(spaceId, ensureArray(value.unitIds));

  let dropdownOptions = originalDropdownOptions;
  if (props.targets) {
    dropdownOptions = originalDropdownOptions.filter((item) => props.targets?.includes(item.type));
  }

  const [cachedValue, setCacheValue] = useState(value.unitIds);

  return (
    <Box
      position="relative"
      display="flex"
      flex={1}
      borderRadius="4px"
      minHeight="40px"
      mt="4px"
      px="4px"
      sx={{
        backgroundColor: 'var(--bg-controls)',
      }}
    >
      {label && <FormLabel required={required}>{label}</FormLabel>}
      {!isShowModal && (
        <UnitPickFormComponent
          multiple={props.multiple}
          onSearch={(v) => {
            setSearchText(v);
          }}
          // variant="add"
          values={cachedValue}
          onOpenChange={(isOpen) => {
            if (!isOpen) {
              onChange?.({
                ...value,
                unitIds: cachedValue,
              });
              setSearchText('');
            }
          }}
          options={dropdownOptions}
          onChange={(newValues) => {
            setCacheValue(newValues);
            onChange({ ...value, unitIds: newValues });
          }}
          footer={
            <Box
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                setIsShowModal(true);
              }}
              sx={{
                display: 'flex',
                borderRadius: '4px',
                padding: '8px',
                height: '32px',
                marginBottom: '4px',
                justifyContent: 'center',
                alignItems: 'center',
                color: 'var(--text-secondary)',
                '&:hover': { cursor: 'pointer', backgroundColor: 'var(--hover)' },
              }}
            >
              <Typography level="b4">{t.buttons.see_more}</Typography>
            </Box>
          }
        />
      )}
      {isShowModal && (
        <MemberSelectModal
          multiple={props.multiple}
          selectedUnits={checkedOptions}
          targets={targets}
          onChange={(items) => {
            const v = items.map((item) => item.id);
            setCacheValue(v);
            onChange({
              ...value,
              unitIds: v,
            });
          }}
          onCancel={() => {
            setIsShowModal(false);
          }}
          setIsEdit={(isOpen) => {
            setIsShowModal(isOpen);
          }}
          api={api.unit}
          locale={locale}
        />
      )}
    </Box>
  );
};

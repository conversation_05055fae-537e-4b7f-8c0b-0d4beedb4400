'use client';

import type { ColorMode, NodeTypes } from '@xyflow/react';
import {
  ReactFlowProvider,
  ReactFlow,
  Background,
  useNodesInitialized,
  useReactFlow,
  applyNodeChanges,
  getNodesBounds,
} from '@xyflow/react';
import { useState, useEffect, CSSProperties } from 'react';
import { createPortal } from 'react-dom';
import { NodeResourceType } from '@bika/types/node/bo';
import type { EditorScreenProps } from '@bika/types/template/type';
import type { OrgChartUnit } from '@bika/ui/editor/flow-editor/nodes/unit';
import { Stack } from '@bika/ui/layouts';
import { FlowControl } from './control';
import { AIAgentNode } from './nodes/ai-agent';
import { AutomationNode } from './nodes/automation';
import { DashboardNode } from './nodes/dashboard';
import { DatabaseNode } from './nodes/database';
import { FolderNode } from './nodes/folder';
import { FormNode } from './nodes/form';
import { IntegrationNode } from './nodes/integration';
import { MirrorNode } from './nodes/mirror';
import { MissionNode } from './nodes/mission';
import { ReportNode } from './nodes/report';
import { UnitNode } from './nodes/unit';
import { TeamControl, TeamViewMode } from './team-control';
import { useFlowData, type IFlowEditorData } from './use-flow-data';

import '@xyflow/react/dist/style.css';
import './index.css';

type NodeTypesValue = NodeTypes[string];

export type FlowNodeTypeDef = NodeResourceType | 'INTEGRATION' | 'MISSION' | 'REPORT' | 'UNIT';
export type FlowNodeMap = Record<FlowNodeTypeDef, NodeTypesValue>;

interface Props {
  data: IFlowEditorData;
  theme?: string;
  /** 显示一些数据 给截图和浏览器用 */
  showInfo?: boolean;
  showControl?: boolean;
  readonly?: boolean;
  // 控制工具栏，是否添加打开新窗按钮？需要的话放个URL
  openNewWindowButtonAndUrl?: string;
  onClick?: (data: EditorScreenProps) => void | ((data: OrgChartUnit) => void);
  showAnimationSpeed?: number;
  fitView?: boolean;
  backgroundStyle?: CSSProperties;
  getMoreUnits?: (unit: OrgChartUnit) => void;

  showTeamControl?: boolean;
  viewMode?: TeamViewMode;
  setViewMode?: (mode: TeamViewMode) => void;
}

/**
 * Template Resources 工作流程编辑器
 *
 * @param props
 * @returns
 */
function Flow(props: Props) {
  const { data, fitView } = props;
  const { nodes: initialNodes, edges } = useFlowData({
    data,
    delay: props.showAnimationSpeed || 0,
    onClick: props.onClick,
    getMoreUnits: props.getMoreUnits,
  });

  const [nodes, setNodes] = useState(initialNodes);
  const [info, setInfo] = useState<{ maxX: number; maxY: number }>({ maxX: 0, maxY: 0 });
  const initialized = useNodesInitialized();
  const { getNodes, setNodes: rfSetNodes, setViewport, fitView: reactFlowFitView } = useReactFlow();
  const [isFullScreen, setIsFullScreen] = useState(false);

  useEffect(() => {
    setNodes(initialNodes);
  }, [initialNodes]);

  useEffect(() => {
    if (!initialized) return;

    if (data.type === 'node-resources') {
      const spacingX = 400;
      const spacing = 16;
      let currentX = 0;
      const all = getNodes();
      const typeY: Record<string, number> = {};

      const typeGroups = [
        ['FOLDER'],
        ['AUTOMATION', 'AI', 'DASHBOARD'],
        ['DATABASE', 'MIRROR', 'FORM'],
        ['VIEW', 'INTEGRATION', 'MISSION', 'REPORT'],
      ];

      const columnX: Record<string, number> = {};

      typeGroups.forEach((group) => {
        const hasNodesInGroup = group.some((type) => all.some((n) => n.type === type));
        if (hasNodesInGroup) {
          let groupY = 0;
          group.forEach((type) => {
            columnX[type] = currentX;
            const nodesOfType = all.filter((n) => n.type === type);
            if (nodesOfType.length > 0) {
              typeY[type] = groupY;
              let typeHeight = 0;
              nodesOfType.forEach((n) => {
                const h = n.measured?.height ?? 80;
                typeHeight += h + spacing;
              });
              groupY += typeHeight + spacing;
            }
          });
          currentX += spacingX;
        }
      });

      all.forEach((n) => {
        const type = n.type!;
        if (!(type in columnX)) {
          columnX[type] = currentX;
          typeY[type] = 0;
          currentX += spacingX;
        }
      });

      const laid = all.map((n) => {
        const h = n.measured?.height ?? 80;
        const x = columnX[n.type!];
        const y = typeY[n.type!] ?? 0;
        typeY[n.type!] = y + h + spacing;
        return { ...n, position: { x, y } };
      });

      rfSetNodes(laid);
    }

    if (fitView) {
      setTimeout(() => {
        if (data.type === 'units') {
          reactFlowFitView({ padding: 0.4, duration: props.showAnimationSpeed || 0 });
        } else {
          setViewport({ x: 0, y: 0, zoom: 1 }, { duration: props.showAnimationSpeed || 0 });
        }
      }, 100);
    }
  }, [initialized, data.type]);

  useEffect(() => {
    if (!initialized) return;

    const timer = setTimeout(() => {
      const bounds = getNodesBounds(getNodes(), { nodeOrigin: [0, 0] });
      setInfo({ maxX: bounds.x + bounds.width, maxY: bounds.y + bounds.height });
    }, 0);

    return () => {
      clearTimeout(timer);
    };
  }, [initialized]);

  useEffect(() => {
    if (props.showInfo && info) {
      // 创建两个隐藏的input 把X和Y记录在div里面
      const inputX = document.createElement('input');
      inputX.type = 'hidden';
      inputX.value = info.maxX.toString();
      inputX.id = 'graph-editor-x';
      document.body.appendChild(inputX);
      const inputY = document.createElement('input');
      inputY.type = 'hidden';
      inputY.value = info.maxY.toString();
      inputY.id = 'graph-editor-y';
      document.body.appendChild(inputY);
    }
    return () => {
      // 查找这两个ID 删除他们
      const inputX = document.getElementById('graph-editor-x');
      if (inputX) {
        inputX.remove();
      }
      const inputY = document.getElementById('graph-editor-y');
      if (inputY) {
        inputY.remove();
      }
    };
  }, [info, props.showInfo]);

  const nodeTypes: FlowNodeMap = {
    AUTOMATION: AutomationNode,
    DATABASE: DatabaseNode,
    FOLDER: FolderNode,
    MIRROR: MirrorNode,
    FORM: FormNode,
    // VIEW: ViewNode,
    INTEGRATION: IntegrationNode,
    MISSION: MissionNode,
    REPORT: ReportNode,
    UNIT: UnitNode,
    DASHBOARD: DashboardNode,
    AI: AIAgentNode,
    // below types TODO:
    ROOT: DatabaseNode,
    TEMPLATE: DatabaseNode,
    PAGE: DatabaseNode,
    EMBED: DatabaseNode,
    DATAPAGE: DatabaseNode,
    CANVAS: DatabaseNode,
    ALIAS: DatabaseNode,
    DOCUMENT: DatabaseNode,
    FILE: DatabaseNode,
    REPORT_TEMPLATE: DatabaseNode,
  };

  const content = (
    <Stack
      sx={
        isFullScreen
          ? {
              position: 'fixed',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              zIndex: 99999,
              flex: 1,
              backgroundColor: 'var(--bg-surface)',
            }
          : {
              flex: 1,
              height: '100%',
            }
      }
    >
      <ReactFlow
        style={{ flex: 1, width: '100%', height: '100%' }}
        edgesFocusable={false}
        edgesReconnectable={false}
        colorMode={props.theme as ColorMode}
        proOptions={{ hideAttribution: true }}
        nodes={nodes}
        edges={edges}
        fitView={fitView}
        nodesDraggable={false}
        // 禁止缩放
        // zoomOnScroll={!props.readonly}
        // 隐藏那个锁按钮
        nodeTypes={nodeTypes}
        nodeOrigin={[0, 0]}
        onNodesChange={(changes) => setNodes((nds) => applyNodeChanges(changes, nds))}
      >
        <Background style={props.backgroundStyle} />
        {props.showControl && (
          <FlowControl
            openNewWindowButtonAndUrl={props.openNewWindowButtonAndUrl}
            isFullScreen={isFullScreen}
            setIsFullScreen={setIsFullScreen}
          />
        )}
        {props.showTeamControl && <TeamControl viewMode={props.viewMode} setViewMode={props.setViewMode} />}
      </ReactFlow>
    </Stack>
  );
  if (typeof window !== 'undefined') {
    const body = document.querySelector('body') as HTMLBodyElement;
    return isFullScreen ? createPortal(content, body) : content;
  }
  return content;
}

export const FlowEditor = (props: Props) => (
  <ReactFlowProvider>
    <Flow {...props} />
  </ReactFlowProvider>
);

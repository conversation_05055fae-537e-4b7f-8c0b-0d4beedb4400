import dagre from '@dagrejs/dagre';
import { type Node, type Edge } from '@xyflow/react';
import { useEffect, useMemo, useState } from 'react';
import { getActionTypesConfig } from '@bika/contents/config/client/automation/actions';
import { useLocale, type ILocaleContext } from '@bika/contents/i18n/context';
import type { BaseCreateMissionAction, SendReportAction } from '@bika/types/automation/bo';
import { AutomationSchema } from '@bika/types/automation/bo';
import { ViewSchema, DatabaseSchema } from '@bika/types/database/bo';
import { MirrorBOSchema } from '@bika/types/node/bo';
import type { NodeResource } from '@bika/types/node/bo';
import { iStringParse } from '@bika/types/system';
import type { EditorScreenProps } from '@bika/types/template/type';
import type { OrgChartUnit } from '@bika/ui/editor/flow-editor/nodes/unit';
// const rootData = useCallback(async () => {
//   const data: TeamSubListPageVO = await trpc.team.subList.query({

export type IFlowEditorData =
  | {
      type: 'node-resources';
      resources: NodeResource[];
    }
  | {
      type: 'units';
      units: OrgChartUnit[];
      currentTeam?: OrgChartUnit;
    };

interface Props {
  data: IFlowEditorData;
  delay: number; // 默认10ms，大于0则有动画

  onClick?: (data: EditorScreenProps) => void | ((data: OrgChartUnit) => void);
  getMoreUnits?: (unit: OrgChartUnit) => void;
}
type FlowEditorResult = { nodes: Node[]; edges: Edge[] };
// newEdges,
// {
//   maxY: Math.max(automationY, databaseY, viewY) + 50,
//   maxX: x - 56 + (integration.length || mission.length || report.length ? 400 : 0),
// },

function parseFlowEditorData(data: IFlowEditorData, localeCtx: ILocaleContext, props: Props): FlowEditorResult {
  const { t, lang: locale } = localeCtx;
  const newNodes: Node[] = [];

  const newEdges: Edge[] = [];
  const integration: { type: string }[] = [];
  // 任务
  const mission: BaseCreateMissionAction[] = [];
  // 报告
  const report: SendReportAction[] = [];

  const x = 0;
  let y = 0;
  //
  const nodeSpacing = 16;
  const nodeWidth = 144;
  const nodeYSpacing = 300; // 每个节点的Y轴间距

  if (data.type === 'node-resources') {
    data.resources.forEach((resource, index) => {
      const idOrTpl = resource.id || resource.templateId;
      const baseId = `${resource.resourceType.toLowerCase()}_${idOrTpl}`;

      switch (resource.resourceType) {
        case 'FOLDER':
          newNodes.push({
            id: baseId,
            type: 'FOLDER',
            position: { x, y },
            data: { ...resource, onClick: props.onClick },
          });
          break;
        case 'AI':
          newNodes.push({ id: `ai_agent_${idOrTpl}`, type: 'AI', position: { x, y }, data: resource });
          break;
        case 'DASHBOARD':
          newNodes.push({ id: baseId, type: 'DASHBOARD', position: { x, y }, data: resource });
          break;
        case 'AUTOMATION': {
          const rd = AutomationSchema.parse(resource);
          const automationId = `automation_${idOrTpl}`;
          newNodes.push({
            id: baseId,
            position: { x, y },
            type: 'AUTOMATION',
            data: { ...rd, onClick: props.onClick },
          });
          for (let idx = 0; idx < rd.actions.length; idx++) {
            const action = rd.actions[idx];
            const id = action.templateId || `automation_${resource.templateId}_action_${idx}`;
            const sourceHandle = `automation_${idOrTpl}_action_${idx}`;
            const actionsConfig = getActionTypesConfig(localeCtx);
            const actionConfig = actionsConfig[action.actionType];
            // Integrations
            for (const inteType of actionConfig.integrations || []) {
              integration.push({ type: inteType });
              newEdges.push({
                label: t.editor.integration_line_text,
                source: automationId,
                target: 'integration',
                id: `${id}_integration_${action.templateId}`,
                sourceHandle,
                targetHandle: `integration_${inteType}`,
                animated: true,
                labelShowBg: false,
                focusable: false,
                labelStyle: { fill: 'var(--text-primary)' },
                zIndex: 1,
              });
            }

            if (action.actionType === 'CREATE_RECORD' || action.actionType === 'FIND_RECORDS') {
              const databaseId = `database_${action.input.databaseId || action.input.databaseTemplateId}`;
              if (databaseId) {
                newEdges.push({
                  label:
                    action.actionType === 'CREATE_RECORD'
                      ? t.editor.create_record_line_text
                      : t.editor.find_records_line_text,
                  source: automationId,
                  target: databaseId,
                  id:
                    action.actionType === 'CREATE_RECORD'
                      ? `${id}_create_record_${databaseId}`
                      : `${id}_find_records_${databaseId}`,
                  sourceHandle,
                  labelShowBg: false,
                  focusable: false,
                  labelStyle: { fill: 'var(--text-primary)' },
                  zIndex: 1,
                });
              }
            }

            if (action.actionType === 'CREATE_MISSION' || action.actionType === 'SEND_REPORT') {
              if (action.actionType === 'CREATE_MISSION') {
                mission.push(action as BaseCreateMissionAction);
              } else {
                report.push(action);
              }
              const target = action.actionType === 'CREATE_MISSION' ? 'mission' : 'report';
              newEdges.push({
                label:
                  action.actionType === 'CREATE_MISSION'
                    ? t.editor.create_mission_line_text
                    : t.automation.action.send_report.name,
                source: automationId,
                target,
                id: `${id}_${target}_${action.templateId}`,
                sourceHandle,
                targetHandle: `${target}_${action.templateId}`,
                animated: true,
                labelShowBg: false,
                focusable: false,
                labelStyle: { fill: 'var(--text-primary)' },
                zIndex: 1,
              });
            }
          }
          break;
        }
        case 'DATABASE': {
          const parsedData = DatabaseSchema.parse(resource);
          const databaseId = `database_${idOrTpl}`;
          newNodes.push({
            id: databaseId,
            position: { x, y },
            type: 'DATABASE',
            data: { ...parsedData, onClick: props.onClick },
          });
          if (parsedData.views) {
            parsedData.views.forEach((view, vid) => {
              const v = ViewSchema.parse(view);
              if (v.filters?.conds?.length) {
                const viewId = v.id || `${index}_view_${vid}`;
                newEdges.push({
                  id: `${databaseId}_${viewId}`,
                  source: databaseId,
                  target: viewId,
                  labelShowBg: false,
                  focusable: false,
                  labelStyle: { fill: 'var(--text-primary)' },
                  type: 'smoothstep',
                  zIndex: 10,
                });
                newNodes.push({
                  id: viewId,
                  position: { x, y },
                  type: 'VIEW',
                  data: {
                    ...v,
                    fields: parsedData.fields,
                    name: `${iStringParse(parsedData.name, localeCtx.lang)} - ${iStringParse(v.name, localeCtx.lang)}`,
                    onClick: props.onClick,
                    databaseId: resource.templateId,
                  },
                });
              }
            });
          }
          break;
        }
        case 'MIRROR': {
          const parsedData = MirrorBOSchema.parse(resource);
          const databaseId = `mirror_${idOrTpl}`;
          const viewRef = data.resources.find((r) => {
            // @ts-expect-error ignore
            if (r.resourceType === 'DATABASE' && r.id === parsedData.databaseId) {
              const dat = DatabaseSchema.parse(r);
              // @ts-expect-error ignore
              if (parsedData.viewId) {
                // @ts-expect-error ignore
                return (dat.views || []).find((v) => v.id === parsedData.viewId);
              }
              return true;
            }
            return false;
          });
          if (viewRef) {
            newNodes.push({
              id: databaseId,
              position: { x, y },
              type: 'MIRROR',
              // @ts-expect-error ignore
              data: { ...parsedData, onClick: props.onClick, fields: viewRef?.fields },
            });
          }
          break;
        }
        case 'FORM': {
          const formData = resource;
          const formId = `form_${idOrTpl}`;
          // @ts-expect-error ignore
          const databaseRef = data.resources.find((r) => r.resourceType === 'DATABASE' && r.id === formData.databaseId);
          if (databaseRef) {
            newNodes.push({
              id: formId,
              position: { x, y },
              type: 'FORM',
              // @ts-expect-error ignore
              data: { ...formData, onClick: props.onClick, fields: databaseRef.fields },
            });
          }
          break;
        }
        default:
          console.error(`Unsupported resource type: ${resource.resourceType}`);
          break;
      }
    });

    // Integrations
    if (integration.length > 0) {
      // integration
      newNodes.push({
        id: 'integration',
        type: 'INTEGRATION',
        // @ts-expect-error ignore
        data: integration,
        position: { x, y },
      });
    }

    // Missions
    if (mission.length > 0) {
      // mission
      newNodes.push({
        id: 'mission',
        type: 'MISSION',
        // @ts-expect-error ignore
        data: mission,
        position: { x, y },
      });
    }

    // Reports
    if (report.length > 0) {
      newNodes.push({
        id: 'report',
        type: 'REPORT',
        // @ts-expect-error ignore
        data: report,
        position: { x, y },
      });
    }

    return {
      nodes: newNodes,
      edges: newEdges,
    };
  }

  function setOrgChartNodes(parentId: string, unitsData: OrgChartUnit[], nodes: Node[], edges: Edge[]) {
    unitsData.forEach((unit) => {
      const nodeId = `${parentId}_${unit.id}`;
      if (unit.disabled) return;
      const memberCount = unit.children?.filter((child) => child.type !== 'Team' && !child.disabled).length || 0;
      nodes.push({
        id: nodeId,
        type: 'UNIT',
        position: { x: 0, y: 0 },
        data: {
          ...unit,
          memberCount,
          label: unit.name,
          parentId: unit.id,
          onClick: props.onClick,
          getMoreUnits: props.getMoreUnits,
        },
      });
      edges.push({
        id: `edge_${nodeId}`,
        source: parentId,
        target: nodeId,
        hidden: unit.disabled,
        type: 'smoothstep',
        style: {
          stroke: 'var(--borderBrandDefault)',
        },
      });
      if (unit.type === 'Team' && unit.children?.length && unit.isExpanded) {
        setOrgChartNodes(nodeId, unit.children, nodes, edges);
      }
    });
  }

  if (data.type === 'units') {
    if (!data.currentTeam) {
      return {
        nodes: [],
        edges: [],
      };
    }

    const unitsData: OrgChartUnit[] = data.units;
    y = 40;
    const currentTeam = data.currentTeam;

    const memberCount = unitsData.reduce((count, unit) => {
      if (unit.type !== 'Team' && !unit.disabled) {
        return count + 1;
      }
      return count;
    }, 0);
    newNodes.push({
      id: currentTeam.id,
      type: 'UNIT',
      position: { x, y },
      data: {
        ...currentTeam,
        label: currentTeam.name,
        memberCount,
      },
    });

    if (unitsData.length > 0) {
      setOrgChartNodes(currentTeam.id, unitsData, newNodes, newEdges);
    }

    const graph = new dagre.graphlib.Graph().setDefaultEdgeLabel(() => ({}));
    const autoLayout = (nodes: Node[], edges: Edge[]) => {
      graph.setGraph({ nodesep: nodeSpacing, ranksep: 0 });
      nodes.forEach((node) => {
        graph.setNode(node.id, { width: nodeWidth, height: nodeYSpacing });
      });
      edges.forEach((edge) => {
        graph.setEdge(edge.source, edge.target, { height: 20 });
      });
      dagre.layout(graph);

      const layoutNodes = nodes.map((node) => {
        const nodeWithPosition = graph.node(node.id);
        const newNode = {
          ...node,
          position: {
            x: nodeWithPosition.x - nodeWidth / 2,
            y: nodeWithPosition.y - nodeYSpacing / 2,
          },
        };
        return newNode;
      });
      return { nodes: layoutNodes, edges };
    };

    const { nodes: layoutNodes, edges: layoutEdges } = autoLayout(newNodes, newEdges);

    return {
      nodes: layoutNodes,
      edges: layoutEdges,
    };
  }

  return {
    nodes: newNodes,
    edges: newEdges,
  };
}

/**
 * 获取Resource的内容，动态地，每100ms递加，转换成ReactFlow的数据结构
 */
export function useFlowData(props: Props) {
  const { data, delay } = props;

  const [aniNodes, setAniNodes] = useState<Node[]>([]);
  const [timeoutId, setTimeoutId] = useState<ReturnType<typeof setInterval> | null>(null);
  const localeCtx = useLocale();
  const { t } = localeCtx;
  const locale = localeCtx.lang;

  // 获取真实的所有数据
  const { nodes, edges } = useMemo(() => parseFlowEditorData(data, localeCtx, props), [data]);

  useEffect(() => {
    setAniNodes([]);
    if (timeoutId) {
      clearInterval(timeoutId);
    }
    if (!nodes || nodes.length === 0) return;

    if (props.delay <= 0) {
      setAniNodes(nodes);
      return;
    }

    const id = setInterval(() => {
      setAniNodes((prev) => {
        if (prev.length === nodes.length) {
          clearInterval(id);
          return prev;
        }
        const theNode = nodes[prev.length];
        return [...prev, theNode];
      });
    }, props.delay);

    setTimeoutId(id);
    // eslint-disable-next-line consistent-return
    return () => {
      if (timeoutId) {
        clearInterval(timeoutId);
      }
    };
  }, [nodes]);
  // 每100ms递加，动态地从真实数据里抽取数据，暴露到外面的React Flow变成动画
  return { nodes: aniNodes, edges };
}

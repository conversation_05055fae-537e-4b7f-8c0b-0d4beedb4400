'use client';

import { Panel } from '@xyflow/react';
import classNames from 'classnames';
import { motion } from 'motion/react';
import { useLocale } from '@bika/contents/i18n/context';

export type TeamViewMode = 'All' | 'Member' | 'AI';

interface Props {
  viewMode?: TeamViewMode;
  setViewMode?: (mode: TeamViewMode) => void;
}

export function TeamControl(props: Props) {
  const { t } = useLocale();
  const { viewMode, setViewMode } = props;

  const TABS: { id: TeamViewMode; name: string }[] = [
    { id: 'All', name: t.team.show_all },
    { id: 'Member', name: t.team.show_member },
    { id: 'AI', name: t.team.show_ai },
  ];

  return (
    <Panel position="top-right" style={{ right: '50px' }}>
      <div className="flex gap-1 p-1 rounded-[8px] border border-[--border-default] bg-[--bg-popup] h-10 relative">
        {TABS.map((tab) => {
          const isActive = tab.id === viewMode;
          return (
            <motion.div
              key={tab.id}
              layout
              onClick={() => setViewMode?.(tab.id)}
              className={classNames(
                'relative flex-1 flex justify-center items-center px-2 rounded-md cursor-pointer z-10 hover:bg-[--hover]',
                isActive ? 'text-[--on-selected]' : 'text-[--text-primary]',
              )}
              transition={{ duration: 0.15 }}
            >
              {isActive && (
                <motion.div
                  layoutId="toggleBackground"
                  className="absolute inset-0 rounded-md bg-[--bg-controls] z-0"
                  transition={{ duration: 0.2 }}
                />
              )}
              <motion.span
                className="relative z-10 text-sm whitespace-nowrap"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.2 }}
              >
                {tab.name}
              </motion.span>
            </motion.div>
          );
        })}
      </div>
    </Panel>
  );
}

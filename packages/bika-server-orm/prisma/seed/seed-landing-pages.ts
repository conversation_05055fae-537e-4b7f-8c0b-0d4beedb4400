import assert from 'assert';
import { $Enums, Prisma } from '../prisma-client';
import { importLandingPage } from '../../../bika-dev-content-server/src/utils/pages';
import { LocalContentLoader } from '../../content';
import { db } from '../../db';

export async function seedLandingPages() {
  // 落地页
  const localLandingPages = await LocalContentLoader.landingPage.fsLandingPagesList();
  const upsertSlugs = [];
  for (const lp of localLandingPages) {
    const lpo = await importLandingPage(lp.lang, lp.slugs);
    const fullSlug = `${lp.lang}/${lp.slugs.join('/')}`;
    console.log(`Writing Landing Page: ${fullSlug}`);
    await db.prisma.content.upsert({
      where: {
        type_slug: {
          slug: fullSlug,
          type: $Enums.ContentType.LANDING_PAGE,
        },
      },
      update: {
        type: $Enums.ContentType.LANDING_PAGE,
        source: $Enums.ContentSource.LOCAL,
        data: lpo as unknown as Prisma.JsonObject,
      },
      create: {
        slug: fullSlug,
        type: $Enums.ContentType.LANDING_PAGE,
        source: $Enums.ContentSource.LOCAL,
        data: lpo as unknown as Prisma.JsonObject,
      },
    });
    upsertSlugs.push(fullSlug);
  }
  // 反向删除，数据库中存在，但是，本地不存在的LOCAL落地页删掉
  const deletedLPs = await db.prisma.content.deleteMany({
    where: {
      type: $Enums.ContentType.LANDING_PAGE,
      source: $Enums.ContentSource.LOCAL,
      NOT: {
        slug: {
          in: upsertSlugs,
        },
      },
    },
  });
  assert(deletedLPs);
  console.log('Deleted Unnecessary Landing Pages: ', deletedLPs.count);
}

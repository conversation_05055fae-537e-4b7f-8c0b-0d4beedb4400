// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider      = "prisma-client-js"
  output        = "./prisma-client"
  binaryTargets = ["native", "debian-openssl-3.0.x", "linux-musl-openssl-3.0.x", "debian-openssl-1.1.x", "linux-musl"]
}

// generator erd {
// provider = "prisma-erd-generator"
// output   = "../../../docs/images/ERD.svg"
// }

// generator dbml {
//   provider = "prisma-dbml-generator"
// }

generator markdown {
  provider = "prisma-markdown"
  output   = "../../../docs/ERD.md"
  title    = "BaseApp.ai Prisma Models"
}

// generator docs {
//   provider = "node ../../node_modules/prisma-docs-generator"
//   output   = "../../../docs/erd"
// }

datasource db {
  provider = "postgresql"
  url      = env("PG_DATABASE_URL")
}

// model Post {
//   id        Int      @id
//   createdAt DateTime @default(now())
//   updatedAt DateTime @updatedAt
//   title     String   @db.VarChar(255)
//   content   String?
//   published Boolean  @default(false)
//   author    User     @relation(fields: [authorId], references: [id])
//   authorId  Int
// }

// Users

// model UserProfile {
//   id        Int      @id
//   createdAt DateTime @default(now())
//   updatedAt DateTime @updatedAt
//   createdBy String? // User ID
//   updatedBy String? // User ID
//   isDeleted Boolean  @default(false)
//   bio       String?

//   user   User @relation(fields: [userId], references: [id])
//   userId Int  @unique
// }

model User {
  id             String  @id
  templateId     String?
  username       String? @unique
  hashedPassword String?

  name   String
  avatar Json

  email    String?
  phone    String?
  timeZone String?

  // 用户的个人配置，无限扩展的JSON，不用改Postgre schema，会出现在VO
  settings Json

  // 附加信息，服务器用来记录信息的，如记录哪种方式注册的，不会暴露给用户VO
  metadata Json

  // isDeleted Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 作为组织成员，可以加入多个Space
  // members       UnitMember[]
  // members UnitMember[] @relation("UserMember")
  // guests        UnitGuest[]

  externalLinks UserExternalLink[]
  tokens        UserDeveloperToken[]
  sessions      UserSession[]

  ownerSpaces Space[]
  embedLinks  EmbedLink[]
  Trash       Trash[]

  // subscriptions BillingSubscription[] @relation("UserToSubscriptions")
}

// 服务端Session，另有Online Session（客户端Session）
model UserSession {
  id        String   @id
  userId    String
  expiresAt DateTime
  ip        String? // 创建的IP 也用于每次登录的更新
  hostname  String? // 主机名 iPhone 13 Pro Max ... etc 用UA+主动上报来获取
  version   String? // 客户端版本号
  user      User     @relation(references: [id], fields: [userId])
  activeAt  DateTime @default(now())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([userId])
}

// 个人开发者token
model UserDeveloperToken {
  token  String @id
  userId String
  user   User   @relation(fields: [userId], references: [id])

  // 不填则永不过期
  expiration DateTime?

  createdBy String? // User ID
  updatedBy String? // User ID
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// 网站整站的Admin，不是空间站Admin
model SiteAdmin {
  // 同userId，如果存在，就是管理员
  id       String @id
  // 额外配置，如果为空，所有权限的super admin
  settings Json?

  createdBy String? // User ID
  updatedBy String? // User ID
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model UserExternalLink {
  user       User     @relation(fields: [userId], references: [id])
  userId     String
  externalId String
  type       String
  createdAt  DateTime @default(now())

  @@id([userId, externalId])
}

// === SPACE ===
model Space {
  id String @id

  // 网址
  slug String? @unique

  name String
  logo Json

  //
  settings Json?

  createdBy String? // User ID
  updatedBy String? // User ID
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime?

  ownerUserId String
  ownerUser   User   @relation(fields: [ownerUserId], references: [id])

  units            Unit[]
  nodes            Node[]
  // controls        Permission[]
  // auditLogs       SpaceAuditLog[]
  templateApplies  TemplateApply[]
  storeTemplates   StoreTemplate[]
  outgoingWebhooks OutgoingWebhook[]
  embedLinks       EmbedLink[]

  // BillingSubscription
  subscriptions BillingSubscription[] @relation("SpaceToSubscriptions")

  @@index([slug])
}

enum LinkInvitationType {
  MEMBER
  GUEST
}

model SpaceLinkInvitation {
  // invite token
  id       String              @id
  spaceId  String
  type     LinkInvitationType?
  member   UnitMember?         @relation(fields: [memberId], references: [id])
  memberId String
  teamId   String
  roleIds  String?

  createdBy String? // User ID
  updatedBy String? // User ID
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([spaceId])
  @@index([memberId])
  @@index([teamId])
}

enum NodeResourceType {
  // 根节点
  ROOT
  // 模板节点，一种带了模板标记的特殊文件夹(Folder)
  TEMPLATE
  // 文件夹
  FOLDER
  // 文件（数表）
  DATABASE
  // 表单
  FORM
  // 仪表盘
  DASHBOARD
  // 文档
  DOCUMENT

  // 文件（附件、真文件）
  FILE

  // AI Chat & AI Agent
  AI
  PAGE
  EMBED

  AUTOMATION
  MIRROR

  // @deprecated
  VIEW
  DATAPAGE
  CANVAS
  // Same as macOS alias
  ALIAS
  REPORT_TEMPLATE
}

// Folder、Template、RootFolder的Resource配置，默认是不创建的，在需要更多配置时才惰式创建
model Folder {
  id   String @id
  node Node   @relation(fields: [id], references: [id], onDelete: Cascade)

  templateId String?

  // 封面(AvatarLogo)，如果没有，则使用node icon
  cover Json?

  // README
  readme Json?

  // isDeleted Boolean  @default(false)
  createdBy String? // User ID
  updatedBy String? // User ID
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// 节点，可以是文件，也可以是文件夹，文件下，可以是Base、Automation等
model Node {
  id         String  @id
  templateId String?

  space   Space  @relation(fields: [spaceId], references: [id])
  spaceId String

  parent   Node?   @relation("ParentNode", fields: [parentId], references: [id], onDelete: Cascade)
  parentId String?
  children Node[]  @relation("ParentNode")

  // 同级下前一个节点ID，用于排序， one-to-one
  preNode   Node?   @relation("PreNode", fields: [preNodeId], references: [id])
  preNodeId String? @unique
  nextNode  Node?   @relation("PreNode")

  // Unit ID
  unitId String?
  unit   Unit?   @relation(fields: [unitId], references: [id])

  name        Json // iString
  description Json? // iString

  // Node Type
  type NodeResourceType

  // 存放一些节点状态，与小红点、Automation运行中、数据表行数等
  state Json?

  // Node 属性，如保存了文件的属性，比如，文件夹是一个模板(Template)
  // property Json?

  // 图标，默认是可以没有的，没有则显示文件夹等默认图标 (空间站logo和个人头像，是强制有的)
  icon Json?
  // isTemplate Boolean @default(false)

  // isDeleted Boolean  @default(false)
  createdBy String? // User ID
  updatedBy String? // User ID
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  automation      Automation?
  database        Database?
  board           Dashboard?
  form            Form?
  // @deprecated，不再使用view node，整合进mirror了
  view            DatabaseView?
  document        Document?       @relation("DocumentNode")
  file            FileNode?       @relation("FileNode")
  reportTemplate  ReportTemplate?
  templateApplies TemplateApply[]
  folder          Folder?
  mirror          Mirror?
  ai              AiNode?         @relation("AiNode")
  page            AiPage?         @relation("AiPage")

  @@index([spaceId, parentId])
}

enum PermissionResourceType {
  NODE
  DATABASE_FIELD
  DATABASE_VIEW
}

enum PermissionPrivilege {
  FULL_ACCESS
  CAN_EDIT
  CAN_EDIT_CONTENT
  CAN_COMMENT
  CAN_VIEW
  NO_ACCESS
}

// 许可证，权限控制单元。包含节点资源权限、字段权限
// 许可证只用于限制space内部的权限，如果需要限制外部权限，需要使用Share模块
model Permission {
  id String @id
  // space   Space       @relation(fields: [spaceId], references: [id])
  // spaceId String

  resourceType PermissionResourceType
  resourceId   String

  unit   Unit   @relation(fields: [unitId], references: [id])
  unitId String

  // @redundancy 冗余，便于更快查询
  unitType UnitType

  // enum AccessPrivilege
  privilege PermissionPrivilege

  createdBy String? // User ID
  updatedBy String? // User ID
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([unitId, resourceType, resourceId])
  @@index([resourceType, resourceId])
}

enum ShareResourceType {
  NODE
  DATABASE_RECORD
}

// 公开分享的范围
// 公开分享，有一个独立的文件夹来装，类似macOS的share目录
enum ShareScope {
  // 默认,站内权限控制
  DEFAULT

  // 公开可读,不影响站内权限控制
  PUBLIC_READ

  // 公开可编辑,不影响站内权限控制
  PUBLIC_READ_WRITE

  // 匿名可编辑
  ANONYMOUS_READ_WRITE

  // 公开发布
  PUBLISH
}

// 分享
model Share {
  id String @id

  resourceType ShareResourceType
  resourceId   String
  scope        ShareScope
  password     String?

  createdBy String? // User ID
  updatedBy String? // User ID
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([resourceType, resourceId])
}

enum UnitType {
  TEAM
  ROLE
  MEMBER
}

model Unit {
  id      String   @id
  space   Space    @relation(fields: [spaceId], references: [id])
  spaceId String
  type    UnitType

  // isDeleted Boolean  @default(false)
  createdBy String? // User ID
  updatedBy String? // User ID
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  team        UnitTeam?
  member      UnitMember?
  role        UnitRole?
  roles       UnitOnRoles[]
  permissions Permission[]
  Node        Node[]

  @@index([spaceId])
}

model UnitTeam {
  id       String     @id
  // @redundancy
  // spaceId  String
  unit     Unit       @relation(fields: [id], references: [id])
  parent   UnitTeam?  @relation("ParentUnitTeam", fields: [parentId], references: [id])
  parentId String?
  children UnitTeam[] @relation("ParentUnitTeam")
  name     String
  sequence Int        @default(1)

  // 是否访客小组
  isGuest Boolean @default(false)

  // isDeleted Boolean  @default(false)
  createdBy String? // User ID
  updatedBy String? // User ID
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // unitMember UnitMember[]
  // unitGuest  UnitGuest[]
  // child UnitTeam?
  // UnitToParent UnitTeam[]
  // UnitTeam     UnitTeam[]
  // UnitTeam     UnitTeam?    @relation(fields: [unitTeamId], references: [id])
  // unitTeaemId   Int?
  members UnitMembersOnTeams[]

  @@index([parentId])
}

enum UnitMemberRelationType {
  USER
  AI
}

// 成员或访客，请注意，PO数据库层面，Guest和Member共享持久化表
// SO业务逻辑层面，GuestSO和MemberSO独立业务类
// 空间站Admin也是共享这个持久化表
model UnitMember {
  id      String @id
  spaceId String

  relationType UnitMemberRelationType?
  relationId   String                  @map("userId")

  // user User?   @relation("UserMember", fields: [relationId], references: [id], map: "user_relationId")
  // ai   AiNode? @relation("AiMember", fields: [relationId], references: [id], map: "aiNode_relationId")

  unit Unit    @relation(fields: [id], references: [id])
  // 可以修改组织内的备注名
  name String?

  // @redundancy
  // 空间站主管理员标志，冗余，便于查询，实际以space.ownerUserId为准
  isOwner Boolean @default(false)

  // 是否匿名访客, 只能访问指定的资源
  isGuest Boolean @default(false)

  // 是否激活状态，or被屏蔽了
  isActive Boolean @default(true)

  // isDeleted Boolean  @default(false)
  createdBy String? // User ID
  updatedBy String? // User ID
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  teams           UnitMembersOnTeams[]
  // missions           Mission[]                @relation("Receiver")
  // comments        DatabaseRecordComment[]
  linkInvitations SpaceLinkInvitation[]

  @@unique([relationId, spaceId])
  @@index([spaceId])
}

model UnitMembersOnTeams {
  team     UnitTeam   @relation(fields: [teamId], references: [id])
  teamId   String
  member   UnitMember @relation(fields: [memberId], references: [id])
  memberId String

  createdBy String? // User ID
  createdAt DateTime @default(now())

  @@id([teamId, memberId])
}

model UnitRole {
  id         String  @id
  // 模板预置时用到
  templateId String?

  // 是否支持管理空间站配置 (新建空间站会有默认的admin role)
  manageSpace Boolean @default(false)

  // 角色设置(管理员权限配置, 其他配置)
  setting Json?

  // @redundancy
  // spaceId  String
  unit Unit @relation(fields: [id], references: [id])

  name        Json
  description Json?

  sequence Int @default(2000)

  // isDeleted Boolean  @default(false)
  createdBy String? // User ID
  updatedBy String? // User ID
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // team and member unit
  units UnitOnRoles[]

  // @@index([spaceId])
}

model UnitOnRoles {
  role   UnitRole @relation(fields: [roleId], references: [id])
  roleId String
  unit   Unit     @relation(fields: [unitId], references: [id])
  unitId String

  createdBy String? // User ID
  createdAt DateTime @default(now())

  @@id([roleId, unitId])
}

enum ShortcutRelationType {
  // 成员捷径
  MEMBER
  // 用户捷径（是的，跨空间站）
  USER
  // 空间站管理员设置的捷径
  SPACE
}

enum ShortcutObjectType {
  // 资源节点
  NODE

  // 某一行数据记录
  DATABASE_RECORD
}

// My Shortcuts,Favorited resources across your space. (Favorite, Pin, Star, Like, Bookmark)
model Shortcut {
  id String @id

  relationType ShortcutRelationType
  relationId   String

  objectType ShortcutObjectType
  objectId   String

  createdBy String? // User ID
  updatedBy String? // User ID
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([objectId])
}

// 访客 TODO: 改成Member的Type
// model UnitGuest {
//   id     String @id
//   user   User   @relation(fields: [userId], references: [id])
//   userId String

//   isDeleted Boolean  @default(false)
//   createdBy String? // User ID
//   updatedBy String? // User ID
//   createdAt DateTime @default(now())
//   updatedAt DateTime @updatedAt
// }

// Objects & Collections & Data

// 所有数据的原点，是一个Object
enum DatabaseType {
  TASK // 任务表，能勾选完成，委派人选
  DATUM // 数据表
  SYNC // 持续同步进来的数据
  EXTERNAL // 外部数据源，MySQL、，遵循接口，直接显示
  SYSTEM
}

enum WidgetType {
  LIST
  CHART // 图表
  NUMBER // 统计汇总数字
  TEXT // 就文字
  PIVOT_TABLE // 透视表
  EMBED
  ICONS
}

model Widget {
  id         String  @id
  templateId String?

  dashboard   Dashboard  @relation(fields: [dashboardId], references: [id], onDelete: Cascade)
  dashboardId String
  type        WidgetType
  name        Json
  description Json?

  // Template
  bo       Json?
  revision Int   @default(0)

  // isDeleted Boolean  @default(false)
  createdBy String? // User ID
  updatedBy String? // User ID
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Dashboard {
  id          String  @id
  // @redundancy
  // spaceId     String
  node        Node    @relation(fields: [id], references: [id], onDelete: Cascade)
  templateId  String?
  revision    Int
  // @redundancy 冗余字段，同node
  name        Json
  // @redundancy 冗余字段，同node
  description Json?

  // isDeleted Boolean  @default(false)
  createdBy String? // User ID
  updatedBy String? // User ID
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  widgets Widget[]
}

model Database {
  id          String       @id
  // @redundancy
  // spaceId     String
  node        Node         @relation(fields: [id], references: [id], onDelete: Cascade)
  templateId  String?
  type        DatabaseType
  revision    Int
  // @redundancy 冗余字段，同node
  name        Json // iString
  // @redundancy 冗余字段，同node
  description Json? // iString

  // isDeleted Boolean  @default(false)
  createdBy String? // User ID
  updatedBy String? // User ID
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  fields DatabaseField[]
  // views  Json // fieldsMap
  views  DatabaseView[]
  // comments DatabaseRecordComment[]
}

//  Represent Type of Database
// "List Database" can also represent as "Table View"
enum DatabaseViewType {
  KANBAN
  FORM
  GALLERY
  CALENDAR
  GANTT
  ORG_CHART
  TABLE
  LIST
}

// 这个View，挂靠在哪里
enum DatabaseViewAttachType {
  // 挂靠在Database里
  DATABASE
  // 挂靠成Node Resource, Database里看不到
  NODE
}

model DatabaseView {
  id           String                 @id
  relationType DatabaseViewAttachType
  relationId   String?
  // @redundancy same as id
  // @redundancy 冗余
  spaceId      String

  database    Database         @relation(fields: [databaseId], references: [id], onDelete: Cascade)
  databaseId  String
  templateId  String?
  type        DatabaseViewType
  name        Json
  description Json?

  // 同级下前一个视图ID，用于排序， one-to-one
  // 这个通常只在挂靠database里才用到，作为node resource有自己的挂靠
  preView   DatabaseView? @relation("PreView", fields: [preViewId], references: [id])
  preViewId String?       @unique
  nextView  DatabaseView? @relation("PreView")

  // 视图的配置属性
  // rows              Json[]
  // columns           Json[]
  autoSave Boolean?
  hidden   Boolean?

  // 字段，字符串数组，如果为空则显示全部
  // Zod Schema校对： DatabaseViewFieldPOSchema
  fields Json?

  // data Json? NO DATA, dynamic calc data via server
  filters Json?
  sorts   Json?
  lock    Json?
  groups  Json?

  // TODO: 这里存放，定制的其它视图的特定属性，比如，Kanban视图，会多groupingFieldId，存这里
  // 如存：KanbanViewExtraSchema
  extra Json?

  frozenColumnCount Int?
  rowHeightLevel    Int?
  autoHeadHeight    Boolean?
  style             Json?
  revision          Int      @default(0)

  displayHiddenColumnWithinMirror Boolean?

  // isDeleted Boolean  @default(false)
  createdBy String? // User ID
  updatedBy String? // User ID
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  node      Node?    @relation(fields: [relationId], references: [id], onDelete: Cascade)

  // forms Form[]
  @@unique([relationId])
}

// 字段类型
enum DatabaseFieldType {
  LONG_TEXT
  SINGLE_TEXT
  NUMBER
  SINGLE_SELECT
  MULTI_SELECT
  DATERANGE
  DATETIME
  ATTACHMENT
  LINK
  URL
  EMAIL
  PHONE
  CHECKBOX
  RATING
  MEMBER
  LOOKUP
  FORMULA
  CURRENCY
  PERCENT
  AUTO_NUMBER
  CREATED_TIME
  MODIFIED_TIME
  CREATED_BY
  MODIFIED_BY
  CASCADER
  ONE_WAY_LINK
  WORK_DOC
  BUTTON

  VIDEO
  VOICE
  PHOTO

  API
  AI_TEXT
  AI_VOICE
  AI_PHOTO
  AI_VIDEO

  JSON
  DOC
}

//
model DatabaseField {
  id         String  @id
  // 自增ID，用于view中的排序
  sequenceId Int?    @default(autoincrement())
  templateId String?

  // @redundancy
  spaceId String

  database    Database          @relation(fields: [databaseId], references: [id], onDelete: Cascade)
  databaseId  String
  type        DatabaseFieldType
  name        Json
  description Json?
  required    Boolean?
  unique      Boolean?
  property    Json?
  validators  Json?
  revision    Int               @default(0)
  primary     Boolean?          @default(false)

  // isDeleted Boolean  @default(false)
  createdBy String? // User ID
  updatedBy String? // User ID
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  databaseFieldLookup DatabaseFieldLookup[]

  @@index([databaseId])
}

model DatabaseFieldLookup {
  id                  String        @id
  linkFieldId         String
  lookupField         DatabaseField @relation(fields: [lookupFieldId], references: [id], onDelete: Cascade)
  lookupFieldId       String
  lookupTargetFieldId String

  createdBy       String? // User ID
  updatedBy       String? // User ID
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  databaseFieldId String?

  @@index([linkFieldId])
  @@index([lookupTargetFieldId])
}

// 数据行记录评论 Record Comment
// 注意，Record在MongoDB
// model DatabaseRecordComment {
//   id        String   @id
//   createdAt DateTime @default(now())
//   updatedAt DateTime @updatedAt
//   createdBy String? // User ID
//   updatedBy String? // User ID
//   isDeleted Boolean  @default(false)

//   // 评论正文内容
//   content String

//   recordId String

//   // 谁写的
//   unitMember   UnitMember @relation(fields: [unitMemberId], references: [id])
//   unitMemberId String

//   // @redundancy 冗余
//   database   Database @relation(fields: [databaseId], references: [id])
//   databaseId String

//   @@index([recordId])
// }

// enum DatabaseRecordStatus {
//   OPEN
//   CLOSED // for List Task, when mission is done, it will be closed
//   ARCHIVED // for both List and Table, when it is archived, it will be hidden
// }

// // Store into MongoDB
// model DatabaseRecord {
//   id        Int      @id
//   createdAt DateTime @default(now())
//   updatedAt DateTime @updatedAt
//   createdBy String? // User ID
//   updatedBy String? // User ID
//   isDeleted Boolean  @default(false)
//   revision  Int

//   data Json

//   status DatabaseRecordStatus
// }

// model DatabaseList {
//   id        Int                @id
//   createdAt DateTime           @default(now())
//   updatedAt DateTime           @updatedAt
//   createdBy String? // User ID
//   updatedBy String? // User ID
//   isDeleted Boolean            @default(false)
//   name      String
//   items     DatabaseListItem[]

//   database   Database @relation(fields: [collectionId], references: [id])
//   collectionId Int
// }

//
// aflsdjf
// model DatabaseListItem {
//   id        Int      @id
//   createdAt DateTime @default(now())
//   updatedAt DateTime @updatedAt
//   createdBy String? // User ID
//   updatedBy String? // User ID
//   isDeleted Boolean  @default(false)

//   name   String
//   status DatabaseListItemStatus
//   list   DatabaseList           @relation(fields: [listId], references: [id])
//   listId Int

//   object   DatabaseRecord @relation(fields: [objectId], references: [id])
//   objectId Int
// }

// model DatabaseTableItem {
//   id        Int           @id
//   createdAt DateTime      @default(now())
//   updatedAt DateTime      @updatedAt
//   createdBy String? // User ID
//   updatedBy String? // User ID
//   isDeleted Boolean       @default(false)
//   name      String
//   database DatabaseTable @relation(fields: [baseId], references: [id])
//   databasebaseId    Int
// }

// CRDT OP, MongoDB maybe
// model DatabaseRecordOperation {
//   id        Int      @id
//   createdAt DateTime @default(now())
//   updatedAt DateTime @updatedAt
//   createdBy String? // User ID
//   updatedBy String? // User ID
//   isDeleted Boolean  @default(false)

//   data Json
// }

// Reminder, is to create Scheduler to send notification without automation
// model Reminder {
//   id String @id

//   name        String
//   description String?

//   // The DateTime start to remind
//   datetime DateTime

//   // The Duration
//   datetimeEnd DateTime?

//   // repeat?
//   cron String?

//   // Extra属性，如，提醒类型：准点提前、提前5分钟、提前15分钟、提前1小时、提前1天
//   property Json?

//   // Reminder Who? Team or Member both OK.
//   unitId String

//   // How many times have been reminded?
//   // 通知过的次数计数，有些是可以循环通知的，有些是单次的，简单技术
//   count Int @default(0)

//   isDeleted Boolean  @default(false)
//   createdBy String? // User ID
//   updatedBy String? // User ID
//   createdAt DateTime @default(now())
//   updatedAt DateTime @updatedAt
// }

// Automation
model Automation {
  id         String  @id
  templateId String?
  // @redundancy
  spaceId    String
  node       Node    @relation(fields: [id], references: [id], onDelete: Cascade)

  // @redundancy 冗余字段，同node
  name        Json // iString
  // @redundancy 冗余字段，同node
  description Json? // iString

  isActive Boolean @default(false)

  triggers AutomationTrigger[]
  actions  AutomationAction[]

  // isDeleted Boolean  @default(false)
  createdBy String? // User ID
  updatedBy String? // User ID
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// model AutomationNode {
//   id String @id

//   // @redundancy
//   spaceId      String
//   automation   Automation @relation(fields: [automationId], references: [id], onDelete: Cascade)
//   automationId String

// source_node_id: 源节点 ID（外键，指向 Nodes 表）
// target_node_id: 目标节点 ID（外键，指向 Nodes 表）
// type: 边的类型（例如：数据流动）

//   // isDeleted Boolean  @default(false)
//   createdBy String? // User ID
//   updatedBy String? // User ID
//   createdAt DateTime @default(now())
//   updatedAt DateTime @updatedAt
// }

// model AutomationEdge {
//   id String @id

//   // @redundancy
//   spaceId      String
//   automation   Automation @relation(fields: [automationId], references: [id], onDelete: Cascade)
//   automationId String

//   // isDeleted Boolean  @default(false)
//   createdBy String? // User ID
//   updatedBy String? // User ID
//   createdAt DateTime @default(now())
//   updatedAt DateTime @updatedAt
// }

// @deprecated 之后用 Automation Nodes 和 Edges
model AutomationAction {
  id         String  @id
  templateId String?
  type       String

  description Json?
  input       Json?
  state       Json?

  // @redundancy
  spaceId      String
  automation   Automation @relation(fields: [automationId], references: [id], onDelete: Cascade)
  automationId String

  // for 嵌套actions
  parentAction    AutomationAction?  @relation("parentAction", fields: [parentActionId], references: [id])
  parentActionId  String?
  childrenActions AutomationAction[] @relation("parentAction")

  // 同级下前一个执行动作ID，用于排序。one-to-one
  preAction   AutomationAction? @relation("PreAction", fields: [preActionId], references: [id])
  preActionId String?           @unique
  nextAction  AutomationAction? @relation("PreAction")

  // isDeleted Boolean  @default(false)
  createdBy String? // User ID
  updatedBy String? // User ID
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([spaceId])
  @@index([automationId])
}

model AutomationTrigger {
  id         String  @id
  templateId String?
  type       String

  description Json?
  input       Json?
  state       Json?

  // @redundancy
  spaceId      String
  automation   Automation @relation(fields: [automationId], references: [id], onDelete: Cascade)
  automationId String

  // 同级下前一个执行器ID，用于排序。one-to-one
  preTrigger   AutomationTrigger? @relation("PreTrigger", fields: [preTriggerId], references: [id])
  preTriggerId String?            @unique
  nextTrigger  AutomationTrigger? @relation("PreTrigger")

  // isDeleted Boolean  @default(false)
  createdBy String? // User ID
  updatedBy String? // User ID
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([spaceId])
  @@index([automationId])
}

enum SchedulerRelationType {
  // Delayed Action
  AUTOMATION_ACTION
  // 定时触发 repeat
  AUTOMATION_TRIGGER
  // 提醒 repeat
  REMINDER
  // 有due date的mission，在到期后，会发送通知给assignee，once一次性
  MISSION
}

// 待执行的Schedule，当Trigger被创建后，计算cron的“下一次执行时间”，立刻创建Schedule
// Schedule被执行后，会立刻变成一个job，Schedule立刻创建「下一个执行时间」 (Nani，创建的下一个schedule，也是<now?循环递归执行)
// Job是否完成，交给Job自己评判，不影响Schedule的滚滚车轮
model Scheduler {
  id String @id

  relationType SchedulerRelationType
  relationId   String
  property     Json?
  runTime      DateTime // schedule的下次执行时间

  createdBy String? // User ID
  createdAt DateTime @default(now())

  @@index([runTime])
  @@index([relationId])
}

enum JobStatus {
  PENDING
  RUNNING
  SUCCESS
  FAILED
}

// Schdule被执行后，立刻生成了Job，Job自己来处理Job是否完成，如果失败，会残留在这里
// (RecordCreated等Event Trigger，也会创建Job，但理论上它们不会多进程执行)
// (如果使用Vercel Serverless，理论上没有多进程，但最好也要创建Job，避免执行失败)
model Job {
  id String @id

  status JobStatus @default(PENDING)

  property Json

  // 通常错误日志写在这
  error String?

  createdAt DateTime @default(now())
}

// Marketing Campagins
model Campaign {
  id        String   @id
  // isDeleted Boolean  @default(false)
  createdBy String? // User ID
  updatedBy String? // User ID
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  sequences CampaignSequence[]
}

model CampaignSequence {
  id         String                 @id
  campaign   Campaign               @relation(fields: [campaignId], references: [id])
  campaignId String
  steps      CampaignSequenceStep[]

  // isDeleted Boolean  @default(false)
  createdBy String? // User ID
  updatedBy String? // User ID
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model CampaignSequenceStep {
  id                 String           @id
  campaignSequence   CampaignSequence @relation(fields: [campaignSequenceId], references: [id])
  campaignSequenceId String

  // isDeleted Boolean  @default(false)
  createdBy String? // User ID
  updatedBy String? // User ID
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// Form 源于 数据表的View，自动匹配View的字段
model Form {
  id          String  @id
  node        Node    @relation(fields: [id], references: [id], onDelete: Cascade)
  // @redundancy 冗余字段，同node
  name        Json // iString
  // @redundancy 冗余字段，同node
  description Json? // iString
  spaceId     String
  templateId  String?
  databaseId  String
  metadata    Json?
  brandLogo   Json?

  createdBy String? // User ID
  updatedBy String? // User ID
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([spaceId])
  @@index([databaseId])
}

enum MirrorType {
  DATABASE_VIEW
  VIEW
  NODE_RESOURCE
}

model Mirror {
  id          String     @id
  node        Node       @relation(fields: [id], references: [id], onDelete: Cascade)
  // @redundancy 冗余字段，同node
  name        Json // iString
  // @redundancy 冗余字段，同node
  description Json? // iString
  // @redundancy
  spaceId     String
  // @redundancy 冗余字段 统计
  resourceId  String?
  databaseId  String?
  viewId      String?
  // @redundancy 冗余字段，同bo.mirrorType
  mirrorType  MirrorType

  templateId String?

  metadata Json?

  // viewId     String
  // metadata   Json?
  // revision   BigInt       @default(0)

  // isDeleted Boolean  @default(false)
  createdBy String? // User ID
  updatedBy String? // User ID
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([spaceId])
  @@index([databaseId])
  @@index([resourceId])
}

// AI Alignment AI沟通对齐
// AI Alignment是一种Chat的形式，隐藏属性，
// 如邮件、语音等自然语言沟通，都会发起一次Alignment，记录下来，如果匹配到Form，AI 就会自动填充Form
// model AIAlign {
//   id        String   @id
//   isDeleted Boolean  @default(false)
//   createdBy String? // User ID
//   updatedBy String? // User ID
//   createdAt DateTime @default(now())
//   updatedAt DateTime @updatedAt
// }

//  Email Service

// 监听IMAP邮箱，收到邮件
// model EmailListener {
//   id        String   @id
//   isDeleted Boolean  @default(false)
//   createdBy String? // User ID
//   updatedBy String? // User ID
//   createdAt DateTime @default(now())
//   updatedAt DateTime @updatedAt
// }

// Integrations, Sync

// 数据同步器，同步外部数据到 Base
// model DataSync {
//   id        String   @id
//   isDeleted Boolean  @default(false)
//   createdBy String? // User ID
//   updatedBy String? // User ID
//   createdAt DateTime @default(now())
//   updatedAt DateTime @updatedAt
// }

enum TemplateSourceType {
  // 从服务器官方本地创建
  LOCAL
  // 从应用市场创建 Template Store
  STORE
  // 从脚本直接装
  SCRIPT
}

// 对模板点星星收藏
model StoreTemplateStar {
  userId     String
  templateId String

  createdBy String? // User ID
  updatedBy String?
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

  @@unique([userId, templateId])
}

enum TemplateApplyType {
  // 新安装，不存在的
  INSTALL
  // 存在过的，更新
  UPDATE
}

// 模板引用到哪个space
model TemplateApply {
  id              String             @id
  // Which template being applied?
  templateId      String
  templateVersion String?
  space           Space              @relation(fields: [spaceId], references: [id])
  spaceId         String
  // node id when apply complete
  node            Node               @relation(fields: [nodeId], references: [id], onDelete: Cascade)
  nodeId          String
  // Which way to apply?
  source          TemplateSourceType
  // Which type?
  type            TemplateApplyType

  createdBy String? // User ID
  updatedBy String?
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

  @@index([spaceId, templateId])
}

enum ReportTemplateType {
  TEXT
  HTML
  MARKDOWN
  AI_PROMPT
}

model ReportTemplate {
  id         String  @id
  templateId String?

  node Node @relation(fields: [id], references: [id])

  type ReportTemplateType

  name        Json // iString, @redundancy
  description Json? // iString, @redundancy

  subject String
  body    String

  // isDeleted Boolean  @default(false)
  createdBy String? // User ID
  updatedBy String? // User ID
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // attachment Attachment[]
}

// Mission 任务请求
// model Mission {
//   id         String  @id
//   templateId String?

//   // Mission的接收者，可以是UnitMember，也可以是UnitGuest
//   // Mission与unit member绑定，可反推userID和spaceID
//   // 如果这个Mission，没有绑定到UnitMember，那么就是一个可被公开访问的Mission，通常通过邮件发出
//   unitMember   UnitMember? @relation("Receiver", fields: [unitMemberId], references: [id])
//   unitMemberId String?

//   // @redundancy 冗余
//   spaceId String

//   // Mission Type
//   type        String
//   name        String
//   description String?
//   property    Json?

//   canReject           Boolean @default(false)
//   canTransfer         Boolean @default(false)
//   canCompleteManually Boolean @default(false)

//   dueDate DateTime?

//   // 完成了
//   completedAt DateTime?

//   // 拒绝了
//   rejectedAt DateTime?

//   isDeleted Boolean  @default(false)
//   createdBy String? // User ID
//   updatedBy String? // User ID
//   createdAt DateTime @default(now())
//   updatedAt DateTime @updatedAt

//   @@index([type, unitMemberId])
// }

// 审批流程，模板定义，未实例化
// 审批是一种Mission，可以是完成某个任务后，进入审批流程；
// 可以直接就是一个审批流程
// Mission的本质就是喊人类干活，因此审批是喊人类就审批，自然就是归属Mission
// model FlowDef {
//   id String @id

//   // 工作流类型，任务完成后，进入审批流程？创建数据后的review流程？审批是一种任务？
//   // type
//   isDeleted Boolean  @default(false)
//   createdBy String? // User ID
//   updatedBy String? // User ID
//   createdAt DateTime @default(now())
//   updatedAt DateTime @updatedAt
// }

// 审批流程的过程, Flow -> Process
// 流程节点
// model FlowProcessDef {
//   id String @id

//   // 上一个process定义，用户顺序
//   preProcessId String?

//   // 审批意见必填？
//   mustComment Boolean @default(false)

//   // 是否可以转交？
//   canTransfer Boolean @default(false)

//   // 审批人，可以从成员、部门选择，也可以从表格中选择
//   // units

//   isDeleted Boolean  @default(false)
//   createdBy String? // User ID
//   updatedBy String? // User ID
//   createdAt DateTime @default(now())
//   updatedAt DateTime @updatedAt
// }

// 审批流程的请求, Flow是模板，Flow Request是实例化，真实在运行，标记去到第几个process
// model FlowRequest {
//   id String @id

//   // 对应哪个Flow定义？内置Process
//   flowDefId            String
//   // 当前去到哪个FlowProcessRequest
//   flowProcessRequestId String

//   isDeleted Boolean  @default(false)
//   createdBy String? // User ID
//   updatedBy String? // User ID
//   createdAt DateTime @default(now())
//   updatedAt DateTime @updatedAt
// }

// 审批流程的请求的过程, Flow Request -> Process Request
// 在审批流进行过程中，FlowProcessRequest是不急着创建的，因为用户可能在审批过程中，对Flow定义进行了修改
// Scenario: 一个工作流，进行到第2步了
// When 用户修改了定义，把第一步和第二步都改了
// Then 原审批流的第1、2步已经request过了，忽略
// model FlowProcessRequest {
//   id            String @id
//   flowRequestId String

//   isDeleted Boolean  @default(false)
//   createdBy String? // User ID
//   updatedBy String? // User ID
//   createdAt DateTime @default(now())
//   updatedAt DateTime @updatedAt
// }

// 空间站审计与Security Log
// TODO: 海量数据嫌疑，迁移Elastic Search
// model SpaceAuditLog {
//   id        String @id
//   space     Space  @relation(fields: [spaceId], references: [id])
//   spaceId   String
//   category  String
//   action    String
//   data      Json // {type; value;}
//   ip        String // IP地址
//   userAgent String

//   createdBy String? // User ID
//   createdAt DateTime @default(now())

//   @@index([spaceId])
// }

// 小写匹配美剧PyaInterval
enum BillingRecurring {
  once // LifeTime Deal
  month
  year
}

enum BillingSkuType {
  SPACE_PLAN
  PAY_AS_YOU_GO
  COIN_CURRENCY
  TEMPLATE
}

enum SubscriptionState {
  ACTIVE
  CANCELLED
  PAST_DUE
  SUSPENDED
}

// Space Billing，付费信息
model BillingSubscription {
  id String @id

  // 游标, 自增型ID, 用于游标偏移分页扫描
  cursor Int @default(autoincrement())

  // 映射我们内部哪个user呢？
  customerRelationType BillingCustomerRelationType

  // 订阅收费主体是空间站或用户
  // user id，或space id
  customerRelationId String

  // user  User?  @relation("UserToSubscriptions", fields: [customerRelationId], references: [id], map: "UserCustomerFKey")
  space Space? @relation("SpaceToSubscriptions", fields: [customerRelationId], references: [id], map: "SpaceCustomerFKey")

  platform               BillingPlatform // PaymentPlatform, 如stripe
  platformSubscriptionId String? // 如Stripe Billing的Subscription ID

  // 订阅配置计划, Sku ID对应Stripe的Product ID通常
  skuType BillingSkuType // 冗余 @redundancy
  skuId   String

  // platformId             String // PaymentPlatform, 如stripe
  // platformSubscriptionId String // 如Stripe Billing的Subscription ID

  // 对应哪个产品？ 可以进行升级、降级（更换product）
  // 这里只进行字段更新，否则非常复杂的业务，具体多态实现抓取Stripe可以
  // productId String
  // product   BillingProduct @relation(fields: [productId], references: [id])

  // SKU，通常在本地BO里定义(Plan / Addons)，映射Stripe的Product
  // @redundancy 冗余字段，通常在product
  // skuId String

  // 是否是循环订阅、一次性支付、年度或月度
  // @redundancy 冗余字段，通常在product
  recurring BillingRecurring

  // 理论上要保存payment，对subscription的存续，这里简化了

  // 客户，映射Stripe或第三方的客户
  // customerId String

  // 哪天过期？ 一定要有个时间 (Stripe每个月在扣款时会callback，更新这里)
  expiresAt DateTime?

  state SubscriptionState @default(ACTIVE)

  // 指定账单日结束后是否取消订阅, 防止退款扣钱
  cancelAtPeriodEnd Boolean   @default(false)
  // 当时操作取消订阅时间
  cancelAt          DateTime?
  // 已被取消订阅的时间
  canceledAt        DateTime?

  // isDeleted Boolean          @default(false)
  createdBy String? // User ID
  updatedBy String? // User ID
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  //  有过哪些支付记录
  payments BillingPayment[]

  @@unique([platform, platformSubscriptionId])
  @@index([customerRelationType, customerRelationId])
  @@index([cursor])
}

enum BillingPlatform {
  // Bika Coin
  BIKA
  // Stripe支付、代客支付、公转公后手工录入等
  STRIPE
  // Appsumo
  APPSUMO
  // ONCELY
  ONCELY
  // Expo Revenue Cat for iOS and Android
  REVENUE_CAT
  // 苹果商店
  IOS
  // 钉钉？
  DING
  // 企业微信？
  WECOM
  // 飞书?
  LARK
}

enum BillingCustomerRelationType {
  SPACE
  USER
}

// Billing Customer，客户信息
model BillingCustomer {
  id String @id

  // 映射PaymentPlatformId的枚举
  platform BillingPlatform

  // Platform上的Customer ID绑定
  // 第三方平台的客户编号映射
  platformCustomerId String

  // 映射我们内部哪个user呢？
  customerRelationType BillingCustomerRelationType
  // user id，或space id
  customerRelationId   String

  // 冗余，通常等于Stripe上的name
  name  String
  // 冗余，通常等于Stripe上的email
  email String

  // 冗余，存放各种额外信息
  metadata Json?

  // isDeleted Boolean          @default(false)
  createdBy String? // User ID
  updatedBy String? // User ID
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  payments BillingPayment[]

  @@unique([platform, customerRelationType, customerRelationId])
  @@unique([platform, platformCustomerId])
}

// Product是用户层面、第三方层面的，SKU是系统层面的
// SKU是我们内部的常量，定义死的
// 这个表用于映射外部Product和内部SKU
// 映射关系：Product -> SKU -> Plan | Addon ...
// model BillingProduct {
//   id                String @id
//   // 哪个商店橱窗看到我们的商品呢？ 可以是ADMIN? Android Play Store? iOS App Store? Stripe?
//   platformId        String
//   // 商店的商品编号呢？
//   platformProductId String

//   // 循环支付方式，ONCE？
//   recurring BillingRecurring

//   currency String
//   amount   Int

//   // 映射我们内部哪个具体的SKU
//   skuId String
//   // payments      BillingPayment[]
//   // subscriptions BillingSubscription[]

//   @@unique([platformId, platformProductId])
// }

// 支付方式，Credit金币？Stripe现金？组合？
// enum BillingPaymentType {
//   COINS
//   STRIPE
//   COMPOSITION
// }

enum BillingPaymentStatus {
  // 等待回调
  PENDING
  // 支付成功
  SUCCESS
  // 过期，超时失败
  EXPIRED

  FAILED

  CANCEL
}

model BillingPayment {
  id String @id

  skuConfig     Json
  checkoutPrice Json

  // type BillingPaymentType
  // platform BillingPlatform

  // ISO 货币代码, USD, CNY...
  // currencyCode String

  // 真现金
  // money BigInt

  status BillingPaymentStatus

  // coins         BigInt
  // coinsCredit   BigInt
  // coinsCurrency BigInt

  customerId String
  customer   BillingCustomer @relation(fields: [customerId], references: [id])

  // 放额外的信息
  metadata Json?

  // skuType BillingSkuType
  // skuId   String
  // product   BillingProduct @relation(fields: [productId], references: [id])

  // 保存支付成功的结果，subscription
  subscriptionId String?
  subscription   BillingSubscription? @relation(fields: [subscriptionId], references: [id])

  createdBy String // User ID
  updatedBy String // User ID
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

enum ContentType {
  // 落地页
  LANDING_PAGE
  // 单个博客文章
  BLOG_POST
  // 一篇帮助文档
  HELP
}

enum ContentSource {
  // 本地Git文件来源
  LOCAL
}

model Content {
  // 内容类型
  type ContentType

  // 网址 ，不带help/page/前缀，由type和slug组合成id
  slug String

  // 发布来源，如local上传
  source ContentSource

  data Json

  contentDate DateTime?

  createdBy String? // User ID
  updatedBy String? // User ID
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([type, slug])
}

// 模板来源
enum StoreTemplateSource {
  // 官方一方应用
  OFFICIAL

  // 第三方开发者
  DEVELOPER
}

enum StoreTemplateVisibility {
  // 公开
  PUBLIC

  // 外部可见，但不可安装，类似WAITING LIST
  WAITING_LIST

  // 空间站成员可见
  SPACE
  // 仅空间站管理员可见
  PRIVATE
}

// 模板商店 Bika Templates Store
model StoreTemplate {
  // templateId
  templateId String              @id
  source     StoreTemplateSource

  visibility StoreTemplateVisibility

  // 没有dependencies字段，跟着template release走

  // 是否经过官方认证？
  verified Boolean @default(false)

  name        Json
  description Json?

  // 冗余，匹配最新的release
  cover    Json?
  // 冗余，字符串或数组
  category Json?

  // 3个冗余,SEO、AIGC需要
  keywords Json?
  personas Json?
  useCases Json?

  // 是否只允许一个空间站安装一次？ 冗余metadata
  installOnce Boolean @default(false)
  // 是否允许解绑？冗余metaddata
  detachable  Boolean @default(false)

  // README
  readme Json?

  // 星星，冗余，在设置StoreTempalteStar的时候进行加减计算
  stars BigInt @default(0)

  // 当前的版本号，用于切换release
  currentVersion String

  // 是否由空间站创建？
  spaceId String?
  space   Space?  @relation(fields: [spaceId], references: [id])

  // isDeleted Boolean  @default(false)
  createdBy String? // User ID
  updatedBy String? // User ID
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  releases StoreTemplateRelease[] @relation("ReleaseTemplate")
  reviews  StoreTemplateReview[]
}

// 模板商店的package包，即每次发布
model StoreTemplateRelease {
  id         String        @id
  templateId String
  template   StoreTemplate @relation("ReleaseTemplate", fields: [templateId], references: [templateId])

  // template.json，同时也包含整个metadata
  data Json

  // 版本号
  version String

  // 变更说明(通常自动生成)
  changelog String?

  // 产品升级说明(通常是用户配置)
  releaseNotes String?

  createdBy String? // User ID
  updatedBy String? // User ID
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([templateId, version])
}

// 评论
model StoreTemplateReview {
  templateId String        @id
  template   StoreTemplate @relation(fields: [templateId], references: [templateId])
  title      String
  content    String

  // isDeleted Boolean  @default(false)
  createdBy String? // User ID
  updatedBy String? // User ID
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

enum DocumentAttachType {
  DATABASE_FIELD
  NODE
}

// 附件文件节点
model FileNode {
  id String @id

  templateId String?

  // @redundancy
  spaceId String

  // 冗余，同node
  name        Json
  // 冗余，同node
  description Json?

  node Node? @relation("FileNode", fields: [id], references: [id], onDelete: Cascade)

  attachmentId String
  attachment   Attachment @relation(fields: [attachmentId], references: [id])

  // isDeleted Boolean  @default(false)
  createdBy String? // User ID
  updatedBy String? // User ID
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model AiPage {
  id String @id

  templateId String?

  // @redundancy
  spaceId String

  // 冗余，同node
  name        Json
  // 冗余，同node
  description Json?

  // 配置
  bo Json

  node Node? @relation("AiPage", fields: [id], references: [id], onDelete: Cascade)

  // attachmentId String
  // attachment   Attachment @relation(fields: [attachmentId], references: [id])

  // isDeleted Boolean  @default(false)
  createdBy String? // User ID
  updatedBy String? // User ID
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model AiNode {
  id String @id

  templateId String?

  // @redundancy
  spaceId String

  // 冗余，同node
  name        Json
  // 冗余，同node
  description Json?

  // 配置
  bo Json

  node Node? @relation("AiNode", fields: [id], references: [id], onDelete: Cascade)

  // members UnitMember[] @relation("AiMember")
  // attachmentId String
  // attachment   Attachment @relation(fields: [attachmentId], references: [id])

  // isDeleted Boolean  @default(false)
  createdBy String? // User ID
  updatedBy String? // User ID
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// 大字段文档，独立，可能是Node、可能是Field，像一个附件(Attachment)
model Document {
  id String @id

  templateId String?

  // 可能依附在节点，也可能是一个database field
  relationType DocumentAttachType
  relationId   String?

  // @redundancy
  spaceId String
  // 文档节点
  // node    Node?   @relation(fields: [nodeId], references: [id])
  // nodeId  String? @unique

  // 文档备份
  // backupDoc   Doc?    @relation("BackupDocs", fields: [backupDocId], references: [id])
  // backupDocId String?
  // docs        Doc[]   @relation("BackupDocs")

  // 文档字段
  // field   DatabaseField? @relation(fields: [fieldId], references: [id])
  // fieldId Int?

  // 冗余，同node
  name        Json
  // 冗余，同node
  description Json?

  // 汇总，用于部分预览
  summary String?

  // 转成ProseMirror JSON存放
  json Json?
  html String?

  // data在外部存放，node document强制依赖
  documentDataId String       @unique
  documentData   DocumentData @relation(fields: [documentDataId], references: [id])

  // isDeleted Boolean  @default(false)
  createdBy String? // User ID
  updatedBy String? // User ID
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  node Node? @relation("DocumentNode", fields: [relationId], references: [id], onDelete: Cascade)
  // databaseField Node?  @relation("DatabaseField", fields: [relationId], references: [id], onDelete: Cascade)
  // Node          Node[]

  @@unique([relationId])
  @@index([spaceId])
}

// 存放文档的二进制数据，脱离Document，便于没有节点绑定也可以出实时协同文档，方便独立服务器doc-server的运行
model DocumentData {
  id String @id

  // 文档内容，Y.js的Y.encodeStateAsUpdate，存放PG因为支持1GB，而MongoDB仅仅支持16MB
  data     Bytes?
  document Document?
}

// 附件
model Attachment {
  id String @id

  // 存放的键值位置
  path String

  // MD5
  etag String

  // 大小
  size Int
  // 扩展名
  ext  String

  // 是否关联到Report Template?
  // reportTempalte   ReportTemplate? @relation(fields: [reportTempalteId], references: [id])
  // reportTempalteId String?

  // 附件引用计数，被其它地方引用了多少次，每次+1，删除时-1
  // 当理论上一个附件肯定是大于0被引用的，如果小于等于=0，是不是考虑得清理下空间了
  refCount Int @default(0)

  // 缩略图路径,命名通常是 {module}/{path}_thumbnail.{ext}
  thumbnail String?

  // 预览图路径,命名通常是 {module}/{path}_preview.{ext}
  preview String?

  // isDeleted Boolean  @default(false)
  createdBy String? // User ID
  updatedBy String? // User ID
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  fileNodes FileNode[]

  @@index([etag])
}

// 第三方集成
enum IntegrationRelationType {
  SPACE
  USER
}

model Integration {
  id           String                  @id
  name         Json
  description  Json?
  // 关联类型， 个人集成？空间站集成？
  relationType IntegrationRelationType
  relationId   String
  // 对应字符串，集成类型，如EMAIL, WEBHOOK等等等
  type         String
  // 对应BO
  bo           Json

  verified Boolean @default(false)

  // isDeleted Boolean  @default(false)
  createdBy String? // User ID
  updatedBy String? // User ID
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([relationId, type])
}

enum WebhookRelationType {
  // 数据库
  DATABASE

  // 自动化
  AUTOMATION
}

enum OutgoingWebhookScope {
  SPACE
  SITE_ADMIN
}

// Outgoing Webhook，注册，并向外部发起请求
model OutgoingWebhook {
  id String @id
  // 对应Outgoing Webhook BO
  bo Json

  // 冗余，BO里也有
  eventType   String? // 对应ServerEventTypeSchema
  name        Json
  description Json?
  callbackURL String

  relationType OutgoingWebhookScope
  relationId   String?

  space Space? @relation(fields: [relationId], references: [id])

  // isDeleted Boolean  @default(false)
  createdBy String? // User ID
  updatedBy String? // User ID
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([relationId])
}

enum EmbedObjectType {
  // 嵌入节点, node resource + id
  NODE_RESOURCE

  // 嵌入任务清单
  MISSIONS
  // 嵌入单个mission
  MISSION
  // 嵌入一个Dashboard Widget
  DASHBOARD_WDIGET
}

// 嵌入资源
model EmbedLink {
  id String @id

  userId String
  user   User?  @relation(fields: [userId], references: [id])

  spaceId String
  space   Space? @relation(fields: [spaceId], references: [id])

  // 嵌入的属性
  props Json?

  objectType EmbedObjectType
  objectId   String

  // isDeleted Boolean  @default(false)
  createdBy String? // User ID
  updatedBy String? // User ID
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

enum CoinAccountType {
  SPACE
  USER
}

// Bika Coin Account (Credit)
model CoinAccount {
  id           String          @id
  relationType CoinAccountType
  relationId   String

  // 余额 = credit + currency, 不支持小数点
  balance BigInt @default(0)

  // 积分，不支持小数点
  credit BigInt @default(0)

  // 真金白银
  currency BigInt @default(0)

  transactions CoinTransaction[]

  createdBy String? // User ID
  updatedBy String? // User ID
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([relationType, relationId])
}

enum CoinTransactionType {
  // 赚取
  EARN
  // 兑换
  REDEEM
}

enum CoinType {
  // 积分
  CREDIT
  // 真金白银
  CURRENCY

  // 虚拟积分
  VIRTUAL
}

model CoinTransaction {
  id String @id

  accountId String

  account CoinAccount? @relation(fields: [accountId], references: [id])

  // Bika Coin兑美元，1比100
  amount BigInt

  // 交易类型
  type CoinTransactionType

  // 金币类型
  coinType CoinType

  // JSON，记录一些参数和状态用于
  reason Json?

  // 部分情况加描述
  description String?

  createdBy String? // User ID
  createdAt DateTime @default(now())

  @@index([accountId])
}

enum ShortURLRelationType {
  NODE_RESOURCE
}

// 短链接，通常可以给Node节点外部分享，需要手工创建，按一下再创建
model ShortURL {
  id String @id

  relationType ShortURLRelationType
  relationId   String

  disabled Boolean @default(false)

  createdBy String? // User ID
  updatedBy String? // User ID
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([relationType, relationId])
}

enum TrashType {
  RECORD
  TRIGGER
  ACTION
  NODE_RESOURCE
}

model Trash {
  id           String            @id
  spaceId      String
  trashType    TrashType
  resourceType NodeResourceType?
  name         Json?
  path         Json?
  unitId       String?

  createdBy String? // User ID
  updatedBy String? // User ID
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user User? @relation(fields: [createdBy], references: [id])

  @@index([unitId])
  @@index([spaceId, trashType, resourceType])
}

// model FeatureAbility {
//   id          String @id
//   featureType String
//   key         String
//   label       Json
//   description Json
//   config      Json // Ability Config

//   createdBy String? // User ID
//   updatedBy String? // User ID
//   createdAt DateTime @default(now())
//   updatedAt DateTime @updatedAt

//   @@index([key])
// }

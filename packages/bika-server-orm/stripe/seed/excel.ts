import * as fs from 'fs';
import path from 'path';
import Excel from 'exceljs';
import {
  BillingPrice,
  StripeUsageType,
  BillingSKUConfig,
  BillingPlatformSchema,
  BillingSKUType,
  BillingSpecSchema,
  SpacePlanTypeSchema,
  BillingPlanFeatureConfig,
  AIModelPrice,
} from '@bika/types/pricing/bo';
import assert from 'assert';
import { getAIModelPricesConfigs } from '@bika/contents/config/server/pricing/ai/ai-models-price';

/**
 * Sku Excel to JSON
 */
async function readAIModelExcelToJson() {
  const workbook = new Excel.Workbook();
  await workbook.xlsx.readFile(
    path.join(__dirname, '../../../../contents/config/server/pricing/ai/ai-models-price.xlsx'),
  );
  const outputPath = path.join(__dirname, '../../../../contents/config/server/pricing/ai/ai-models-price.json');

  console.log(`write to: ${outputPath}`);

  const worksheet = workbook.getWorksheet('ai-models'); // Replace 'Sheet1' with the actual sheet name
  const aiModelPrices: Record<string, AIModelPrice> = {};

  worksheet!.eachRow({ includeEmpty: true }, (row, rowNumber) => {
    if (rowNumber === 1 || rowNumber === 2) {
      // Skip the header row
      return;
    }

    const aiModelName = row.getCell(1).text as string;
    const inputCredit = Number(row.getCell(7).text);
    const outputCredit = Number(row.getCell(8).text);

    if (aiModelPrices[aiModelName] !== undefined) {
      throw new Error(`Duplicate AI model name found: ${aiModelName}`);
    }

    aiModelPrices[aiModelName] = {
      inputCredit,
      outputCredit,
    };
  });

  const jsonOutputString = JSON.stringify(aiModelPrices, null, 2);
  fs.writeFileSync(outputPath, jsonOutputString); // Replace 'output.json' with the desired output file name

  const configs = getAIModelPricesConfigs();
  assert(configs);
}
/**
 * Sku Excel to JSON
 */
async function readSkuExcelToJson() {
  const workbook = new Excel.Workbook();
  await workbook.xlsx.readFile(path.join(__dirname, '../../../../contents/config/server/pricing/sku/sku.xlsx'));
  const outputPath = path.join(__dirname, '../../../../contents/config/server/pricing/sku/sku.json');

  console.log(`write to: ${outputPath}`);

  const worksheet = workbook.getWorksheet('sku'); // Replace 'Sheet1' with the actual sheet name

  // const allPlans: {[id: string]: BillingPlanConfig} = {};
  const allSkus: { [skuId: string]: BillingSKUConfig } = {};

  worksheet!.eachRow({ includeEmpty: true }, (row, rowNumber) => {
    if (rowNumber === 1 || rowNumber === 2) {
      // Skip the header row
      return;
    }

    const skuType = row.getCell(1).text as BillingSKUType;
    const spec = row.getCell(2).text; // 规格，FREE、PLUS-XXX等，去掉-，通常是plan

    const interval = row.getCell(3).text as 'month' | 'year';
    const skuVersion = Number(row.getCell(4).text);
    const platform = BillingPlatformSchema.parse(row.getCell(5).text);
    const usageMeter = row.getCell(6).text as StripeUsageType;

    const name = row.getCell(7).text;
    const description = row.getCell(8).text;
    // const comment = row.getCell(9).text;
    const included = Number(row.getCell(10).text);
    const billingUnit = Number(row.getCell(11).text);

    const skuId = `${skuVersion}-${skuType}-${spec}-${interval}-${platform}-${usageMeter}`
      .toLowerCase()
      .replaceAll('_', '-');

    const usd = Math.round(Number(row.getCell(12).text) * 100) || 0;
    const cny = parseFloat(row.getCell(13).text) * 100 || 0;
    const twd = parseFloat(row.getCell(14).text) * 100 || 0;
    const jpy = parseFloat(row.getCell(15).text) * 100 || 0;
    const hkd = parseFloat(row.getCell(16).text) * 100 || 0;
    const mop = parseFloat(row.getCell(17).text) * 100 || 0;
    const sgd = parseFloat(row.getCell(18).text) * 100 || 0;
    const cad = parseFloat(row.getCell(19).text) * 100 || 0;
    const eur = Math.round(parseFloat(row.getCell(20).text) * 100) || 0;
    const aud = parseFloat(row.getCell(21).text) * 100 || 0;
    const krw = parseFloat(row.getCell(22).text) * 100 || 0;

    const thePrice: BillingPrice = {
      USD: usd,
      CNY: cny,
      TWD: twd,
      JPY: jpy,
      HKD: hkd,
      MOP: mop,
      SGD: sgd,
      CAD: cad,
      EUR: eur,
      AUD: aud,
      KRW: krw,
    };
    // if (usageMeter === 'seat') {
    //   thePlan.seatPrice[interval] = thePrice;
    // } else {
    //   thePlan.usagesPrice[usageMeter] = {
    //     included,
    //     price: thePrice,
    //   };
    // }

    // let plan :SpacePlanType;
    // if (skuType === 'space-plan') {
    // }
    let thePlan: BillingSKUConfig | undefined = allSkus[skuType];
    if (!thePlan) {
      if (skuType === 'SPACE_PLAN') {
        const newSpacePlan: BillingSKUConfig = {
          id: skuId,
          version: skuVersion,
          platform,
          skuType: 'SPACE_PLAN',
          spec: BillingSpecSchema.parse(spec),
          plan: SpacePlanTypeSchema.parse(spec.split('_')[0]),
          interval,
          name,
          meter: usageMeter,
          description,
          prices: thePrice,
          included,
          billingUnit,
        };
        thePlan = newSpacePlan;
      } else if (skuType === 'PAY_AS_YOU_GO') {
        const newPayAsYouGoPlan: BillingSKUConfig = {
          id: skuId,
          version: skuVersion,
          platform,
          skuType: 'PAY_AS_YOU_GO', // 'pay-as-you-go
          // spec,
          plan: SpacePlanTypeSchema.parse(spec.split('-')[0]),
          interval: 'month', // 按量付费锁定按月
          name,
          meter: usageMeter,
          description,
          prices: thePrice,
          included,
          billingUnit,
        };
        thePlan = newPayAsYouGoPlan;
      } else if (skuType === 'COIN_CURRENCY') {
        const newPayAsYouGoPlan: BillingSKUConfig = {
          id: skuId,
          version: skuVersion,
          platform,
          skuType: 'COIN_CURRENCY', // 'pay-as-you-go
          // spec,
          amount: Number(spec),
          interval: 'once', // 按量付费锁定按月
          name,
          meter: usageMeter,
          description,
          prices: thePrice,
          included,
          billingUnit,
        };
        thePlan = newPayAsYouGoPlan;
      } else {
        throw new Error(`Unknown skuType: ${skuType}`);
      }
    }

    allSkus[skuId] = thePlan;
  });

  const jsonOutputString = JSON.stringify(allSkus, null, 2);
  fs.writeFileSync(outputPath, jsonOutputString); // Replace 'output.json' with the desired output file name
}

/**
 * Features Excel to JSON
 */
async function readFeatureExcelToJson() {
  const workbook = new Excel.Workbook();
  await workbook.xlsx.readFile(path.join(__dirname, '../../../../contents/config/server/pricing/feature/feature.xlsx'));
  const outputPath = path.join(__dirname, '../../../../contents/config/server/pricing/feature/feature.json');

  console.log(`write to: ${outputPath}`);

  const worksheet = workbook.getWorksheet('feature'); // Replace 'Sheet1' with the actual sheet name

  const allSpecs: { [spec: string]: BillingPlanFeatureConfig } = {};
  worksheet!.eachRow({ includeEmpty: true }, (row, rowNumber) => {
    if (rowNumber === 1 || rowNumber === 2 || rowNumber === 3) {
      // Skip the header row
      return;
    }
    function toFeatureBoolean(value: string) {
      if (value === '0') {
        return 'NO';
      }
      if (value === '1') {
        return 'YES';
      }
      return 'COMING_SOON';
    }

    const spec = row.getCell(1).text;
    const seats = Number(row.getCell(2).text);
    const guests = Number(row.getCell(3).text);
    const recordsPerSpace = Number(row.getCell(4).text);
    const recordsPerDatabase = Number(row.getCell(5).text);
    const storage = Number(row.getCell(6).text);
    const resources = Number(row.getCell(7).text);
    const browserNotifications = toFeatureBoolean(row.getCell(8).text);
    const mobileNotifications = toFeatureBoolean(row.getCell(9).text);
    const missions = Number(row.getCell(10).text);
    const reports = Number(row.getCell(11).text);
    const sms = Number(row.getCell(12).text);
    const managedEmails = Number(row.getCell(13).text);
    const smtpEmails = Number(row.getCell(14).text);
    const automationRuns = Number(row.getCell(15).text);
    const automationRunsHistory = Number(row.getCell(16).text);
    const automationIntegrations = toFeatureBoolean(row.getCell(17).text);
    const advancedAutomationIntegrations = toFeatureBoolean(row.getCell(18).text);

    const creditsPerSeat = Number(row.getCell(19).text);
    // const advancedAI = Number(row.getCell(20).text);
    const bringYourOwnAIKey = toFeatureBoolean(row.getCell(21).text);
    const resourcesPermission = Number(row.getCell(22).text);
    const publishShare = toFeatureBoolean(row.getCell(23).text);
    const viewResource = toFeatureBoolean(row.getCell(24).text); // 是否有view resource
    const removeLogos = toFeatureBoolean(row.getCell(25).text); // 是否有view resource

    const spaceIntegrations = Number(row.getCell(26).text);
    const apiRequest = Number(row.getCell(27).text);
    const syncer = toFeatureBoolean(row.getCell(28).text);
    const openapiRate = Number(row.getCell(29).text);

    const subAdmins = toFeatureBoolean(row.getCell(30).text);
    const subDomain = toFeatureBoolean(row.getCell(31).text);
    const customDomain = toFeatureBoolean(row.getCell(32).text);

    const restrictDomains = toFeatureBoolean(row.getCell(33).text);
    const userSessionsLogs = toFeatureBoolean(row.getCell(34).text);
    const spaceSessionsLogs = Number(row.getCell(35).text);
    const spaceAuditLogs = Number(row.getCell(36).text);

    const exportExcel = toFeatureBoolean(row.getCell(37).text);
    const exportBika = toFeatureBoolean(row.getCell(38).text);

    const publishTemplate = toFeatureBoolean(row.getCell(39).text);
    const sellTemplate = toFeatureBoolean(row.getCell(40).text);
    const privateTemplate = toFeatureBoolean(row.getCell(41).text);
    const community = toFeatureBoolean(row.getCell(42).text);
    const helpCenter = toFeatureBoolean(row.getCell(43).text);
    const webinar = toFeatureBoolean(row.getCell(44).text);
    const emailSupport = toFeatureBoolean(row.getCell(45).text);
    const imSupport = toFeatureBoolean(row.getCell(46).text);
    const professionalServices = toFeatureBoolean(row.getCell(47).text);
    const selfHosted = toFeatureBoolean(row.getCell(48).text);

    const featureConfig: BillingPlanFeatureConfig = {
      sms,
      spec,
      seats,
      guests,
      recordsPerSpace,
      recordsPerDatabase,
      storage,
      resources,
      browserNotifications,
      mobileNotifications,
      missions,
      reports,
      managedEmails,
      smtpEmails,
      automationRuns,
      automationRunsHistory,
      automationIntegrations,
      advancedAutomationIntegrations,
      creditsPerSeat,
      // advancedAI,
      bringYourOwnAIKey,
      resourcesPermission,
      publishShare,
      viewResource,
      removeLogos,
      spaceIntegrations,
      apiRequest,
      syncer,
      openapiRate,
      subAdmins,
      subDomain,
      customDomain,

      restrictDomains,
      userSessionsLogs,
      spaceSessionsLogs,
      spaceAuditLogs,

      exportExcel,
      exportBika,

      publishTemplate,
      sellTemplate,
      privateTemplate,
      community,
      helpCenter,
      webinar,
      emailSupport,
      imSupport,
      professionalServices,
      selfHosted,
    };
    allSpecs[spec] = featureConfig;
    // console.log(`row ${rowNumber} spec ${spec}`);
  });

  const jsonOutputString = JSON.stringify(allSpecs, null, 2);
  fs.writeFileSync(outputPath, jsonOutputString); // Replace 'output.json' with the desired output file name
}

readSkuExcelToJson()
  .then(() => {
    console.log('Stripe Sku JSON generated successfully.');
  })
  .catch((error) => {
    console.error('Error generating Sku JSON:', error);
  });

readFeatureExcelToJson()
  .then(() => {
    console.log('Stripe Features JSON generated successfully.');
  })
  .catch((error) => {
    console.error('Error generating Features JSON:', error);
  });

readAIModelExcelToJson()
  .then(() => {
    console.log('AI Models price JSON generated successfully.');
  })
  .catch((error) => {
    console.error('Error generating AI Model JSON:', error);
  });

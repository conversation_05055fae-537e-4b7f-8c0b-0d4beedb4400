import mongoose, { InferSchemaType } from 'mongoose';
import { AICompletionBOSchema, AICompletionKindSchema, AIUsageSchema } from '@bika/types/ai/bo';

/**
 * AI Intent Wizard Dialog DB Chat PO
 */
export const AICompletionSchema = new mongoose.Schema(
  {
    id: { type: String, required: true, index: true },
    createdBy: { type: String, index: true },
    updatedBy: { type: String }, // user id
    // isDeleted: { type: Boolean, require: true, default: false },

    kind: {
      type: String,
      required: true,
      validate: {
        validator(v: object) {
          AICompletionKindSchema.parse(v);
          return true;
        },
        message: (props: mongoose.ValidatorProps) =>
          `${props.value} is not valid AICompletionKindSchema: ${JSON.stringify(props)}`,
      },
    },

    bo: {
      type: Object,
      required: true,
      validate: {
        validator(v: object) {
          AICompletionBOSchema.parse(v);
          return true;
        },
        message: (props: mongoose.ValidatorProps) =>
          `${props.value} is not valid AICompletionSchema: ${JSON.stringify(props)}`,
      },
    },

    usage: {
      type: Array,
      required: true,
      validate: {
        validator: (v: Array<object>) => {
          AIUsageSchema.array().parse(v);
          return true;
        },
        message: (props: mongoose.ValidatorProps) =>
          `${props.value} is not valid AICompletionUsageSchema: ${JSON.stringify(props)}`,
      },
    }, // token 消耗, 可能多个模型
  },
  {
    timestamps: true,
  },
);

// 为 intent.nodeId 添加索引
// AICompletionSchema.index({ 'intent.nodeId': 1 });

export type AICompletionModel = InferSchemaType<typeof AICompletionSchema>;

export const AICompletionDAO = mongoose.models.AICompletion || mongoose.model('AICompletion', AICompletionSchema);

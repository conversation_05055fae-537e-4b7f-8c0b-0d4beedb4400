/**
 * MongoDB Mongoose Models for Database
 */

import mongoose, { InferSchemaType } from 'mongoose';
import { AIUsageSchema, AIArtifactStateSchema, AIArtifactErrorSchema } from '@bika/types/ai/bo';

export const AIArtifactSchema = new mongoose.Schema(
  {
    id: { type: String, required: true, index: true },

    messageId: { type: String },

    toolCallId: { type: String },

    state: { type: String, enum: AIArtifactStateSchema.enum, default: 'CREATED' },

    // artifact type
    type: {
      type: String,
      // enum: ['html', 'code', 'text'],
      required: true,
    },

    // prompt log，用什么 prompt 起的
    prompt: { type: mongoose.Schema.Types.Mixed },

    // artifact data
    data: { type: mongoose.Schema.Types.Mixed },

    error: {
      type: Object,
      required: false,
      validate: {
        validator: (v: object) => {
          AIArtifactErrorSchema.parse(v);
          return true;
        },
        message: (props: mongoose.ValidatorProps) =>
          `${props.value} is not valid AIArtifactErrorSchema : ${JSON.stringify(props)}`,
      },
    },

    usage: {
      type: Object,
      required: false,
      validate: {
        validator(v: object) {
          AIUsageSchema.parse(v);
          return true;
        },
        message: (props: mongoose.ValidatorProps) =>
          `${props.value} is not valid AIUsageSchema: ${JSON.stringify(props)}`,
      },
    },

    // 意图识别的状态 // IntentResolutionStatus，独立状态，是因为有时候哪怕参数已经填满了（Resolve），但还需要用户confirm
    // intentResolutionStatus: { type: String },

    // 标题和描述，由 AI 生成，用于历史回放
    // title: { type: String },
    // description: { type: String },

    createdBy: { type: String, index: true },
    updatedBy: { type: String }, // user id
  },
  {
    timestamps: true,
  },
);

export type AIArtifactModel = InferSchemaType<typeof AIArtifactSchema>;

export const AIArtifactDAO = mongoose.models.AIArtifact || mongoose.model('AIArtifact', AIArtifactSchema);
